{"name": "jettong-ng", "version": "1.0.0", "description": "jettong-ng-web", "author": "jettong-ng", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@antv/g6": "^4.8.24", "@aomao/engine": "^2.10.26", "@element-plus/icons-vue": "^2.3.1", "@vue/compat": "^3.5.17", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.2", "bpmn-js": "^11.5.0", "classnames": "^2.3.2", "codemirror": "^5.65.16", "codemirror-minimap": "^1.0.6", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.8.8", "fast-glob": "^3.3.3", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mavon-editor": "^3.0.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "qs": "^6.11.2", "snowflake-id": "^1.1.0", "spark-md5": "^3.0.2", "store": "^2.0.12", "swiper": "^8.4.7", "throttle-debounce": "^5.0.0", "tinycolor2": "^1.6.0", "vue": "^3.4.21", "vue-clipboard2": "^0.3.3", "vue-cropper": "^0.5.8", "vue-float-action-button": "^0.7.9", "vue-fullscreen": "^2.6.1", "vue-grid-layout": "^2.3.12", "vue-json-pretty": "^2.2.4", "vue-json-viewer": "^3.0.4", "vue-router": "^4.2.5", "vue-simple-uploader": "^0.7.6", "vue-uuid": "^3.0.0", "vue-virtual-scroll-list": "^2.3.4", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "vxe-table": "^4.5.0", "vxe-table-plugin-element": "^3.0.7", "xe-utils": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "mockjs": "^1.1.0", "postcss": "^8.4.32", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "4.5.14", "vite-plugin-svg-icons": "^2.0.1"}}