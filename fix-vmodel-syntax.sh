#!/bin/bash

# 批量修复 v-model:visible 语法
# 在Element Plus中，对话框组件应该使用 v-model 而不是 v-model:visible

echo "开始修复 v-model:visible 语法..."

# 查找所有包含 v-model:visible 的文件
files=$(find src -name "*.vue" | xargs grep -l "v-model:visible" 2>/dev/null)

if [ -z "$files" ]; then
    echo "没有找到包含 v-model:visible 的文件"
    exit 0
fi

echo "找到以下文件包含 v-model:visible 语法:"
echo "$files"
echo ""

# 备份和替换
for file in $files; do
    echo "处理文件: $file"
    
    # 创建备份
    cp "$file" "$file.vmodel_bak"
    
    # 替换 v-model:visible 为 v-model (仅针对 el-dialog 组件)
    sed -i '' 's/v-model:visible="/v-model="/g' "$file"
    
    echo "  ✓ 已处理"
done

echo ""
echo "所有文件处理完成！"
echo "原文件已备份为 .vmodel_bak 文件"
echo ""

# 检查是否还有遗漏的
remaining=$(find src -name "*.vue" | xargs grep -n "v-model:visible" 2>/dev/null | wc -l)
echo "剩余包含 v-model:visible 的行数: $remaining"

if [ "$remaining" -gt 0 ]; then
    echo "仍有文件包含 v-model:visible 语法，需要手动检查:"
    find src -name "*.vue" | xargs grep -n "v-model:visible" 2>/dev/null | head -10
fi

echo ""
echo "注意：在Element Plus中，对话框组件应该使用 v-model 而不是 v-model:visible。"
