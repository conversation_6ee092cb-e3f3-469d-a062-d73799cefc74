@use './variables.scss' as *;
@use './mixin.scss' as *;
@use './transition.scss' as *;
@use './element-ui.scss' as *;
@use './color.scss' as *;

// 颜色和背景色过度效果
:root * {
  transition: color, background-color 100ms linear;
  // transition: opacity .3s,transform .4s,top .4s;
}
body {
  height: 100%;
  font-size: 14px; // 大部分情况都是14px
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: PingFang SC,Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

ul,
li {
  padding: 0;
  margin: 0;
}

ul {
  &:not(.cdx-list) {
    list-style: none
  }
}

li {
  &:not(.cdx-list__item) {
    list-style: none
  }
}
.btnMore {
  min-width: 32px;
  width: 32px;
  margin-left: 12px;
}
// 未用page-wrapper组件的布局
.page-wrapper-content {
  min-height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin});
  -webkit-box-shadow: var(--main-bg-shadow);
  box-shadow: var(--main-bg-shadow);
  background: var(--main-bg-color);
  border-radius: 4px;
  overflow: hidden;
}

// 左右布局
.sectionPageContent {
  display: flex;
  .leftSection {
    margin-right: 10px;
    box-shadow: var(--main-bg-shadow);
    background: var(--main-bg-color);
    height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin});
    overflow-y: auto;
    border-radius: 4px;
  }
  .rightSection {
    border-radius: 4px;
    padding: 16px 16px 10px 16px;
    flex: 1;
    box-shadow: var(--main-bg-shadow);
    background: var(--main-bg-color);
    height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin});
    overflow-y: auto;
  }
}

// 未设置高度的背景
.pageContentNoH {
  box-shadow: var(--main-bg-shadow);
  background: var(--main-bg-color);
  border-radius: 4px;
}

// 注释浏览器默认聚焦的外边框
:focus-visible {
  outline: none;
}

// 切换菜单加载进度条
#nprogress .bar {
  z-index: 2001 !important;
}

//全局-----文字超过一行显示省略号
.text-over {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 全局tab菜单样式
.v-top-action {
  text-align: right;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: var(--main-bg-color);
}

// 表格样式重置
.vone-table {
  font-size: 14px;
  .el-table__header {
    height: 36px;
  }
  tr.el-table__row {
    height: 36px;
  }

  .el-table__cell {
    .el-dropdown {
      font-size: 12px;
    }
  }
  .el-loading-spinner {
    margin-top: unset;
  }
  .el-table__cell:last-child {
    border-right: unset;
  }
  a {
    color: var(--main-theme-color);
  }
}
// vuex 表格
.vone-vxe-table {
  font-family: PingFang SC,Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif;
  // 44px-分页 48px-筛选 36px-表格头
  .vxe-table--body-wrapper {
    height: calc(100vh - #{$main-margin} - #{$main-margin} - #{$main-padding} - #{$main-padding} - #{$nav-top-height} - 44px - 48px - 36px);
    overflow-y: auto;
  }
  .vxe-header--column:not(.col--ellipsis),
  .vxe-body--column:not(.col--ellipsis),
  .vxe-footer--column:not(.col--ellipsis) {
    padding: 7px 0;
  }
  .vxe-header--column.col--ellipsis,
  .vxe-body--column.col--ellipsis,
  .vxe-footer--column.col--ellipsis,
  .vxe-editable .vxe-body--column {
    height: 36px;
  }
  
  thead {
    background-color: var(--content-bg-color);
  }
  .vxe-table--header-wrapper {
    background-color: var(--content-bg-color) !important;
  }
  .vxe-header--column {
    font-weight: normal;
    color: var(--main-font-color)
  }
  .vxe-body--row.row--hover {
    background-color: var(--content-bg-hover-color);
    .el-select {
      input {
        background-color: var(--content-bg-hover-color);
      }
    }
  }
  .vxe-body--column.col--edit {
    cursor: pointer;
  }
  td {
    color: var(--main-font-color)
  }

  .vxe-cell--checkbox .vxe-checkbox--icon {
    font-size: unset;
    color: var(--input-border-color);
    font-size: 14px;
    font-weight: 600;
    background-color: var(--main-bg-color);
    border-radius: 4px;
  }
  .is--checked.vxe-cell--checkbox .vxe-checkbox--icon {
    color: var(--main-theme-color)
  }

  .is--checked.vxe-cell--checkbox .vxe-checkbox--icon,
  .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon {
    color: var(--main-theme-color)
  }
  .vxe-cell--checkbox:hover {
    .vxe-checkbox--icon {
      color: var(--input-border-color);
    }
  }
  .vxe-cell--checkbox .vxe-checkbox--icon:hover {
    color: var(--input-border-color) !important
  }

  .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:hover {
    color: var(--main-theme-color) !important
  }
  .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:hover {
    color: var(--main-theme-color) !important
  }
  .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon {
    background-color: var(--content-bg-disabled-color);
  }
  .el-button {
    min-width: 16px;
    padding: 0px;
  }
  .el-button--text.el-button.is-disabled,
  .el-button--default {
    border: unset;
    border: 1px solid transparent;
  }
  a {
    color: var(--main-theme-color)
  }

  .vxe-tree-cell {
  
    .name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
  .vxe-cell--sort {
    .vxe-sort--desc-btn {
      bottom: -1.7px;
     }

    font-size: 10px;
  }
  .vxe-tree--btn-wrapper {
    display: flex;
  }
  .vxe-tree-cell {
    a {
      display:flex;
      align-items: center;
      .iconfont {
        display:flex;
        margin-right: 4px;
      }
    }
  }
  .custom-title-style {
    .c--ellipsis {
      padding-right: 50px;
    }
    .main {
      position: relative;
    }
    .vxe-tree-cell, .vxe-cell {
      .custom-title-style-icon {
        margin-right: 4px;
      }
      .custom-title-style-text {
        color: var(--main-theme-color);
        cursor: pointer;
      }
      .custom-title-style-copy {
        display: none;
      }
    }
    &:hover{
      .custom-title-style-copy {
        display: block;
      }
    }
  }
  
  .col--checkbox {
    .vxe-cell {
      overflow: inherit!important;
    }
  }
  .el-input__prefix {
      left:0px
    }
    .vone-avatar {
      .el-input--prefix .el-input__inner {
        padding-left: 30px;
      }
    }
   
}
.nobg-table {
  .vxe-table--header-wrapper { 
    background-color: #Fff!important;
  }
  .vxe-header--column {
    background-image: linear-gradient(#fff, #fff);
    color: var(--font-second-color);
  }
  thead {
    background: #fff;
  }
  .vxe-resizable {
    display: none;
  }
  .vxe-table--border-line {
    border: none;
  }
  .vxe-table--header-wrapper .vxe-table--header-border-line  {
    border: none;
    border-bottom: 1px solid #fff;
    
  }
}
.voneNobg-table {
  th.el-table__cell.is-leaf {
    background-color: unset;
    color: var(--font-second-color);
  }
  .el-table--border {
    border: none;
  }
  .el-table--border .el-table__cell {
    border-right: none;
  }
}
.bpmn-reset-table {
  .el-table__header {
    height: 36px;
  }
  tr.el-table__row {
    height: 36px;
  }
}
.operation-icon {

  // 表格中的按钮样式重置
  .el-button {
    padding: 0px;
    height: unset;
    line-height: unset;
    min-width: unset;
    font-size: 16px;
  }

  .el-button.is-disabled {
    background-color: unset;
    border: unset;
  }
}

// 树形下拉
.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  padding: 0 5px 0 0;
}

.vue-treeselect--searchable .vue-treeselect__input-container {
  padding: 0px;
}

.vue-treeselect--open.vue-treeselect--open-below .vue-treeselect__control,
.vue-treeselect--focused:not(.vue-treeselect--open) .vue-treeselect__control {
  border-radius: 6px;
}

.vue-treeselect--open-below:not(.vue-treeselect--append-to-body) .vue-treeselect__menu-container {
  top: 44px;
}

.vue-treeselect__menu {
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.vue-treeselect__list {
  padding: 8px 0px;
}

.vue-treeselect__option {
  height: 34px;
  line-height: 34px;
  margin: 0px;
}

.vue-treeselect__control {
  height: 32px;
  line-height: 32px;
  border-radius: 6px;
  padding: 0px 12px;
  position: relative
}

.vue-treeselect__x-container {
  display: inline-block;
  position: absolute;
  border-radius: 50%;
  right: 35px;
  top: calc(50% - 10px);
  line-height: 19px;
  height: 20px;
}


.vone-form-detail {
  .el-col {
    line-height: 34px;
    display: flex;
    align-items: center;

    .form-title {
      display: inline-block;
      width: 90px;
      min-width: 90px;
      text-align: right;
      margin-right: 16px;
    }
  }
}

// 横向form样式
.form-inline {
  .el-form-item__label {
    line-height: 32px;
    padding-bottom:0px
  }
}
// tab线 样式
.vone-tab-line {
  .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs__item.is-top:nth-child(2),
  .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs__item.is-top:nth-child(2) {
    padding-left: 12px;
    margin-left: 16px;
  }
  .el-tabs__item.is-top:last-child {
    padding-right: 12px;
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: var(--solid-border-color);
  }
  .el-tabs__item {
    padding: 0px 12px;
    color: var(--font-second-color);
    height: 48px;
    line-height: 48px;
  }
  .el-tabs__item.is-active {
    color: var(--main-theme-color);
    border-bottom: 2px solid var(--main-theme-color);
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__header {
    margin: 0px
  }
}
.mintabline {
  // margin: 8px 16px;
  .el-tabs__item {
    height: 36px;
    line-height: 36px;
  }
  .el-tabs__item.is-top:nth-child(2), .el-tabs__item.is-top:nth-child(2) {
    margin-left: 0px;
  }
}
// tab卡片样式重置
.vone-tabs {
  .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs__item.is-top:nth-child(2) {
    padding-left: 8px;
    margin-left: 0px;
  }
  .el-tabs__item.is-top:last-child {
    padding-right: 8px;
  }
  .el-tabs__nav {
    height: 32px;
    background: var(--content-bg-hover-color);
    border-radius: 4px;
    padding: 3px 4px;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    height: 26px;
    line-height: 26px;
    padding: 0px 8px;
    color: var(--font-second-color)
  }
  .el-tabs--top .el-tabs__item.is-top:last-child {
    padding-right: 8px;
  }
  .el-tabs__item.is-active {
    background: var(--main-bg-color);
    border-radius: 2px;
    color: var(--main-theme-color);
    border-bottom:unset
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__header {
    margin: 0px;
  }
  .el-tabs__content {
    padding: 0px!important;
  }
}

.flowTab.el-tabs--card>.el-tabs__header {
  margin: 0 -16px 12px;
  padding: 0 16px;
  border-bottom: 1px solid var(--solid-border-color);
  .el-tabs__nav {
    border: none;
  }

  .el-tabs__item {
    margin-left: 4px;
    padding: 0 10px;
    height: 32px;
    line-height: 32px;
    font-weight: 400;
    color: var(--font-second-color);
    background-color: var(--content-bg-hover-color);
    border: 1px solid var(--solid-border-color);

    &.is-active {
      color: var(--main-theme-color);
      background: #fff;
      border-bottom: none;
      font-weight: 500;
    }

    &:first-child {
      margin-left: 0;
      border-left: 1px solid var(--solid-border-color);
    }

    &:nth-child(2),
    &:last-child {
      padding: 0 8px;
    }
  }
}

// 内容区域
.mainApp {
  height: calc(100vh - #{$noHasHeader});
  overflow-y: auto;
}

.search-input {
  width: 200px;
  padding-right: 10px;
}

// 卡片统一样式

.vone-card {
  .el-loading-mask {
    background: none;
  }

  .el-card__body {
    padding: 16px
  }

  .description {
    font-size: 12px
  }

  .vone-card__title {
    .title {

      // 一级标题
      .title1 {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .title2 {
      // 副标题
      font-size: 14px;
      margin-top: 6px;
    }
  }
}

// 折叠面板

.vone-collapse {
  .el-collapse-item__header {
    font-size: 16px;
    border-radius: 6px;
    border-bottom: none;
    height: 56px;
    line-height: 56px;
    padding: 0px 16px 0px 0px;
    position: relative
  }

  .el-collapse-item__header:before {
    content: "";
    display: block;
    width: 3px;
    height: 16px;
    margin-right: 13px;
  }

  .el-icon-arrow-right {
    display: none;
  }

  .el-collapse-item__header:after {
    font-family: "iconfont";
    content: '\e6ca';
    position: absolute;
    right: 16px;
    font-size: 16px !important;
    font-size: 14px;
  }

  .el-collapse-item__header.is-active {
    border-radius: 6px 6px 0px 0px;
  }

  .el-collapse-item__header.is-active:after {
    content: '\e6c9';
  }

  .el-collapse-item__wrap {
    border-bottom: none;
    border-radius: 0px 0px 6px 6px;
  }

  .el-collapse-item__content {
    padding: 10px 20px
  }

  .el-collapse-item {
    border-radius: 6px;
  }

  .el-icon-arrow-right:before {
    content: '\e790';
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(180deg);
  }
}

.vone-L-title {
  border-left: 4px solid var(--main-theme-color);
  font-weight: 600;
  font-size: 16px;
  padding-left: 12px;
  margin: 16px 0px 16px -16px;
}

// svg动画效果
#filter0_d {
  opacity: 1;
  animation: mymove1 1s infinite 0s;
}

#filter1_d {
  opacity: 0.2;
  animation: mymove2 1s infinite 0s;
}

#filter2_d {
  opacity: 0.2;
  animation: mymove3 1s infinite 0s;
}

@keyframes mymove1 {
  0% {
    opacity: 1.0;
  }

  25% {
    opacity: 1.0;
  }

  50% {
    opacity: 1.0;
  }

  75% {
    opacity: 1.0;
  }

  100% {
    opacity: 1.0;
  }
}

@keyframes mymove2 {
  0% {
    opacity: 0.2;
  }

  25% {
    opacity: 0.2;
  }

  50% {
    opacity: 1.0;
  }

  75% {
    opacity: 1.0;
  }

  100% {
    opacity: 0.2;
  }
}

@keyframes mymove3 {
  0% {
    opacity: 0.2;
  }

  25% {
    opacity: 0.2;
  }

  50% {
    opacity: 0.2;
  }

  75% {
    opacity: 1.0;
  }

  100% {
    opacity: 0.2;
  }
}


/**修改全局的滚动条*/
/**滚动条的宽度*/
::-webkit-scrollbar {
  width: 16px;
  height: 16px;
  background: #fff;
  &:vertical:hover {
    border-left: 1px solid #F2F3F5;
    border-right: 1px solid #F2F3F5;
  }
  &:horizontal:hover {
    border-top: 1px solid #F2F3F5;
    border-bottom: 1px solid #F2F3F5;
  }
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  border: 4px solid transparent;
  background-color: #DFE1E5;
  background-clip: content-Box;
  border-radius: 8px;
  &:hover {
    background-color: #B2B6BF;
  }
}

.ml-2 {
  margin-left: 2px;
}

.ml-16 {
  //列表右上角操作按钮距离其它按钮的margin
  margin-left: 16px;
}

// icon图标svg样式
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.w-e-text {
  a {
    color: var(--main-theme-color, #3e7bfa);
    text-decoration: underline;
  }
}

// 测试中拖拽相关样式
// 拖拽进入节点样式
.dragg2Tree {
  border: 1px solid var(--main-theme-color, #3e7bfa);
  border-radius: 4px;
}

// 拖拽中的样式
.draggingRow {
  opacity: 0.1;
  border: 1px solid #f5f7fa;
  z-index: 1000;
}

// 拖拽多选的样式
.selectedDrag {
  background-color: #f5f7fa;
  border: 1px solid red !important;
}

// 点击选中左侧拖拽样式
.handleDrag {
  position: absolute;
  left: 2px;
  top: 50%;
  font-size: 14px;
  transform: translateY(-7px);
  color: var(--placeholder-color);
  cursor: grab;

  &:hover {
    color: var(--main-theme-color, #3e7bfa);
  }
}

// end
// 自定义的tag样式，主要用在平台配置，配置项，状态
.tagCustom {
  text-align: center;
  border-radius: 2px;
  padding: 0 4px;
  min-width: 80px;
  display: inline-flex;
  align-items: center;
  justify-content: space-around;
  font-size: 12px;
  font-weight: 500;
}

// 没有权限的tab页，用于代码库，分支/合并/成员
.noPermission {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 286px);
}

// 没有权限的a标签，span标签
.noPermissionBtn {
  cursor: not-allowed !important;
}

// 测试管理树相关样式
.rootNode {
  color: var(--main-font-color);
  font-weight: 500;
}

//  快速新增按钮样式
.subBtton {
  padding: 0;
  min-width: 32px;
}

// 操作下拉菜单样式
.optionBtn {
  span {
    display: flex;
    align-items: center;
  }
}

// tab页保存按钮居中底部展示
.tabFooter {
  position: fixed;
  bottom: 2%;
  left: 50%;
  // overflow: hidden;
}

.mt-3 {
  margin-top: 15px;
}

// 用例文本样式
.caseText {
  &:hover {
    background-color: var(--hover-bg-color);
  }

  // 选中的用例文本样式
  &.selected {
    background-color: var(--hover-bg-color);
  }
}

// 工作项编辑弹窗的^上下图标样式
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;

  &:hover {
    font-weight: bold;
  }
}

// mini按钮样式
.miniBtn {
  border-radius: 3px;
  height: 24px;
  line-height: 23px;
  min-width: 48px;
  font-size: 12px;
  padding: 0px 8px;
}
// vxe-table loading动画
.el-icon-vone_loading {
  animation: rotating 2s linear infinite;
}


// 表格空数据样式
.empty_wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 10px 0;

  .empty_pic {
    width: 120px;
    height: 96px;
  }

  .empty_desc {
    line-height: 22px;
    color: var(--font-second-color);
  }
}

// 流水线步骤条
.vone-step-box {
  background: #F3F7FD;

  .steps {
    border: 1px solid var(--solid-border-color) !important;
    background: var(--main-bg-color);
  }

  .childSteps {
    border: 1px solid var(--solid-border-color);
    background: var(--main-bg-color);
  }
  .iconfont {
    color: var(--font-disabled-color);
  }
  .name {
    color: var(--font-main-color);
  }
  .time {
    color: var(--font-second-color);
  }

}
.vone-pipeline-steps {
  .el-step__line-inner {
    border-color: var(--solid-border-color);
  }
  .el-step .el-step__title {
    color: var(--font-second-color);
  }
}
.vone-version-step {
  .el-step__line {
    background-color: var(--content-bg-color);
  }
}

// 开关
.openSwitch {
  .el-switch__label * {
    font-size: 12px;
  }
  .el-switch__label {
    line-height: 18px;
    min-width: 54px;
    position: absolute;
    display: none;
    color: #fff;
  }
  .el-switch__label--left {
    z-index: 9;
    left: 18px;
  }
  .el-switch__label--right {
    z-index: 9;
    left: -5px;
  }
  .el-switch__label.is-active {
    display: block;
  }
  .el-switch__core {
    min-width: 54px;
    border-color: var(--font-disabled-color);
    background-color: var(--font-disabled-color);
  }
  &.is-checked .el-switch__core {
    border-color: var(--Green--10);
    background-color: var(--Green--10);
  }
}

.custom-tree-icon {
  .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  // 未展开
  .el-icon-caret-right:before {
    font-size: 16px;
    content: "\e80e" !important;
    font-family: "iconfont" !important;
  }
  // 展开
  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    font-size: 16px;
    content: "\e80d" !important;
    font-family: "iconfont" !important;
  }
  // 没有子节点
  // .el-tree /deep/.el-tree-node__expand-icon.is-leaf::before
  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
    cursor: default;
  }
}

// 左侧小卡片
.left-small-card {
  .el-card__body {
    padding: 12px;
  }
  height: 76px;
  border: 1px solid var(--solid-border-color);
  border-radius: 4px;
  .small-card-title {
    display: flex;
    height: 24px;
    align-items: center;
    color: var(--font-main-color);
    .small-card-title-left {
      flex: 1;
      display: flex;
      align-items: center;
      .svg-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        .icon {
          font-size: 24px
        }
      }
      span {
        width: 126px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .small-card-desc {
    font-size:12px;
    margin-top: 8px;
    height: 20px;
    width: 100%;
    font-weight: 400;
    color: var(--font-second-color);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.left-small-card.is-hover-shadow:not(.is-active):hover {
  box-shadow: var(--dialog-shadow);
  background-color: var(--content-bg-hover-color);
}
.left-small-card.is-active {
  border: 2px solid var(--main-theme-color);
}

.vone-detail-title {
  height: 32px;
  line-height: 22px;
  font-weight: 500;
  display: flex;
  align-items: center;
  .svg-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    .icon {
      font-size: 24px;
    }
  }
}
.vone-detail-row {
  .el-col {
    height: 22px;
    display: flex;
    align-items: center;
    margin-top: 8px;
    .form-title {
      min-width: 56px;
      color: var(--font-second-color);
      text-align: right;
      margin-right: 8px;
    }
    .form-content {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding-left: 8px;
    }
  }
}
.el-message {
  transition: opacity .3s, transform .4s, top .4s;
}

.vue-treeselect--single .vue-treeselect__input{
  height: 32rem;
  line-height: 32rem;
  border-radius: 2rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // padding: 0 12rem;
  font-size: 14rem;
}

.color-danger{       //延期图标颜色
  color:#E53535;
}

.vue-treeselect--open-below .vue-treeselect__menu{
  box-shadow: 0 2rem 12rem 0 rgba(0,0,0,.1);
  border: 1rem solid var(--solid-border-color);
  border-radius: 4rem;
  background-color: #fff;
  box-sizing: border-box;
  margin: 5rem 0;
  font-family: '';
}
// 快速新增样式
.dropList {
  min-width: 60px;
  .el-row {
    margin-left:0px!important;
    margin-right:0px!important;
  }
}
.rightSection{
  position: relative;
}
.newItemBtn{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.subBtton{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-right: 1px;
  &:focus{
    background-color: #3E7BFA;
  }
}