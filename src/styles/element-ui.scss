// cover some element-ui styles
// 动画过渡s s
.el-select-dropdown, .el-dropdown-menu {
  transition: opacity .3s,transform .4s,top .4s!important;
}
// el-menu
.el-menu-item {
  padding: 0px;
  margin: 0px 16px;
  &.is-active {
    border-bottom: 2px solid;
    background: none;
  }
  &:focus,
  &:hover {
    background: none;
  }
}

// .el-divider
.el-divider {
  background-color: var(--solid-border-color);
}
// 按钮
.el-button {
  font-size: 14px;
  border-radius: 4px;
  height: 32px;
	line-height: 32px;
  padding: 0px 10px;
  min-width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  & [class*=el-icon-]+span,
  .el-icon--right {
    margin-left: 4px;
  }
  [class*=" el-icon-"],
  [class^=el-icon-] {
    font-size: 16px;
  }
  &+& {
    margin-left: 16px;
  }
  // 默认
  &--default {
			border: 1px solid var(--input-border-color);
			color: var(--font-second-color);
    &:focus {
      border: 1px solid var(--main-10);
			color: var(--main-10);
      background: unset;
    }
    &:hover {
      border: 1px solid var(--main-hover-theme-color);
      color: var(--main-hover-theme-color);
      background: unset;
    }
  }
  // 主要
  &--primary {
    background-color: var(--main-theme-color);
    border-color: var(--main-theme-color);
    color:#fff;
    &:focus {
      background-color: var(--main-10);
      border-color: var(--main-10);
      color:#fff;
    }
    &:hover {
      background-color: var(--main-hover-theme-color);
      border-color: var(--main-hover-theme-color);
      color:#fff;
    }
    &.is-disabled,
    &.is-disabled:active,
    &.is-disabled:focus,
    &.is-disabled:hover {
      border: none;
    }
  }
  //危险
  &--danger {
    background-color: var(--Red);
    border-color: var(--Red);
    color: #fff;

    &:focus,
    &:hover {
      background-color: var(--Red);
      border-color: var(--Red);
      color: #fff;
    }
  }

  // 成功
  &--success {
    background-color: var(--Green);
    border-color: var(--Green);;
    color: #fff;

    &:focus,
    &:hover {
      background-color: var(--Green);;
      border-color: var(--Green);;
      color: #fff;
    }
  }

  // 致灰
  &.is-disabled {
    color: var(--font-disabled-color);
    border: 1px solid var(--input-border-color);
    background-color: var(--content-bg-disabled-color);

    &:hover {
      color: var(--font-disabled-color);
      border: 1px solid var(--input-border-color);
      background-color: var(--content-bg-disabled-color);
    }
  }

  &--text.el-button {
    color: var(--main-theme-color);

    &.is-disabled {
      color: var(--input-border-color);
      background: unset;
    }
  }
}

// el-radio-group
.el-radio-group {
  .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background-color:var(--main-theme-color);
    border-color: var(--main-theme-color);;
  }
  .el-radio-button:not(.is-active):hover {
    .el-radio-button__inner {
      color: var(--main-hover-theme-color);
    }
  }
  .el-radio-button__inner {
    border:1px solid var(--input-border-color);
    color: var(--font-main-color);
  }
}

// dialog
.el-dialog {  
  position: absolute;
  margin-top: 0 !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  border-radius: 4px;
  max-height: calc(100vh - 10vh );
  border:1px solid var(--solid-border-color);
  box-shadow: var(--dialog-shadow);
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid var(--solid-border-color);
  }

  .el-dialog__footer {
    padding: 12px 20px;
    border-top: 1px solid var(--solid-border-color);
  }

  .el-dialog__headerbtn {
    top: 15px;
    right: 20px;
  }
  .el-dialog__close {
    color: var(--font-second-color);
  }
  .el-dialog__title {
		color:var(--font-main-color);
    font-size: 16px;
    font-weight: 500;
  }
  .el-dialog__body {
    padding: 16px;
    max-height: calc(100vh - 10vh - 115px);
    overflow-y: auto;
    .el-form-item {
      margin-bottom: 16px;
      .el-form-item__label {
        padding-bottom: 4px;
        // 弹窗中的表单
        line-height: 22px;
      }
    }
    .el-form-item--small[class~="is-required"] {
      margin-bottom: 16px;
    }
  }

}

// confirm
.el-message-box {
  border-radius: 4px;
  box-shadow: var(--dialog-shadow);
}
// confirm删除
.delConfirm {
  padding-bottom: 0px;
  .el-message-box__header {
    padding:20px 0px 0px 20px;
    margin-left: 28px;
    line-height: 20px;
  }
  .el-message-box__message {
    padding:0px 28px ;
    color: var(--font-main-color);
    white-space: normal;
    overflow-wrap: break-word;
  }
  .el-message-box__status {
    position: absolute;
    top: -17px;
    font-size: 20px!important;
  }
  .el-message-box__content {
    padding: 8px 20px 20px 20px;
  }
  .el-message-box__btns {
    padding: 0px 20px 20px 0px;
  }
  .el-message-box__title,.el-message-box__status {
    color: var(--Red);
  }
  .el-button--primary {
    background-color: var(--Red);
    border-color: var(--Red);
    color: #fff;
    &:focus,
    &:hover {
      background-color: var(--Red);
      border-color: var(--Red);
      color: #fff;
    }
  }
  .el-icon-warning:before{
    content: '\e864';
    font-family: 'iconfont';
  }
}

// message
.el-message {
  padding: 8px 16px;
  min-width: 170px;
  transition: opacity .3s,transform .4s,top .4s;
  .el-message__content, .el-message__closeBtn {
    color: var(--font-second-color);
  }
}

// 成功message
.el-message--success {
  background-color: var(--Green--50);
  border-color: var(--Green--30);
  .el-icon-success {
  color: var(--Green);
  }
}
// 错误message
.el-message--error {
  background-color: var(--Red--50);
  border-color: var(--Red--30);
  .el-icon-error {
  color: var(--Red);
  }
}
//警告message
.el-message--warning {
  background-color: var(--Yellow--50);
  border-color: var(--Yellow--30);
  .el-icon-error {
  color: var(--Yellow);
  }
}
// el-table
.el-table {
  .el-table__cell {
    padding: 0px;
  }
  .el-table-column--selection .cell {
    padding: 0px 10px;
  }
  background-color: var(--main-bg-color);
  color: var(--font-main-color);
  tr.el-table__row {
    background-color: var(--main-bg-color);
  }
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid var(--table-border-color);
    color: var(--main-font-color);
  }
  th.el-table__cell.is-leaf {
    font-weight: 500;
    background-color: var(--content-bg-color);
    color: var(--font-main-color);
  }
  &__body {
    tr.hover-row>td.el-table__cell {
      background-color: var(--content-bg-hover-color);
    }
  }
  &--border {
    border: 1px solid var(--table-border-color);
    .el-table__cell {
      border-right: 1px solid var(--table-border-color);
    }
    .is-leaf.el-table__cell:not(:last-child):hover {
      border-right: 1px dashed var(--table-border-color);
    }
  }
  &::before,
  &--border::after,
  &--group::after {
    background-color: var(--content-bg-disabled-color);
  }
  // 表格行hover样式
  &--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: var(--content-bg-hover-color);
  }
  // 表格固定行样式
  &__fixed {
    &::before,
    &-right::before {
      border-bottom: 1px solid var(--solid-border-color);
    }

    &-right-patch {
      background-color: var(--content-bg-disabled-color);
      border-color: var(--content-bg-disabled-color);
    }
  }
  .caret-wrapper {
    height: 20px;
  }
  // 排序箭头样式
  .sort-caret {
    &.ascending {
      top: -2px;
    }
    &.descending {
      bottom: 0;
    }
  }
}
// 分页 el-pagination
.el-pagination {
  font-weight: unset;
  padding: 0px;

  &__total {
    color: var(--font-main-color);
    margin-right: 8px;
  }
  &__jump {
    color: var(--font-second-color);
    margin-left: 8px;
  }
  &__sizes {
    margin: 0px 8px 0px 0px;
  }
  &__editor {
    margin: 0px 8px;
  }
  button:disabled {
    color: var(--font-disabled-color);
    background: none;
  }
  .btn-next,
  .btn-prev {
    color: var(--font-second-color);
    background: none;
  }
  .el-pager {
    li {
      font-size:14px;
      min-width: 28px;
      margin: 0px 4px;
      border-radius: 2px;
      background: none;
      color: var(--font-main-color);
      &:hover,
      &.active {
        color: var(--main-theme-color);
        background-color: var(--main-hover-page-color);
      }
    }
  }

  button,
  span:not([class*=suffix]) {
    min-width: unset;
    font-size: 14px;
  }
  .el-select .el-input{
    margin: 0px 0px 0px 8px;
  }
  .el-select .el-input .el-input__inner {
    border-color: var(--input-border-color);
    color: var(--font-main-color);  
    border-radius: 2px;
    font-size: 14px;
    
  }
}

// 时间选择框弹出框 el-picker-panel
.el-date-range-picker {
  width: 552px;
  .el-date-table {
    margin-top:12px
  }
}
.el-date-picker {
  width: 276px;
  .el-picker-panel__content {
    width: 252px;
    margin: 12px;
  }
}
.el-picker-panel {
  
  th {
    margin-top: 12px;
  }
  .el-picker-panel__icon-btn {
    margin-top: 6px;
  }
  .el-picker-panel__footer {
    border-top: 1px solid var(--solid-border-color);
    padding: 4px 12px;
    .el-button + .el-button {
      margin-left: 12rem;
    }
    .el-picker-panel__link-btn {
      height: 24px;
      min-width: 48px;
      line-height: 18px;
      font-size: 12px;
    }
  }
  .el-date-picker__header, .el-date-range-picker__header {
    margin: 0px;
    padding: 8px;
    border-bottom: 1px solid var(--solid-border-color);
    line-height: 24px;
    height: 40px;
  }
  .el-date-range-picker__header {
    margin: -12px -12px 0px -12px;
  }
  .el-date-picker__time-header {
    border-bottom: none;
  }
  .el-date-picker__header-label,  .el-date-range-picker__header div {
    color: var(--font-main-color);
    font-size:14px
  }
  .el-date-range-picker__content.is-left {
    border-right: 1rem solid var(--solid-border-color);
  }
  .el-date-table td.end-date span, .el-date-table td.start-date span {
    background-color: var(--main-theme-color);
    color: #fff;
  }
  .el-date-table th {
    border-bottom: none;
    padding: 0px;
    width: 36px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
  // 重置文本大小
  .el-date-table .el-date-table__row td {
    font-size: 14px;
  }
  .el-date-table td.in-range div, .el-date-table td.in-range div:hover, .el-date-table.is-week-mode .el-date-table__row.current div, .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: var(--main-hover-page-color);
  }
  .el-date-table td.start-date div {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
    margin-left: 8px;
  }
  .el-date-table td.end-date div {
    border-top-right-radius: 2rem;
    border-bottom-right-radius: 2rem;
    margin-right: 8px;
  }
  .el-date-table td span {
    border-radius: 2px;
  }
  .el-date-table th, .el-picker-panel__icon-btn {
    color: var(--font-second-color);
  }
  .el-picker-panel {
    color: var(--font-main-color);
  }
  .el-date-table td.next-month, .el-date-table td.prev-month {
    color: var(--font-disabled-color);
  }
  .el-date-table td, .el-date-table td div {
    height: 24px;
  }
  .el-date-table td div {
    padding: 0px;
  }
  .el-date-range-picker__content {
    padding: 12px;
  }
  .el-date-range-picker__header div {
    font-size: 14px
  }
  .el-date-table td.current:not(.disabled) span {
    background-color: unset;
    color: var(--font-main-color);
    border: 1px solid var(--main-theme-color);
    line-height: 22px;
  }
  .el-date-table td.today:not(.start-date):not(.end-date) span {
    color:var(--main-theme-color);
  }
  .el-button--default {
      border: 1px solid var(--input-border-color);
      color: var(--font-second-color);
    &:focus {
      border: 1px solid var(--main-10);
      color: var(--main-10);
      background: unset;
    }
    &:hover {
      border: 1px solid var(--main-hover-theme-color);
      color: var(--main-hover-theme-color);
      background: unset;
    }
  }
  .el-time-spinner__item.active:not(.disabled) {
    color: var(--font-main-color);
  }
  .el-time-spinner__item {
    font-size: 14px;
  }
  .el-time-spinner__item,.el-time-panel__btn {
    color: var(--font-second-color);
  }
  .el-time-panel__btn.confirm {
    color: var(--main-theme-color);
  }
}

// dropdown
.el-dropdown-menu {
  padding: 4px 0px;
  border-radius: 2px;
  background-color: var(--main-bg-color);
  box-shadow: var(--dialog-shadow);
  .el-dropdown-menu__item {
    color: var(--font-main-color);
    display: flex;
    align-items: center;
    &:not(.is-disabled):hover {
      color: var(--font-main-color);
      background-color: var(--hover-bg-color);
    }
    &.is-disabled {
      color: var(--font-disabled-color);
      .iconfont {
        color: var(--font-disabled-color);
      }
    }
   
  }
  &--small .el-dropdown-menu__item {
    padding: 0px 12px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    i {
      margin-right: 4px;
    }
    
  }
  &--small .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
    margin: 0 -12px;
  }
  .iconfont {
    color: var(--font-second-color);
    font-size: 16px;
  }
}
// 所有输入框
.vue-treeselect--open.vue-treeselect--open-below .vue-treeselect__control, .vue-treeselect--focused:not(.vue-treeselect--open) .vue-treeselect__control {
  border-radius: 2px!important;
}
.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: var(--main-theme-color);
}
.el-select {
  width: 100%;
}
.el-input__inner, .vue-treeselect__control, .el-textarea__inner {
  border: 1px solid var(--input-border-color);
  color: var(--font-main-color);
}
.vue-treeselect--has-value .vue-treeselect__input {
  vertical-align: middle;
  font-family:PingFang SC,Helvetica Neue,Helvetica,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif
}

.el-select-dropdown__item.selected {
  color: var(--main-theme-color);
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: var(--main-theme-color);
}
.el-select .el-input.is-focus .el-input__inner, .vue-treeselect--open.vue-treeselect--open-below .vue-treeselect__control, .vue-treeselect--focused:not(.vue-treeselect--open) .vue-treeselect__control, .el-input--small .el-input__inner:focus, .el-textarea__inner:focus, .el-cascader .el-input .el-input__inner:focus, .el-cascader .el-input.is-focus .el-input__inner {
  border-color: var(--main-theme-color);
}
.el-input__inner:hover, .el-textarea__inner:hover, .el-select__input:hover, .el-select:hover .el-input__inner {
  border-color: var(--main-theme-color);
}
.vue-treeselect:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) .vue-treeselect__control:hover{
  border-color: var(--main-theme-color);
}
.el-select-dropdown__item.hover, .vue-treeselect__option:hover, .vue-treeselect__option--selected, .vue-treeselect__option--highlight {
  background: var(--content-bg-hover-color);
}
.vue-treeselect__menu{
  box-shadow: var(--dialog-shadow);
  color: var(--font-main-color);
}
.vue-treeselect--open.vue-treeselect--open-below .vue-treeselect__control, .vue-treeselect--focused:not(.vue-treeselect--open) .vue-treeselect__control, .el-input--small .el-input__inner:focus, .el-textarea__inner:focus, .el-cascader .el-input .el-input__inner:focus, .el-cascader .el-input.is-focus .el-input__inner {
  border-color:var(--main-theme-color);
}
.vue-treeselect__x-container:hover {
  background-color: var(--content-bg-hover-color);
  color: var(--font-second-color);
}
.vue-treeselect__option--selected.vue-treeselect__option {
  background-color: var(--content-bg-hover-color);
  color: var(--main-theme-color);
  font-weight: 400;
}
.vue-treeselect__control {
  height: 15px!important;
  line-height: 15px!important;
}
.vue-treeselect--disabled {
  .vue-treeselect__single-value {
    color: var(--font-disabled-color);
  }
}
// .vue-treeselect--disabled .vue-treeselect__control {
//   border: unset;
//   background-color: var(--content-bg-hover-color)!important;
//   .vue-treeselect__single-value {
//     color: var(--font-main-color)!important;
//   }
// }
.el-cascader { // select树
  .el-tag.el-tag--info {
    color: var(--font-main-color);
  }
  .el-tag .el-icon-close {
    background-color: unset;
    color: var(--font-main-color);
  }
  .el-tag .el-icon-close:hover {
    background-color: var(--content-bg-hover-color);
    color: var(--font-main-color);
  }
}
// 输入框搜索文字颜色
.el-select__input, .el-select-dropdown__item {
  color: var(--font-main-color);
}
// placeholder
.el-input__inner::placeholder {
  color: var(--font-disabled-color);
}
.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  border-radius: 2px;
  box-sizing: border-box;
  padding: 0px 12px;
	font-size: 14px;
}
.el-input--prefix .el-input__inner {
  padding-left: 30px;
}
.el-input__suffix {
  // right: 12px;
}
.el-radio {
  color: var(--font-main-color);
  font-weight: normal;
}
.el-radio__input.is-checked+.el-radio__label {
  color: var(--font-main-color);
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--main-theme-color);
  background: var(--main-theme-color);
}
.el-radio__inner:hover {
  border-color: var(--main-theme-color);
}
.el-radio__input.is-checked .el-radio__inner, .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: var(--main-theme-color);
  background: var(--main-theme-color);
}
.el-checkbox__input.is-disabled .el-checkbox__inner, .el-radio__input.is-disabled .el-radio__inner, .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: var(--content-bg-disabled-color);
  border-color: var(--input-border-color)
}
.el-checkbox__input.is-disabled+span.el-checkbox__label,.el-radio__input.is-disabled+span.el-radio__label {
  color: var(--font-disabled-color);
}
.el-input-number {
  .el-input__inner {
    text-align: left;
  }
}
.el-input-number__decrease,
.el-radio-button:first-child .el-radio-button__inner {
  border-radius: 5px 0px 0px 5px;
}
.el-input-number__increase,
.el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0px 5px 5px 0px;
}
.el-input.is-disabled .el-input__inner {
  background-color: var(--content-bg-disabled-color);
  color: var(--font-disabled-color);
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: var(--content-bg-hover-color);
  color: var(--font-second-color);
  border-color: var(--table-border-color);
}
.el-textarea__inner,
.el-popover {
  border-radius: 2px;
  padding: 5px 12px;
}
.el-textarea .el-input__count {
  line-height: initial;
}
.el-popover {
  background: var(--main-bg-color);
  color: var(--font-second-color);
  box-shadow: var(--dialog-shadow);
  &__title {
    color: var(--font-main-color);
  }
  &__reference-wrapper {
    color: var(--font-main-color);
  }
}
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background-color: var(--content-bg-hover-color)
}
.el-cascader-menu {
  color: var(--font-main-color);
  font-weight: 400;
  border-color: var(--solid-border-color)
}
.el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
  color: var(--main-theme-color);
}
// 计数器
.el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled), .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
  border: 1px solid var(--main-theme-color);
}
.el-input-number__decrease:hover:not(.is-disabled), .el-input-number__increase:hover:not(.is-disabled) {
  color: var(--main-theme-color);
}
.el-input-number__decrease {
  border-right: 1px solid var(--table-border-color);
}
.el-input-number__increase {
  border-left:  1px solid var(--table-border-color);
}
.el-input-number__decrease, .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 2px 0 0 2px;
}
.el-input-number__increase, .el-radio-button:last-child .el-radio-button__inner {
  border-radius:  0 2px 2px 0;
}
.el-input-number__decrease, .el-input-number__increase {
  background: var(--main-bg-color);
  color: var(--font-second-color);
}
.el-input-number__decrease.is-disabled, .el-input-number__increase.is-disabled {
  background: var(--content-bg-disabled-color);
  color: var(--font-disabled-color);
}
.el-icon-plus:hover 
// tag
.el-tag {
  font-size: 12px;
  border-radius: 2px;
}
.el-tag.el-tag--small {
  height: 18px;
  line-height: 16px;
  padding: 0px 6px;
  border-radius: 2px!important;
  font-weight: 500;
}
.el-tag--dark {
  color: var(--main-10);
  background-color: var(--main-hover-page-color);
  border-color: var(--main-hover-page-color);
}
.el-tag--dark.el-tag--success {
  color: var(--Green);
  background-color: var(--Green--50);
  border-color: var(--Green--50);
}
.el-tag--dark.el-tag--warning {
  color: var(--Yellow-10);
  background-color: var(--Yellow--50);
  border-color: var(--Yellow--50);
}
.el-tag--dark.el-tag--info {
  color: var(--info-font-color);
  background-color:var(--info-bg-color);
  border-color: var(--info-bg-color);
}
// form
.el-form {
  .el-form-item__label {
    //可输入表单
    line-height: 22px;
    color: var(--main-font-color);
    padding-bottom: 4px;
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
    color: var(--Red);
    line-height: 22px;
    margin-right: 2px;
  }
  .el-row {
    margin-left: -8px!important;
    margin-right: -8px!important;
  }
  .el-col {
    padding-left: 8px!important;
    padding-right: 8px!important;
  }
  //居中显示的label
  .centerLabel {
    display: flex;
    align-items: center;
    .el-form-item__label {
      padding-bottom: 0px!important;
    }
  }
  // // 表单中头像
  // .el-input__prefix {
  //   left: 10px
  // }
  // .el-input--prefix .el-input__inner {
  //   padding-left: 35px
  // }
}
// form表单输入框错误
.el-form-item.is-error .el-input__inner, .el-form-item.is-error .el-input__inner:focus, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner:focus, .el-message-box__input input.invalid, .el-message-box__input input.invalid:focus {
  border-color: var(--Red);
}
.el-form-item__error {
  color: var(--Red);
}


// 上传
.upload {
  .el-button {
    border: none;
    padding: 0px;
    color: var(--main-theme-color);
  }
  .el-upload-list__item {
    margin-top: 4px;
    line-height: 32px;
    .el-icon-upload-success {
      display: none;
    }
  }
  .el-upload-list__item.is-success .el-upload-list__item-name:focus, .el-upload-list__item.is-success .el-upload-list__item-name:hover {
    color: var(--main-theme-color);
    background-color: var(--content-bg-hover-color);
  }
  .el-upload-list__item:first-child {
    margin-top: 4px;
  }
  .el-upload-list__item-status-label, .el-icon-close {
    right: 12px;
  }
  .el-icon-close {
    top:8px
  }
  .el-upload-list__item-name{
    color: var(--font-main-color);
    // padding-left: 12px;
    // i{
    //   color: var(--main-theme-color);
    // }
  }
  .el-icon-upload-success {
    color: var(--Green);
  }
}

//---------------------------分割线-------------------------------
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}



// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.dialogContainer .el-form-item__label {
  line-height: 1.15;
  font-weight: 400;
}

// reset collapse style
.el-collapse-item__ {
  &header {
    padding: 10px 20px;

    &:hover {
      color: var(--sub-font-color);
      background-color: var(--hover-bg-color);
    }
  }

  &content {
    padding: 10px 20px;
  }

  &header,
  &wrap {
    color: var(--main-font-color);
    background-color: var(--main-bg-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
}

// reset loading
// .el-loading-spinner .circular {
//   display: none;
// }

// .el-loading-spinner {
//   background: url(../assets/vone-loading.gif) no-repeat 50%;
//   background-size: 48px 48px;
//   width: 100%;
//   height: 100%;
//   position: relative;
//   top: 0;
// }

// .el-loading-mask {
//   background-color: var(--main-bg-color);
// }



.el-scrollbar__view {
  border-radius: 0px 0px 6px 6px;
}







// 设置弹窗边角颜色
@mixin borderColor($place) {
  &[x-placement^=#{$place}] {
    .popper__arrow {
      border-#{$place}-color: var(--disabled-bg-color);

      &::after {
        border-#{$place}-color: var(--main-bg-color, #fff);
      }
    }
  }
}

// 菜单弹窗
.el-popper {
  border: 1px solid var(--solid-border-color);
  &[x-placement^=bottom] {
    margin-top: 8px;
  }

  @include borderColor(top);
  @include borderColor(bottom);
  @include borderColor(left);
  @include borderColor(right);

}


.el-cascader {
  // select树
  .el-tag.el-tag--info {
    line-height: 22px;
    height: 22px;
    border-radius: 2px;
    border: none;
  }
  .el-tag .el-icon-close {
    font-size: 7px;
    top: 0px;
  }
  &:not(.is-disabled):hover .el-input__inner {
    border-color: var(--input-border-color);
  }
}
.el-cascader-panel.is-bordered {
  border-radius: 2px;
  box-shadow: var(--dialog-shadow)
}

.el-cascader-node {
  padding: 0px 10px 0px;
}

.el-cascader-node__label {
  margin-left: 8px;

}

.el-cascader__dropdown {
  // select 下拉树
  border-radius: 6px;
  .el-scrollbar__view {
    background: unset !important;
  }
}

.el-select-dropdown__wrap {
  .el-scrollbar__view {
    background: unset !important;
  }
}

.el-select-dropdown__list {
  padding: 6px 0px
}

.el-select-dropdown__item {
  padding: 0px 12px
}

.el-radio__inner::after {
  width: 7px;
  height: 7px;
}

.el-checkbox__inner {
  border-radius: 2px;
}

// 时间选择框

// .el-range-editor--small .el-range-input {
//   background: unset;
//   font-size: 12px;
// }




.el-drawer__header {
  padding: 12px 20px;
  margin-bottom: 0px;
  line-height: 24px;
  color: var(--main-font-color);
  border-bottom: 1px solid var(--solid-border-color);
  font-size: 16px;
  font-weight: 500;
}

.el-drawer__close-btn {
  font-size: 16px;
}

//tree
.el-tree {
  background: none;
  color: var(--main-font-color);

  .el-tree-node__content {
    height: 30px;

    &:hover {
      background-color: var(--content-bg-hover-color);
    }
  }
  // .el-tree-node:focus>.el-tree-node__content {
  //   background-color: var(--main--50);
  //   color: var(--main-theme-color);
  // }
  .el-tree-node:focus>.el-tree-node__content,
  .el-tree-node.is-current>.el-tree-node__content {
    background-color: var(--main--50);
      color: var(--main-theme-color);
  }
}

.el-tree-node {
  .el-button+.el-button {
    margin-left: 8px;
  }
}

.el-card {
  border-radius: 6px;
  border: 1px solid var(--solid-border-color);
  &__header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--solid-border-color);
  }
}

.el-collapse {
  border: none;
  margin-bottom: 10px;
}

// 穿梭框
.el-transfer__buttons {
  .el-button {
    display: inline-block !important;
  }
}

.el-transfer-panel .el-transfer-panel__header {
  line-height: 48px;
  height: 48px;
  padding-left: 16px;

  .el-checkbox {
    line-height: 48px;
  }

  .el-checkbox .el-checkbox__label {
    font-size: 16px;

    span {
      right: 16px;
    }
  }
}

.el-transfer-panel__item.el-checkbox .el-checkbox__label {
  font-weight: normal;
}


// el-tooltip
.el-tooltip:focus {
  //切换菜单 退出页面 图标有边框
  outline: none
}
.el-tooltip__popper.is-dark {
  background-color: rgba(48,49,51,0.95);
}
.el-cascader-panel {
  max-width: 400px;
  overflow: hidden;
  overflow-x: auto;
}

.el-tooltip__popper {
  max-width: 500px;
}