<template>
  <div v-if="!item.hidden" style="float: left">
    <!-- 单个子菜单显示 -->
    <div v-if="shouldShowSingleChild">
      <app-link v-if="onlyOneChild.meta" :to="rebasePath">
        <el-menu-item :index="basePath">
          <item :icon="getItemIcon" :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </div>

    <!-- 多个子菜单显示 -->
    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template #title>
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  computed: {
    rebasePath() {
      const params = this.$route.params
      let basePath = this.basePath || ''
      for (const k in params) {
        basePath = basePath.replace(':' + k, params[k])
      }
      return basePath
    },
    shouldShowSingleChild() {
      return this.hasOneShowingChild(this.item.children, this.item) &&
             (!this.onlyOneChild.children || this.onlyOneChild.noShowingChildren) &&
             !this.item.alwaysShow
    },
    getItemIcon() {
      return this.onlyOneChild.meta.icon || (this.item.meta && this.item.meta.icon)
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        // 只显示二级菜单
        parent.alwaysShow = false
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
