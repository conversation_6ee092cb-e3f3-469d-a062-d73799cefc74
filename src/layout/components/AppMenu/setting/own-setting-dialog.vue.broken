<template>
  <el-dialog title="个人设置" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="800px" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <el-tabs v-model="tabActive" tab-position="left" class="vone-tab-left">
      <el-tab-pane label="个人设置" name="ownSetting">

        <el-form ref="personForm" v-loading="formLoading" :model="personForm" :rules="rules" label-position="left">
          <el-row class="accountRow">
            <span class="label">
              登录账号
            </span>
            <span>{{ personForm.account }}</span>
          </el-row>

          <el-form-item label="头像" prop="avatarPath" class="avatarPath">
            <el-popover ref="popoverRef" placement="bottom" trigger="hover">
              <el-row class="popover">
                <el-col v-for="item in avatarList" :key="item.name" :span="4">
                  <div class="userhead item">
                    <a @click="changeIcon(item)">

                      <vone-user-avatar
                        :avatar-path="item.name"
                        :avatar-type="true"
                        :show-name="false"
                        width="45px"
                        height="45px"
                      />
                    </a>
                  </div>
                </el-col>
              </el-row>

              <template #reference>
                <span>
                  <vone-user-avatar
                    :avatar-path="personForm.avatarPath"
                    :avatar-type="true"
                    :show-name="false"
                    width="45px"
                    height="45px"
                  />
                </span>
              </template>
            </el-popover>
          </el-form-item>
          <el-form-item label="用户名称" prop="name">
            <el-input v-model="personForm.name" placeholder="请输入用户名称" :disabled="personForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
          <el-form-item label="用户邮箱" prop="email">
            <el-input v-model="personForm.email" placeholder="请输入用户邮箱" />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="personForm.mobile" placeholder="请输入手机号" :disabled="personForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="personForm.description" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="第三方账号" name="thirdAccount">
        <el-form ref="thirdForm" :model="thirdForm" class="thirdForm" :rules="thirdRules" label-position="left">
          <el-form-item prop="account" class="account">
            <template #label>
              绑定GitLab账号
              <div class="sub-tip">
                <i class="iconfont el-icon-tips-info-circle" />
                <span>绑定后无法修改，请谨慎操作。</span>
              </div>
            </template>
            <el-select v-model="thirdForm.account" filterable clearable :disabled="acconts.length>0" remote :remote-method="getRemoteCodeUser" placeholder="请搜索代码库账户">
              <el-option v-for="v in remoteUsers" :key="v.id" :label="v.name" :value="v.id" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="thirdForm.account" label="GitLab密码重置" name="passwordReset">
        <el-form ref="passwordForm" :model="passwordForm" :rules="passwordFormRules">
          <el-form-item label="新密码" prop="password">
            <el-input
              v-model="passwordForm.password"
              show-password
              placeholder="请输入新密码"
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              show-password
              placeholder="请输入确认密码"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 表单结束，按钮操作部分 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="close">取消</el-button>
        <el-button v-if="tabActive === 'ownSetting'" type="primary" size="small" :loading="saveLoading" @click="addConfirm">确定</el-button>
        <el-button v-else type="primary" size="small" :loading="thirdsaveLoading" @click="holdThird">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>

import { getUserDetail } from '@/api/vone/base/user'
import storage from 'store'
import { editUser } from '@/api/vone/base/user'
import { avatarList } from '@/assets/avatar/avatar'
import { bindPlatformUser, getLibraryAccounts, getLibraryUsers } from '@/api/vone/code/member'
import { apiBaseEngineNoPage } from '@/api/vone/base/engine'
import { debounce } from 'lodash'
import { updateCodeMember } from '@/api/vone/code/member'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }

  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认密码'))
      } else if (value !== this.passwordForm.password) {
        callback(new Error('两次密码输入不一致'))
      } else {
        callback()
      }
    }
    return {
      userAvatar: null,
      avatarList,
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_.\\u4e00-\\u9fa5]{1,50}$',
            message: '请输入不超过50位由字母、数字或者下划线组成的名称',
            trigger: 'change'
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          }
        ],
        email: [
          {
            required: true,
            message: '请输入邮箱地址',
            trigger: 'change'
          },
          {
            pattern: '^\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}$',
            message: '请输入正确的邮箱地址',
            trigger: 'change'
          },
          {
            max: 200,
            message: '请输入长度不超过200个字符的邮箱地址',
            trigger: 'change'
          }
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change'
          }
        ]
      },
      thirdRules: {
        account: [{
          required: true,
          message: '请选择gitlab账号',
          trigger: 'change'
        }]
      },
      formLoading: false,
      saveLoading: false,
      thirdsaveLoading: false,
      tabActive: 'ownSetting',
      personForm: {
        name: '',
        code: '',
        state: true,
        description: '',
        avatarType: true
      },
      thirdForm: {
        account: ''
      },
      engineData: {},
      remoteUsers: [],
      acconts: [],
      typeList: [
        {
          value: 'ALL',
          label: '全部'
        }, {
          value: 'THIS_LEVEL',
          label: '本级'
        }, {
          value: 'THIS_LEVEL_CHILDREN',
          label: '本级及子级'
        }, {
          value: 'CUSTOMIZE',
          label: '自定义'
        }, {
          value: 'SELF',
          label: '个人'
        }],
      passwordForm: {
        id: '',
        confirmPassword: '',
        // oldPassword: '',
        password: ''
      },
      passwordFormRules: {
        // oldPassword: [
        //   {
        //     required: true,
        //     message: '旧密码不允许为空'
        //   }
        // ],
        password: [
          {
            required: true,
            message: '请输入新密码',
            trigger: 'change'
          },
          {
            pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
            message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符',
            trigger: 'change'
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '确认密码不允许为空'
          },
          {
            validator: validatePass
          }
        ],
        gitlabUser: {}
      }
    }
  },

  mounted() {
    // 回显
    this.getUserInfo()
    this.$set(this.personForm, 'avatarPath', 'avatar1')
    // this.userAvatar =
    // require('@/assets/avatar/avatar1.png')
  },
  async created() {
    this.getEngine()
    this.getCodeAccont()
  },
  methods: {
    changeIcon(item) {
      this.$set(this.personForm, 'avatarPath', item.name)
      this.$refs.popoverRef.doClose()
    },

    async getUserInfo() {
      this.formLoading = true
      const userInfo = storage.get('user')
      // const res = await this.$store.dispatch('user/getUserData', userInfo.id
      // )
      const res = await getUserDetail(userInfo.id)
      this.formLoading = false
      if (!res.isSuccess) {
        return
      }

      this.personForm = { ...res.data, ...userInfo }

      // this.$set(this.personForm, 'avatarPath', this.personForm.avatarPath != '' ? `http://${window.location.host}/api/base/webapps/${this.personForm.avatarPath}` : null)

      this.$nextTick(() => {
        this.$refs.personForm.clearValidate()
      })
    },
    // 保存
    async addConfirm() {
      try {
        await this.$refs.personForm.validate()
      } catch (e) {
        return
      }
      this.saveLoading = true
      const res = await editUser(this.personForm)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.$store.commit('user/set_user', {
        id: res.data.id,
        userAccount: res.data.account,
        name: res.data.name,
        userName: res.data.name,
        avatarId: res.data.avatarId,
        orgId: res.data.orgId,
        avatarPath: res.data.avatarPath,
        avatarType: res.data.avatarType
      })
      this.close()
    },
    async holdThird() {
      if (this.tabActive == 'thirdAccount') {
        try {
          await this.$refs.thirdForm.validate()
        } catch (error) {
          return
        }
        const userInfo = storage.get('user')
        try {
          const res = await bindPlatformUser(this.thirdForm.account, userInfo.id)
          if (res.isSuccess) {
            this.$message.success('绑定成功')
            this.getCodeAccont()
          } else {
            this.$message.error('绑定失败')
          }
        } catch (error) {
          this.getCodeAccont()
        }
      } else if (this.tabActive == 'passwordReset') {
        try {
          await this.$refs.passwordForm.validate()
        } catch (error) {
          return
        }
        try {
          const { isSuccess, msg } = await updateCodeMember({
            ...this.gitlabUser,
            password: this.passwordForm.password
          })
          if (!isSuccess) {
            return this.$message.error(msg)
          }
          this.$message.success('重置成功')
          this.$refs.passwordForm.resetFields()
        } catch (error) {
          this.getCodeAccont()
        }
      }
    },
    getRemoteCodeUser: debounce(async function(query) {
      if (!this.engineData.id) {
        this.$message.warning('当前登录用户没有可用引擎，请先授权引擎')
        return
      }
      const res = await getLibraryUsers({ engineId: this.engineData.id, keyword: query })
      if (res.isSuccess) {
        this.remoteUsers = res.data
      }
    }, 1000),
    async getCodeAccont() {
      const res = await getLibraryAccounts()
      if (!res.isSuccess) return
      this.acconts = res.data
      if (res.data?.length > 0) {
        this.thirdForm.account = res.data[0].id
        this.remoteUsers = res.data
        this.gitlabUser = res.data[0]
      }
    },
    async getEngine() {
      const { data, isSuccess } = await apiBaseEngineNoPage({ classify: 'CODE_WAREHOUSE' })
      if (!isSuccess) return
      this.engineData = data?.[0] || {}
    },
    close() {
      // this.$emit('success')
      this.$refs.personForm.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.avatarPath{
  display: flex;
  align-items: center;
   :deep(.el-form-item__content) {
     width: 35%;
     margin-left: 16px;
   }
}

.popover {
  width: 400px;

  .item {
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: var(--main-bg-color,#fff);
    &:hover {
      background-color: var(--hover-bg-color,#f5f6fa);
    }
  }
}
.accountRow{
  height: 32px;
  line-height: 32px;
  padding-left: 8px;
  margin-bottom: 12px;
  .label{
    color: var(--main-font-color);
    margin-right:16px;
  }
}
.vone-tab-left {
  :deep(.el-tabs__nav) {
    min-height: 450px;
  }
}
:deep(.el-tabs--left .el-tabs__header.is-left) {
  margin-right: 0;
}
:deep(.el-tabs__content) {
  padding: 24px 75px;
}
:deep(.el-dialog .el-dialog__body .el-form-item--small[class~=is-required]) {
  margin-bottom: 14px;
}
:deep(.account) {
  .el-form-item__label {
    display: flex;
  }
  .el-form-item--small .el-form-item__error {
    padding-top: 24px;
  }
}
.sub-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #99A0AC;
  font-size: 12px;
  font-weight: 400;
  margin-left: 10px;
}
</style>
