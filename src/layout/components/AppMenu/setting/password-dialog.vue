<template>
  <div>
    <el-dialog
      title="修改密码"
      width="40%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-form ref="passwordForm" :model="passwordForm" :rules="rules">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            show-password
            placeholder="请输入旧密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="passwordForm.password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            show-password
            placeholder="请输入确认密码"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { putPassword } from '@/api/vone/base/user'
import storage from 'store'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    id: {
      type: String,
      default: null
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认密码'))
      } else if (value !== this.passwordForm.password) {
        callback(new Error('与新密码输入不一致'))
      } else {
        callback()
      }
    }
    return {
      passwordForm: {
        id: this.id,
        confirmPassword: '',
        // oldPassword: '',
        password: ''
      },

      rules: {
        oldPassword: [
          {
            required: true,
            message: '请输入旧密码'

          },
          {
            pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
            message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符'

          }
        ],
        password: [
          {
            required: true,
            message: '请输入旧密码'

          },
          {
            pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
            message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符'

          },
          {
            validator: (r, v, c) => {
              if (v === this.passwordForm.oldPassword) {
                c('新密码与旧密码密码不能一致,请修改')
              }
              c()
            }
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '确认密码不允许为空'
          },
          {
            validator: validatePass
          }
        ]
      }
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.passwordForm.resetFields()
    },
    async saveInfo() {
      await this.$refs.passwordForm.validate().catch(e => {
        this.$message.warning('请检查数据')
        this.sendLoading = false
        throw e
      })
      const userInfo = storage.get('user')
      const { isSuccess, msg } = await putPassword(
        {
          id: userInfo.id,
          confirmPassword: this.passwordForm.confirmPassword,
          oldPassword: this.passwordForm.oldPassword,
          password: this.passwordForm.password
        }
      )
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.$message.success('密码修改成功')
      this.onClose()
      this.$emit('success')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
