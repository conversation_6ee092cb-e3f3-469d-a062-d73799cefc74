<template>
  <el-dialog title="工时日志填报" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="80%">
    <div class="simple-dialog">
      <p>工时日志填报对话框 - 简化版本</p>
      <p>这是一个临时的简化版本，用于测试Codegen问题</p>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="close">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ManhourDialogSimple',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  methods: {
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.simple-dialog {
  padding: 20px;
  text-align: center;
}
</style>
