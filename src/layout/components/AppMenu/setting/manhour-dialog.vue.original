<template>
  <div>
    <el-dialog width="80%" :model-value="visible" @update:model-value="$emit('update:visible', $event)" :before-close="onClose" :close-on-click-modal="false" :show-close="false" >
      <template #title>
        <div class="header">
          <div class="title">{{ dateType === "week"? "每周": "每天" }}工时日志填报</div>
          <div class="data-switching">
            <el-button icon="el-icon-arrow-left" plain :disabled="preDisabled" @click="previous" />
            <span v-if="dateType === 'week'" class="data-title">{{ startDate }} 到 {{ endDate }}</span>
            <span v-else class="data-title">{{ dayDate }}</span>
            <el-button icon="el-icon-arrow-right" plain :disabled="nextDisabled" @click="next" />
          </div>
          <div class="date-tabs">
            <span :active="dateType==='week'" @click="changeTab('week')">周</span>
            <span :active="dateType==='day'" @click="changeTab('day')">天</span>
          </div>
        </div>
      </template>
      <div v-loading="pageLoading">
        <div class="box">
          <el-form ref="timeForm" :model="timeForm" :inline-message="false">
            <main style="height:calc(100vh - 250rem);">
              <!-- 周 -->
              <vxe-table
                v-if="dateType==='week'"
                ref="timeTable"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="timeForm.timelist"
                :column-config="{ minWidth:'120px' }"
                :checkbox-config="{ reserve: true }"
                row-id="id"
              >
                <vxe-column key="1" title="用户" width="120">
                  <template #default="{ row,rowIndex }">
                    <el-form-item
                      v-if="!row.echoMap"
                      :key="'timelist.' + rowIndex +'.filledBy'"
                      label-width="0"
                      :prop="'timelist.' + rowIndex +'.filledBy'"
                      :rules="{
                        required: true, message: '请选择用户', trigger: 'change'
                      }"
                    >
                      <vone-remote-user v-model="row.filledBy" placeholder="请选择" />
                    </el-form-item>

                    <span v-else>
                      <span v-if="row.echoMap && row.echoMap.filledBy">
                        <vone-user-avatar :avatar-path="row.echoMap.filledBy.avatarPath" :avatar-type="row.echoMap.filledBy.avatarType" :name="row.echoMap.filledBy.name" />
                      </span>
                    </span>
                  </template>
                </vxe-column>
                <vxe-column key="6" title="项目" width="120">
                  <template #default="{ row,rowIndex }">
                    <el-form-item
                      v-if="!row.echoMap"
                      :key="'timelist.' + rowIndex +'.projectId'"
                      label-width="0"
                      :prop="'timelist.' + rowIndex +'.projectId'"
                      :rules="{
                        required: true, message: '请选择项目', trigger: 'change'
                      }"
                    >
                      <el-select v-model="row.projectId" filterable style="width:100%" @change="e=>projectChange(e, row)">
                        <el-option v-for="i in allProjectList" :key="i.id" :label="i.name" :value="i.id" />
                      </el-select>

                    </el-form-item>
                    <span v-else>
                      <span v-if="row.echoMap && row.echoMap.projectId">
                        {{ row.echoMap.projectId.name }}
                      </span>
                    </span>
                  </template>
                </vxe-column>

                <vxe-column key="2" title="工作项类型" width="100">
                  <template #default="{ row,rowIndex }">
                    <el-row v-if="!row.echoMap" class="typeselect">
                      <el-form-item
                        label-width="0"
                        :prop="'timelist.' + rowIndex +'.type'"
                        :rules="{
                          required: true, message: '请选择类型', trigger: 'change'
                        }"
                      >
                        <el-select v-model="row.type" class="select-tyle" placeholder="请选择" @change="e=>workTypeChange(e, row)">
                          <el-option v-for="op in typeCodeList" :key="op.value" :label="op.label" :value="op.value">
                            <i :class="op.icon" :style="{color:op.color}" />
                            {{ op.label }}
                          </el-option>
                        </el-select>

                      </el-form-item>
                    </el-row>
                    <span v-else>
                      <span v-if="row.type && typeCodeMap[row.type]">
                        <i :class="typeCodeMap[row.type].icon" :style="{color:typeCodeMap[row.type].color}" />
                        {{ typeCodeMap[row.type].label }}
                      </span>

                    </span>
                  </template>
                </vxe-column>
                <vxe-column key="5" title="工作项" min-width="240">
                  <template #default="{ row,rowIndex }">
                    <el-row v-if="!row.echoMap" class="typeselect">
                      <el-form-item
                        label-width="0"
                        :prop="'timelist.' + rowIndex +'.bizId'"
                        :rules="{
                          required: true, message: '请选择工作项', trigger: 'change'
                        }"
                      >
                        <el-select v-model="row.bizId" :remote-method="(query)=>remoteMethod(query,row)" :loading="loading" no-match-text="暂未查询到匹配数据,请重新输入" filterable remote class="select-option" placeholder="请输入编号或标题搜索" clearable>
                          <virtual-list
                            :style="{ 'max-height': '258px', 'overflow-y': 'auto' }"
                            data-key="id"
                            :extra-props="{
                              label: 'name',
                              value: 'id'
                            }"
                            :data-sources="row.workItem || []"
                            :data-component="itemConponent"
                          />
                        </el-select>
                      </el-form-item>
                    </el-row>
                    <span v-else>
                      <span v-if="row.echoMap && row.echoMap.biz">
                        {{ row.echoMap.biz.name }}
                      </span>

                    </span>
                  </template>
                </vxe-column>
                <vxe-column v-for="(item,index) in dateList" :key="item.date+'3_' + index" width="100" :title="item.date" :sum="true" :field="item.date">
                  <template #header="{ column }">
                      <div>
                        <div>{{ item.name }}
                          <span>
                            {{ item.previewDate }}
                          </span>
                        </div>
                        <div>
                          <span :style="{'color': getColor( Number(getSum(column.property) ))}">
                            <span>{{ Number(getSum(column.property) ) }}</span> /
                            <span>{{ configInfo['NORM_HOURS_OF_EVERY_DAY'].value }}</span>
                          </span>

                        </div>
                      </div>

                    </template>
                    <template #default="{row,rowIndex}">
                      <el-form-item
                        :key="'timelist.' + rowIndex+ '_'+ item.date"
                        label-width="0"
                        :prop="'timelist.' + rowIndex + '.' + item.date"
                        :rules="[
                          {required: false, message: '请输入工时', trigger: 'blur'},
                          { validator: checkTime }
                        ]"
                      >
                        <el-input v-model.number="row[item.date]" type="number" :disabled="isdisabled(item,row)" class="number-input" placeholder="请输入" @change="numberChange($event, row, item.date )" />
                      </el-form-item>
                    </template>
                </vxe-column>

                <vxe-column :key="Math.random()" width="80" field="total" title="全部工时" style="font-size:14px">
                  <template #default="{row}">
                    {{ getTotal(row) }} h
                  </template>
                </vxe-column>
                <vxe-column title="操作" fixed="right" align="left" width="50">
                  <template #default="{row, rowIndex }">
                    <el-button
                      :disabled="row.verified ? true : false"
                      icon="iconfont el-icon-application-delete"
                      type="text"
                      size="small"
                      @click.native.prevent="deleteRow(rowIndex, timeForm.timelist)"
                    />
                  </template>
                </vxe-column>
              </vxe-table>
              <!-- 天 -->
              <DayTable v-if="dateType==='day'" ref="dateTypeDay" :config-info="configInfo" :type-code-list="typeCodeList" :day-date="dayDate" :entry="'fast'" />

            </main>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dateType == 'week'" type="text" icon="el-icon-plus" @click="addChild">添加行</el-button>

        <el-button v-else icon="el-icon-plus" type="text" @click="addDayRow">添加行</el-button>

        <span>
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="addTime">提交</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import dayjs from 'dayjs'
import { workItemSearch, addWorkingHoursInfoBatch, getWorkingHoursConfig, getWorkingHoursDayAll, deleteWorkingHoursInfo } from '@/api/vone/manhour/index'
import storage from 'store'
import projectMixin from '@/mixin/project-list'
import DayTable from '@/views/vone/project/work-hour/components/day-table'
import VirtualList from 'vue-virtual-scroll-list'
import itemConponent from '@/views/vone/project/work-hour/components/item-conponent.vue'

import _ from 'lodash'

const calendar = require('dayjs/plugin/calendar')
dayjs.extend(calendar)
const typeCodeList = [
  {
    label: '需求',
    icon: 'iconfont el-icon-icon-xuqiu',
    value: 'ISSUE',
    color: 'rgb(135, 145, 250)'
  },
  {
    label: '缺陷',
    icon: 'iconfont el-icon-icon-quexian',
    value: 'BUG',
    color: 'rgb(250, 107, 87)'
  },
  {
    label: '任务',
    icon: 'iconfont el-icon-icon-renwu',
    value: 'TASK',
    color: 'rgb(62, 123, 250)'
  }
]
export default {
  components: {
    DayTable,
    VirtualList,
    itemConponent
  },
  mixins: [projectMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemConponent: itemConponent,
      pageLoading: false,
      typeCodeList,
      typeCodeMap: {},
      saveLoading: false,
      configInfo: {},
      dateList: [],
      workItem: [],
      startDate: '',
      endDate: '',
      dayDate: '',
      weekday: [
        '星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'
      ],
      currentDay: new Date(),
      currentToday: new Date(),
      timeForm: {
        timelist: []
      },
      loading: false,
      params: {},
      dateType: 'week',
      config: {},
      pickerType: {},
      column: [
        {
          label: 'filledBy',
          width: '150px',
          value: 'filledBy'
        }
      ],
      preDisabled: false,
      nextDisabled: false
    }
  },
  computed: {
    getColor() {
      return function(count) {
        if (count < this.configInfo['NORM_HOURS_OF_EVERY_DAY'].value) {
          return 'red' // √
        } else if (count == this.configInfo['NORM_HOURS_OF_EVERY_DAY'].value) {
          return 'green' // 当数量等于8时显示蓝色
        } else {
          return '#F27900' // 当数量大于8时显示绿色
        }
      }
    }
  },
  mounted() {
    this.pickerType = {
      week: dayjs().add(-6, 'day').startOf('day').format('YYYY-MM-DD'),
      month: dayjs().add(-30, 'day').startOf('day').format('YYYY-MM-DD'),
      quarter: dayjs().add(-90, 'day').startOf('day').format('YYYY-MM-DD')
    }
    this.addChild()
    this.typeCodeMap = this.typeCodeList.reduce((r, v) => (r[v.value] = v) && r, {})
  },
  created() {
    this.getWorkingHoursConfFn()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      // this.$refs.companyForm.resetFields()
    },
    getSum(field) {
      return this.$refs.timeTable && this.$refs.timeTable.tableData.reduce((total, row) => total + (row[field] ? row[field] : 0), 0) || 0
    },
    isdisabled(item, row) {
      // 可填报周期
      const setting = this.config['FILLABLE_PERIOD']
      // today
      const currentDay = dayjs().format('YYYY-MM-DD')
      // today -> 30天
      const monthDay = dayjs(currentDay).subtract(30, 'day').format('YYYY-MM-DD')
      // today -> 90天
      const quarterDay = dayjs(currentDay).subtract(90, 'day').format('YYYY-MM-DD')

      const isVerified = row.filledInfos?.find(r => r.fillingTime == item.date)?.verified

      if (setting == 'month') {
        return !!(dayjs(item.date).isBefore(dayjs(monthDay)) || (isVerified && row[item.date]))
      } else if (setting == 'quarter') {
        return !!dayjs(item.date).isBefore(dayjs(quarterDay)) || (isVerified && row[item.date])
      } else {
        return false
      }
    },
    async getWorkingHoursConfFn() {
      try {
        const res = await getWorkingHoursConfig()
        res.data.forEach(e => {
          this.config[e.key] = e.value
        })

        this.configInfo = res.data.reduce((r, v) => (r[v.key] = v) && r, {})
        this.dayDate = dayjs(new Date()).format('YYYY-MM-DD')

        this.getDateList()
      } catch (e) {
        return
      }
    },
    async  getDateList() {
      this.pageLoading = true
      var d = this.getMonDate()
      var arry = []
      if (this.dateType === 'week') {
        for (var i = 0; i < 7; i++) {
          var obj = {
            name: this.weekday[dayjs(d).day()],
            week: dayjs(d).day(),
            date: dayjs(d).format('YYYY-MM-DD'),
            previewDate: dayjs(d).format('DD')
          }

          arry.push(obj)
          d.setDate(d.getDate() + 1)
        }

        this.startDate = arry[0].date
        this.endDate = arry[6].date
      } else {
        var objs = {
          name: this.weekday[dayjs(this.dayDate).day()],
          week: dayjs(this.dayDate).day(),
          date: this.dayDate
        }
        arry.push(objs)
      }

      const userInfo = storage.get('user')

      const res = await getWorkingHoursDayAll(
        this.dateType == 'week' ? this.startDate : this.dayDate,
        this.dateType == 'week' ? this.endDate : this.dayDate,
        [userInfo.id]
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.dateList = arry

      const dateMap = res.data.hoursDays.reduce((r, v) => (r[v.day] = v.duration) && r, {})

      // const allTime = _.sum(res.data?.userHistory?.map(r => r.duration))

      this.dateList.forEach(element => {
        element.duration = dateMap[element.date] ? dateMap[element.date] : 0
      })
      const result = []

      res.data.userHistory.forEach((element, index) => {
        element.dateContainer = []
        if (element.filledInfos && element.filledInfos.length) {
          element.filledInfos.forEach(j => {
            element.dateContainer.push(j.fillingTime)
          })
        }

        element.type = element.type?.code
        const obj = element.filledInfos.reduce((r, v) => (r[v.fillingTime] = v.duration) && r, {})

        element.verified = element.filledInfos.length ? element.filledInfos[0].verified : false
        result.push(Object.assign({}, element, obj))
      })

      this.timeForm.timelist = result
      this.pageLoading = false

      this.dateCalculation()
      this.getBtnDIsabled()
      // this.getgetWorkingHoursDay()
    },
    getBtnDIsabled() {
      // 可填报周期
      const setting = this.config['FILLABLE_PERIOD']
      // today
      const currentDay = dayjs().format('YYYY-MM-DD')
      // today -> 一周
      const weekDay = dayjs(currentDay).subtract(6, 'day').format('YYYY-MM-DD')
      // today -> 30天
      const monthDay = dayjs(currentDay).subtract(30, 'day').format('YYYY-MM-DD')
      // today -> 90天
      const quarterDay = dayjs(currentDay).subtract(90, 'day').format('YYYY-MM-DD')

      if (this.dateType == 'week') {
        // rifht按钮
        this.nextDisabled = this.endDate == currentDay
        // left 按钮
        if (setting == 'week') {
          this.preDisabled = true
        } else if (setting == 'month') {
          this.preDisabled = dayjs(this.startDate).isBefore(dayjs(monthDay)) // 默认毫秒
        } else if (setting == 'quarter') {
          this.preDisabled = dayjs(this.startDate).isBefore(dayjs(quarterDay)) // 默认毫秒
        } else {
          this.preDisabled = false
        }
      } else {
        // rifht按钮
        this.nextDisabled = this.dayDate == currentDay
        // left 按钮
        if (setting == 'week') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(weekDay)) // 默认毫秒
        } else if (setting == 'month') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(monthDay)) // 默认毫秒
        } else if (setting == 'quarter') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(quarterDay)) // 默认毫秒
        } else {
          this.preDisabled = false
        }
      }
    },
    changeTab(e) {
      this.dateType = e
      this.dayDate = dayjs(new Date()).format('YYYY-MM-DD')
      this.timeForm.timelist.forEach((e, index) => {
        if (e.dateContainer && e.dateContainer.length > 0) {
          e.dateContainer.forEach(item => {
            e[item] = null
          })
        }
        this.$set(this.timeForm.timelist, index, e)
      })
      this.getWorkingHoursConfFn()
    },

    getTotal(row) {
      var total = 0
      row.datelist.forEach(item => {
        if (row[item.date] && row[item.date] > 0) {
          total += row[item.date]
        }
      })
      row.total = total
      return total
    },
    checkTime(rule, value, callback) {
      const filed = rule.field.split('.')
      const key = filed[filed.length - 1]
      const duration = this.dateList.find(r => r.date == key).duration

      if (value == null || value === '' || typeof value == 'undefined') {
        if (this.dateType === 'week') {
          return callback()
        } else {
          return callback(new Error('请输入工时'))
        }
      }
      if (typeof value !== 'number') {
        callback(new Error('工时为数字'))
      } else if (value <= 0) {
        callback(new Error('工时应大于0'))
      } else if (Number(value + duration) > Number(this.config['MAX_HOURS_OF_EVERY_DAY'])) {
        callback(new Error(`工时不大于${this.config['MAX_HOURS_OF_EVERY_DAY']}`))
      } else {
        callback()
      }
    },
    remoteMethod: _.debounce(function(query, row, date) {
      this.loading = true
      if (query != '') {
        this.params.search = query
        this.getworkItemSearch(row, date)
      }
    }, 1000),
    resetForm(formName) {
      this.timeForm.timelist.forEach(e => {
        e.total = 0
      })
      this.$refs[formName].resetFields()
    },
    numberChange(e, row, item) {
      if (e == '') {
        this.$set(row, item, null)
        if (row.dateContainer.includes(item)) {
          row.dateContainer.splice(row.dateContainer.findIndex(e => e === item), 1)
        }
      } else {
        if (!row.dateContainer.includes(item)) {
          row.dateContainer.push(item)
        }
      }
    },
    workTypeChange(e, row, date) {
      this.$set(row, 'bizId', '')

      this.params.sourceTypes = [e]
      this.params.rateProgress = this.configInfo?.BIZ_STATUS?.value
      this.params.search = null
      this.params.projectId = row.projectId
      this.getworkItemSearch(row, date)
    },
    async  projectChange(e, row, date) {
      this.$set(row, 'bizId', '')
      this.loading = true
      const res = await workItemSearch({
        sourceTypes: [row.type],
        rateProgress: this.configInfo?.BIZ_STATUS?.value,
        projectId: e,
        limit: '-1'
      })
      if (res.isSuccess) {
        const week = this.$refs.timeTable.tableData.filter(r => r.bizId).map(j => j.bizId)

        res.data.forEach(element => {
          element.disabled = !!week.includes(element.id)
        })

        this.$set(row, 'workItem', res.data)
      }
      this.loading = false
    },

    async getworkItemSearch(row, date) {
      this.loading = true
      const res = await workItemSearch({ ...this.params, limit: '-1' })
      if (res.isSuccess) {
        const week = this.$refs.timeTable.tableData.filter(r => r.bizId).map(j => j.bizId)

        res.data.forEach(element => {
          element.disabled = !!week.includes(element.id)
        })
        // row[date + '_workItem'] = res.data
        // row.workItem = res.data
        this.$set(row, 'workItem', res.data)
      }
      this.loading = false
    },
    previous() {
      const date = dayjs(this.currentDay).subtract(7, 'day').toDate()
      this.currentDay = date
      if (this.dateType === 'day') {
        const todate = dayjs(this.currentToday).subtract(1, 'day').toDate()
        this.currentToday = todate
        this.dayDate = dayjs(this.currentToday).format('YYYY-MM-DD')
      }
      this.getDateList()
    },
    next() {
      const date = dayjs(this.currentDay).add(7, 'day').toDate()
      this.currentDay = date
      if (this.dateType === 'day') {
        const todate = dayjs(this.currentToday).add(1, 'day').toDate()
        this.currentToday = todate
        this.dayDate = dayjs(this.currentToday).format('YYYY-MM-DD')
      }
      this.getDateList()
    },
    async addTime() {
      if (this.dateType == 'day') {
        this.addDay()
        return
      }
      try {
        await this.$refs.timeForm.validate()
        var arry = []

        this.timeForm.timelist.forEach(e => {
          e.dateContainer.forEach(item => {
            if (e[item]) {
              const historyId = e.filledInfos?.find(r => r.fillingTime == item)?.id || null
              var obj = {
                filledBy: this.dateType === 'week' || e.echoMap ? e.filledBy : e[item + '_filledBy'],
                type: this.dateType === 'week' || e.echoMap ? e.type : e[item + '_type'],
                bizId: this.dateType === 'week' || e.echoMap ? e.bizId : e[item + '_bizId'],
                fillingTime: item,
                duration: e[item],
                description: e[item + '_description'],
                id: historyId
              }
              arry.push(obj)
            }
          })
        })

        if (arry.length > 0) {
          this.saveLoading = true
          const res = await addWorkingHoursInfoBatch(arry)
          this.saveLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.$message.success('操作成功')
          this.getWorkingHoursConfFn()
        // this.resetForm('timeForm')
        } else {
          this.$message.warning('请填写工时')
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
    async addDay() {
      // console.log(this.$refs.dateTypeDay.$children[0].validate(), 'this.$refs.dateTypeDay.dayForm')
      await this.$refs.dateTypeDay.$children[0].validate()
      // console.log(this.$refs.dateTypeDay, '9909')

      const table = this.$refs.dateTypeDay.dayForm.tableData

      const params = table.map(r => ({
        filledBy: r.filledBy,
        type: r.type,
        bizId: r.bizId,
        duration: r.duration,
        description: r.description,
        projectId: this.$route.params.id,
        id: r.echoMap ? r.id : null,
        fillingTime: this.dayDate
      }))

      const res = await addWorkingHoursInfoBatch(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('操作成功')
      this.$refs.dateTypeDay.getAllInfo()
    },
    addChild() {
      const userInfo = storage.get('user')
      const parameter = {
        filledBy: userInfo.id,
        type: '',
        name: '',
        datelist: this.dateList,
        total: 0,
        dateContainer: [],
        description: '',
        duration: null
      }
      this.dateList.forEach(e => {
        parameter[e.date] = null
      })

      this.params = {}
      this.timeForm.timelist.push(parameter)
    },
    // deleteRow(e) {
    //   this.timeForm.timelist.splice(e, 1)
    // },
    getMonDate() {
      var d = _.cloneDeep(this.currentDay)
      const dd = dayjs(d).subtract(6, 'day').$d
      return dd
    },

    dateCalculation() {
      const userInfo = storage.get('user')

      this.timeForm.timelist.forEach((e, index) => {
        e.datelist = this.dateList
        e.filledBy = userInfo.id
        this.$set(this.timeForm.timelist, index, e)
      })

      this.$nextTick(() => {
        // this.$refs['timeTable'].doLayout()
      })
    },
    async deleteRow(e, data) {
      if (!data[e].filledInfos || !data[e].filledInfos.length) {
        this.timeForm.timelist.splice(e, 1)
        return
      }

      try {
        await this.$confirm(`确定删除此条工时记录吗?`, '删除', {
          type: 'warning',
          closeOnClickModal: false
        })

        const res = await deleteWorkingHoursInfo([data[e].filledInfos[0].id])
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        } else {
          this.$message.success('删除成功')
          this.getWorkingHoursConfFn()
        }
      } catch (e) {
        return
      }
    },
    addDayRow() {
      this.$refs.dateTypeDay.addChild()
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
	position: relative;
  height: calc(100vh - 250px);
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
	// padding-right: 30px;
	.title {
		font-weight: 500;
    color: var(--main-font-color);
	}
	.data-switching {
		width: 400px;
		text-align: center;
		.data-title {
			font-weight: 600;
			margin: 0 24px;
			color: var(--main-font-color);
		}
    .el-button {
      width: 32px;
    }
	}
  .date-tabs {
    background: #F2F3F5;
    border-radius: 2px;
    padding: 4px;
    height: 38px;
    >span {
      color: var(--auxiliary-font-color);
      font-size: 14px;
      cursor: pointer;
      height: 30px;
      width: 38px;
      text-align: center;
      line-height: 30px;
      display: inline-block;
      border-radius: 2px;
      &[active] {
        background: #fff;
        color: #3E7BFA;
        font-weight: 500;
      }
    }
  }
}
:deep(.el-button) {
	min-width: 32px;
	&--default {
    background-color: var(--main-bg-color);
    border-color: #CED1D9;
    color: #6B7385;
    &:focus{
      background-color: var(--main-bg-color);
      border-color: #CED1D9;
      color: #6B7385;
    }
    &:hover {
      background-color: #F2F3F5;
      border-color:#CED1D9;
      color: #6B7385;
    }
  }
}
:deep(th.el-table__cell.is-leaf) {
	height: 48px;
}
:deep(.el-table__cell:nth-child(-n+2)) {
font-size: 14px;
}
:deep(.el-table__cell:nth-child(n+2)) {
font-size: 14px;
}
:deep(.vxe-table .el-form-item) {
  // margin-bottom: 16px;
  margin: 16px 0;
  .el-form-item {
    margin: 0;
  }
}
.select-tyle {
  min-width: 90px;
  height: 30px;
	:deep(.el-input__inner) {
		border: none;
	}
	&::after {
		content: '';
		width: 1px;
		height: 20px;
		background: #EAECF0;
		position: absolute;
		right: 0px;
    top: calc(50% - 10px);
	}
}
.select-option {
  height: 30px;
	:deep(.el-input__inner) {
		border: none;
	}
}
.foot {
	width: calc(100% - 72px);
  height: 64px;
  line-height: 64px;
	text-align: right;
	position: absolute;
	bottom: 0;
	right: 0;
  padding-right: 16px;
  background-color: var(--main-bg-color);
  box-shadow: 0px -3px 12px rgba(29, 33, 41, 0.06);
  z-index: 10
  // background-color: red;

}
.pushbut {
  width: 100%;
  margin-bottom: 30px;
}
.typeselect {
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  border: 1px solid var(--input-border-color);
  border-radius: 2px;
  :deep(.el-form-item) {
    margin: 0;
  }
  &:focus {
    border-color: #3E7BFA;
  }
  :deep(.el-input__inner + .typeselect) {
    &:focus {
      border-color: #3E7BFA;
      color: red;
    }
  }
}
.iconfont {
  line-height: 22px;
  margin-right: 9px;
}
.fontstyle {
  overflow:hidden;
  white-space:nowrap;
  text-overflow:ellipsis;
}
:deep(.vone-vxe-table .vxe-body--column) {
  height: 40px;
  line-height: 40px;
  &>.vxe-cell {
     max-height: 34px;
    //  height: 40px;
    // line-height: 40px;
  }
}
:deep(.el-button--text) {
  padding: 0;
  min-width: auto;
}
:deep(.vxe-table::before) {
  display: none;
}
:deep(.el-table__fixed-right::before, .el-table__fixed::before) {
  display: none;
}
.number-input {
  :deep(input::-webkit-inner-spin-button) {
    appearance: none !important;
  }

  :deep(input::-webkit-outer-spin-button) {
    appearance: none !important;
  }

  input[type='number'] {
    appearance: textfield;
  }
}
.dialog-footer{
	// padding:0 16px;
  display: flex;
  justify-content: space-between;
}

:deep(.vxe-table .el-form-item) {
  margin: 0;
}

:deep(.vone-vxe-table .vxe-body--column) {
  height: 40px;
  line-height: 40px;
}

</style>
