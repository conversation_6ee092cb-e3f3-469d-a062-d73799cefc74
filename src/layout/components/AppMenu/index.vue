<template>
  <div class="left-app-container">
    <div class="logo-main">
      <div v-if="logo">
        <el-image
          fit="fill"
          :src="logo"
          class="product-logo"
        />

      </div>
      <!-- <svg-icon icon-class="logo" class="product-logo" @click="$router.push('/')" /> -->
    </div>
    <div class="appbox">
      <vone-app :active-app="activeApp" />
    </div>

    <el-popover ref="cascader" placement="right" trigger="click" popper-class="cascader-popover">

      <el-cascader-panel
        :model-value="choose"
        @update:model-value="choose = $event"
        :options="options"
        :props="{ expandTrigger: 'hover' }"
        size="mini"
        @change="changeFunction"
        @visible-change="visibleChange"
      >
        <template v-slot:default="{ data }">

          <i :class="data.icon" style="color: #777F8E" />

          <span class="ml-8">{{ data.label }}</span>
        </template>
      </el-cascader-panel>

      <template v-slot:reference>
        <div class="user-main">
          <!-- 登录用户头像 -->
          <vone-user-avatar :avatar-path="user && user.avatarPath" :avatar-type="true" :show-name="false" height="35px" width="35px" />
        </div>
      </template>
    </el-popover>

    <!-- 个人设置 -->
    <ownSettingDialog v-if="ownSettingParams.visible" :model-value="ownSettingParams.visible" @update:model-value="ownSettingParams.visible = $event" v-bind="ownSettingParams" />
    <!-- 修改密码 -->
    <passwordDialog v-if="passwordParams.visible" :model-value="passwordParams.visible" @update:model-value="passwordParams.visible = $event" v-bind="passwordParams" />

    <!-- 企业设置 -->
    <companyDialog v-if="conpanyParams.visible" :model-value="conpanyParams.visible" @update:model-value="conpanyParams.visible = $event" v-bind="conpanyParams" />

    <!--工时管理 -->
    <manHourDialog v-if="hourParams.visible" :model-value="hourParams.visible" @update:model-value="hourParams.visible = $event" v-bind="hourParams" />

  </div>
</template>
<script>
import VoneApp from './voneApp.vue'
import ownSettingDialog from './setting/own-setting-dialog.vue'
import passwordDialog from './setting/password-dialog.vue'
import companyDialog from './setting/company-dialog.vue'
import manHourDialog from './setting/manhour-dialog.vue'
import storage from 'store'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'AppMenu',
  components: {
    VoneApp,
    ownSettingDialog,
    passwordDialog,
    companyDialog,
    manHourDialog
  },
  data() {
    return {
      choose: [],
      avatarPath: null,
      ownSettingParams: { visible: false },
      passwordParams: { visible: false },
      conpanyParams: { visible: false },
      hourParams: { visible: false },
      options: [
        {
          value: 'releaseNote',
          label: '更新说明',
          icon: 'iconfont el-icon-application-renew'
        },
        {
          value: 'setting',
          label: '个人设置',
          icon: 'iconfont el-icon-application-setting'
        },
        {
          value: 'password',
          label: '修改密码',
          icon: 'iconfont el-icon-application-password'
        },
        {
          value: 'company',
          label: '企业设置',
          icon: 'iconfont  el-icon-application-company-setting'
        },
        {
          value: 'logout',
          label: '退出登录',
          icon: 'iconfont el-icon-application-quit'
        }
      ],
      companyLogo: null,
      logo: require('@/assets/logo.png')
    }
  },
  computed: {
    activeApp() {
      const route = this.$route
      const { meta } = route
      if (meta.activeApp) {
        return meta.activeApp
      }
      return meta.key
    },
    ...mapGetters([
      'user'
    ]),
    ...mapState({
      user: state => state.user.user,
      token: state => state.token
    })
  },
  created() {
    const userInfo = storage.get('user')
    if (Object.keys(this.$store.state.user.user).length == 0) {
      this.$store.commit('user/set_user', userInfo)
    }
  },
  methods: {
    visibleChange(val) {},
    async changeFunction(val) {
      this.$refs.cascader.doClose()
      this.choose = []

      if (val[0] == 'logout') {
        this.logout()
        // 切换主题
      } else if (val[0] == 'flow') {
        // 绑定刷新图标事件
        this.$bus.$emit('refreshChart')
      } else if (val[0] == 'setting') { // 个人设置
        this.ownSettingParams = { visible: true }
      } else if (val[0] == 'password') { // 修改密码
        this.passwordParams = { visible: true }
      } else if (val[0] == 'company') { // 企业设置
        this.conpanyParams = { visible: true }
      } else if (val[0] == 'releaseNote') {
        this.$router.push({ path: '/releaseNote' })
      }
    },
    // 退出登录
    logout() {
      localStorage.removeItem('panel-main-tree')
      localStorage.removeItem('dataset-tree')
      this.$store.dispatch('user/logout', this.user.id)
    }
  }
}
</script>
<style scoped lang="scss">
@import "@/styles/variables.scss";
.ml-8 {
  margin-left: 8px;
}
.left-app-container {
  width: $nav-left-width;
  height: 100vh;
  background: var(--main-bg-color,#fff);
  border-right: 1px solid var(--solid-border-color);
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  left: 0;
  padding: 0px 4px;
  .logo-main {
    text-align: center;
    cursor: pointer;
    user-select: none;
    .product-logo {
      width: 42px;
      margin: 16px 0 0;
    }
  }
  .user-main {
    position: absolute;
    bottom: 16px;
    left: 50%;
		transform: translate(-50%,0);
    overflow: hidden;
    border-radius: 6px;
    cursor: pointer;

    img {
			text-align: center;
      width: 100%;
    }
  }
  .appbox {
    margin-top: 8px;
  }
}

:deep(.el-cascader-node__prefix) {
  display: none;
}
:deep(.el-cascader-menu__list) {
  padding-top: 2px;
}

:deep(.el-cascader-node.is-disabled) {
  cursor: not-allowed;
}
:deep(.el-cascader-node.is-disabled:hover) {
  cursor: not-allowed;
}
:deep(.el-cascader-menu__wrap) {
  // height: 130px !important;
}
</style>
<style lang='scss'>
// 用户弹窗样式
.cascader-popover.el-popper {
  padding: 0px;
  border: none;
  box-shadow: none;
}

</style>
