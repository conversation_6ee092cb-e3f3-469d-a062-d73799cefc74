<template>
  <div>
    <el-dialog title="详情" width="40%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-row type="flex" style="margin-bottom:16px">

        <el-upload
          accept=".lic"
          action="#"
          class="avatar-uploader"
          :show-file-list="false"
          :on-change="handleChange"
        >
          <el-button icon="el-icon-refresh" type="primary" :loading="upLoading">
            更新licenses
          </el-button>

        </el-upload>
        <el-button icon="iconfont el-icon-edit-export" @click="download">
          导出licenses
        </el-button>
      </el-row>
      <main style="height:calc(100vh - 380px)">

        <vxe-table
          ref="lecensesTable"
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :loading="pageLoading"
          :empty-render="{ name: 'empty' }"
          :data="data"
          row-id="id"
          :column-config="{ minWidth:'120px' }"
        >
          <vxe-column title="应用" field="name" width="150" />
          <vxe-column field="failureTime" title="失效时间">
            <template #default="{ row }">
              <span>
                {{ row.endTime }}
              </span>
            </template>
          </vxe-column>
          <vxe-column field="stateName" title="是否授权" width="100px">
            <template #default="{ row }">
              {{ row.stateName }}
            </template>
          </vxe-column>
        </vxe-table>
      </main>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onClose">关闭</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
const XLSX = require('xlsx') // 使用import有的属性无法找到
import { apiOuthUploadLicenses } from '@/api/vone/ouath/ouath'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    exportData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageLoading: false,
      licensesFile: null,
      upLoading: false

    }
  },
  computed: {

  },
  mounted() {

  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    download() {
      this.$nextTick(() => {
        this.exportExcel('licenses详情', this.exportData)
      })
    },
    exportExcel(filename, data) {
      const worksheet = XLSX.utils.aoa_to_sheet(data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      XLSX.writeFile(workbook, filename + '.xlsx')
    },

    async handleChange(val) {
      if (val.status != 'ready') return
      try {
        this.upLoading = true

        const { isSuccess, msg, errorMsg } = await apiOuthUploadLicenses({
          licensesFile: val.raw
        })

        this.upLoading = false
        if (!isSuccess) {
          this.$message.warning(errorMsg)
          return
        }
        this.$message.success(msg)
        this.$emit('success', 0)
        this.onClose()
      } catch (e) {
        this.upLoading = false
        this.$emit('success', 1)
        this.onClose()
        return
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader{
	margin-right:  16px;
}
</style>
