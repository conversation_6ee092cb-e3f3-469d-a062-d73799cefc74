<template>
  <!-- 自定义趋势图 -->
  <vone-echarts-card :title="formInfo.name">
    <vone-echarts :options="options" @chartClick="chartClick" />
    <detailDialog v-if="detailParams.visible" v-bind="detailParams" v-model="detailParams.visible" />
  </vone-echarts-card>
</template>

<script>
import { getChartData } from '@/api/vone/dashboard/index'
import detailDialog from '@/views/widget/detail-data-dialog.vue'
export default {
  components: { detailDialog },
  props: {
    formInfo: {
      type: Object,
      default: null
    },
    keys: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      detailParams: { visible: false },
      data: [],
      options: {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          },
          confine: true
        },
        legend: {
          icon: 'circle',
          x: 'right', // 居右显示
          itemHeight: 8,
          itemWidth: 8,
          borderRadius: 8,
          data: [],
          textStyle: { // 图例文字的样式
            color: '#777F8E'
          }

        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          // boundaryGap: false,
          splitLine: {
            show: false // 去掉网格线
          },
          axisLabel: {
            show: true,
            rotate: 20,
            textStyle: {
              color: '#777F8E' // 更改坐标轴文字颜色
            }
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5' // 更改坐标轴颜色
            }
          }
        },
        yAxis: {
          type: 'value',
          'axisTick': { // y轴刻度线
            'show': false
          },
          minInterval: 1,
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: '#EBEEF5'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#777F8E' // 更改坐标轴文字颜色
            }
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5' // 更改坐标轴颜色
            }
          }
        },
        series: []
      }

    }
  },
  watch: {

  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const { data, isSuccess, msg } = await getChartData({
        group: this.formInfo.group,
        x: this.formInfo.x,
        y: this.formInfo.y,
        tableViewId: this.formInfo.tableViewId
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }

      // 处理空数据
      data.outList.forEach(element => {
        element.group = !element.group ? '(空)' : element.group
        element.key = !element.key ? '(空)' : element.key
      })

      // 初始化数据容器
      const dataMap = {}
      // 初始化 x 轴的值
      const xAxis = []
      // 遍历待转换的数据
      data.outList.forEach(item => {
        // 检查 x 轴的值是否已存在
        if (!xAxis.includes(item.key)) {
          xAxis.push(item.key)
        }
        // 初始化数据容器的值
        if (!dataMap[item.group]) {
          dataMap[item.group] = Array(xAxis.length).fill(0)
        }
        // 将数据填充到数据容器中
        const index = xAxis.indexOf(item.key)
        dataMap[item.group][index] = Number(item.value)
      })
      if (this.formInfo.sortrule == 'over') { // x轴倒序
        for (const key in dataMap) {
          if (dataMap[key].length < xAxis.length) {
            for (let i = 0; i < xAxis.length - dataMap[key].length; i++) {
              dataMap[key].push(0)
            }
          }
          dataMap[key].reverse()
        }
        xAxis.reverse()
      }
      // 转换数据容器的结构
      const transformedData = Object.keys(dataMap).map(key => {
        return {
          name: !this.formInfo.isGroup ? '总计' : key,
          type: 'line',
          data: dataMap[key]
        }
      })

      // 返回转换后的数据
      this.options.series = transformedData
      this.options.xAxis.data = xAxis
    },
    chartClick(params) {
      if (params.name == '(空)') {
        this.$message.warning('当前数据分组,暂不支持查看')
        return
      }
      const form = this.formInfo
      const data = {
        groupData: null,
        xdata: params.name
      }
      this.detailParams = { visible: true, form: form, data: data }
    }

  }

}
</script>

<style>
</style>
