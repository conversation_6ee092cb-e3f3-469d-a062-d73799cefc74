<template>
  <vone-echarts-card :title="formInfo.name">
    <div style="min-height:200px">
      <!-- 二维表 -->
      <vxe-table
        v-if="formInfo.listType == '1' "
        ref="customList"
        class="vone-vxe-table"
        height="auto"
        border="default"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="data"
        :loading="tableLoading"
        row-id="id"
      >
        <vxe-column v-for="(item,index) in tableProps" :key="index+Math.random()" :title="item == 'countMath' ? '合计': item" :field="item">
          <template #default="{ row }">
            <span v-if="index == 0">
              {{ row[item] }}
            </span>
            <a v-else @click="chartClick(row,item)">{{ row[item] }}</a>
          </template>
        </vxe-column>
      </vxe-table>
      <!-- 普通表 -->
      <vxe-table
        v-else
        ref="normalChart"
        class="vone-vxe-table"
        border
        auto-resize
        min-height="200px"
        :loading="loading"
        :empty-render="{ name: 'empty' }"
        :data="tableData"
        :column-config="{ minWidth:'150px' }"
        row-id="id"
      >
        <vxe-column title="编号" field="code" min-width="150" fixed="left">
          <template #default="{ row }">
            <a @click="jumpTask(row)">{{ row.code }}</a>
          </template>
        </vxe-column>
        <vxe-column title="标题" field="name" min-width="150" fixed="left">
          <template #default="{ row }">
            <a @click="jumpTask(row)">{{ row.name }}</a>
          </template>
        </vxe-column>
        <vxe-column title="工作项类型" field="classify" min-width="150">
          <template #default="{ row }">
            {{ typeNameObj(row).classifyType }}
          </template>
        </vxe-column>
        <vxe-column title="类型" field="type" min-width="150">
          <template #default="{ row }">
            {{ typeNameObj(row).typeName }}
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="leadingBy" min-width="100">
          <template #default="{ row }">
            <vone-user-avatar :avatar-path="userInfo(row).avatarPath" :avatar-type="userInfo(row).avatarType" :name="userInfo(row).name" />
          </template>
        </vxe-column>
        <vxe-column title="状态" field="state" width="100">
          <template #default="{ row }">
            {{ stateName(row) }}
          </template>
        </vxe-column>
        <vxe-column field="projectId" show-overflow="tooltip" title="所属项目" min-width="180">
          <template #default="{ row }">
            {{ projectName(row) }}
          </template>
        </vxe-column>
      </vxe-table>

    </div>
    <detailDialog v-if="detailParams.visible" v-bind="detailParams" v-model:visible="detailParams.visible" />

  </vone-echarts-card>
</template>
<script>
import { getChartData } from '@/api/vone/dashboard/index'
import { apiInsightDetailItemInfo } from '@/api/vone/weidget/index'
import detailDialog from '@/views/widget/detail-data-dialog.vue'

export default {
  components: { detailDialog },
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      detailParams: { visible: false },
      data: [],
      tableProps: [],
      tableLoading: false,
      loading: false,
      tableData: []
    }
  },
  computed: {
    typeNameObj() {
      return function(row) {
        const classifyType = row.echoMap?.typeCode?.classify?.desc || ''
        const typeName = row.echoMap?.typeCode?.name || ''
        return { classifyType, typeName }
      }
    },
    userInfo() {
      return function(row) {
        const avatarPath = row.echoMap?.leadingBy?.avatarPath || ''
        const avatarType = row.echoMap?.leadingBy?.avatarType || ''
        const name = row.echoMap?.leadingBy?.name || ''
        return { avatarPath, avatarType, name }
      }
    },
    stateName() {
      return function(row) {
        return row.echoMap?.stateCode?.name || ''
      }
    },
    projectName() {
      return function(row) {
        return row.echoMap?.projectId?.name || ''
      }
    }
  },
  mounted() {
    if (this.formInfo.listType == 1) {
      this.getData()
    } else {
      this.getInfo()
    }
  },
  methods: {
    async getData() {
      this.tableLoading = true
      const { data, isSuccess, msg } = await getChartData({
        group: this.formInfo.group,
        x: this.formInfo.x,
        y: this.formInfo.y,
        tableViewId: this.formInfo.tableViewId
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      // 处理空数据
      data.outList.forEach(element => {
        element.group = !element.group ? '(空)' : element.group
        element.key = !element.key ? '(空)' : element.key
      })
      // 初始化数据容器
      const dataMap = {}
      // 初始化 x 轴的值
      const xAxis = Array.from(new Set(data.outList.map(item => item.key)))

      xAxis.unshift(this.formInfo.groupName)

      // 遍历待转换的数据
      data.outList.forEach(item => {
        // 初始化数据容器的值
        if (!dataMap[item.group]) {
          dataMap[item.group] = Array(xAxis.length).fill(0)
        }
        // 将数据填充到数据容器中
        const index = xAxis.indexOf(item.key)
        dataMap[item.group][index] = Number(item.value)
      })

      // 转换数据容器的结构
      const transformedData = Object.keys(dataMap).map(key => {
        return {
          name: key || this.formInfo.x,
          data: dataMap[key]
        }
      })

      transformedData.map(item => { // 空位补0
        xAxis.map((v, index) => {
          item[v] = v === this.formInfo.groupName ? item.name : item.data[index] || 0
        })
      })
      const count = { [this.formInfo.groupName || 'name']: '合计', countMath: 0 }
      if (this.formInfo.listType == 1) { // 合计
        transformedData.map(item => {
          item.countMath = sum(item.data || [])
          count.countMath = count.countMath + item.countMath
        })
        transformedData.map(item => {
          xAxis.map((v, index) => {
            item[v] = v === this.formInfo.groupName ? item.name : item.data[index] || 0
            count[v] = v === this.formInfo.groupName ? count[v] : (count[v] || 0) + item.data[index]
          })
        })
        transformedData.push(count)
      }
      transformedData.map(item => {
        delete item.name
        delete item.data
      })

      function sum(arr) { // 计算列和
        var s = 0
        arr.map(function(val, idx, arr) {
          s += val
        }, 0)
        return s
      }

      const tableProp = [] // table头
      for (const key in transformedData[0]) {
        if (tableProp.indexOf(key) < 0 && key !== 'id') {
          tableProp.push(key)
        }
      }
      if (this.formInfo.listType !== 1) {
        delete tableProp['countMath']
      }

      this.tableProps = tableProp
      this.data = transformedData

      this.tableLoading = false
    },
    // 普通列表
    async getInfo() {
      this.loading = true
      const { data, isSuccess, msg } = await apiInsightDetailItemInfo({
        ...this.formInfo
        // xdata: this.data.xdata
      })
      this.loading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.tableData = data
    },
    async jumpTask(row) {
      const workItemType = row.echoMap?.typeCode?.classify?.code
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = workItemType == 'ISSUE' ? '/project/issue' : workItemType == 'TASK' ? '/project/task' : workItemType == 'BUG' ? '/project/defect' : '/project/issue'
      const newpage = await this.$router.resolve({ path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`, query: {
        showDialog: true,
        queryId: row?.id,
        rowTypeCode: row?.typeCode,
        stateCode: row?.stateCode,
        projectId: projectId
      }})
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    chartClick(row, item) {
      const form = this.formInfo
      if (item == '(空)' || row[form.groupName] == '(空)') {
        this.$message.warning('当前数据分组,暂不支持查看')
        return
      }

      const data = {
        groupData: row[form.groupName] == '合计' ? null : row[form.groupName],
        xdata: item == 'countMath' ? null : item
      }
      this.detailParams = { visible: true, form: form, data: data }
    }
  }
}
</script>
