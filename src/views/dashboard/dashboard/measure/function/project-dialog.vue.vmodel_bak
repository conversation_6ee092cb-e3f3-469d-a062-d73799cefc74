<template>
  <el-dialog :close-on-click-modal="false" title="数据详情" v-model:visible="visible" :before-close="close">
    <div>
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        min-height="200"
        max-height="300"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column show-overflow-tooltip field="name" title="流水线名称">

          <template slot-scope="scope">
            {{ scope.row.echoMap.pipelineId && scope.row.echoMap.pipelineId.name }}

          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="applicationName"
          title="服务应用"
        >
          <template slot-scope="scope">

            {{ scope.row.echoMap.applicationId && scope.row.echoMap.applicationId.name }}

          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="status"
          title="执行结果"
          width="80"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.status == 'FAILED'" class="executeResult">
              <i class="el-icon-error" style="color: #EA6362" /><span class="ml-1">失败</span>
            </div>
            <div v-if="scope.row.status == 'IN_PROGRESS'" class="executeResult">
              <i class="el-icon-loading" style="color: var(--main-theme-color,#3e7bfa)" /><span class="ml-1">执行中</span>
            </div>
            <div v-if="scope.row.status == 'SUCCESS'" class="executeResult">
              <i class="el-icon-success" style="color: #3CB540" /><span class="ml-1">成功</span>
            </div>
            <div v-if="!scope.row.status" class="executeResult">
              <i
                class="el-icon-question"
                style="color: #ADB0B8"
              /><span class="ml-1">未测</span>
            </div>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" style="position: static;" @update="getInitTableData" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPipDetail } from '@/api/vone/dashboard/index'
import dayjs from 'dayjs'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    queryType: {
      type: String,
      default: ''
    },
    layout: {
      type: Object,
      default: () => {}
    },
    dateValue: {
      type: Array,
      default: null
    }

  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      prioritList: []
    }
  },
  computed: {

  },
  mounted() {
    this.getInitTableData()
  },
  methods: {

    // 初始化进入页面列表
    async getInitTableData(e, type) {
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: {
          projectIds: this.layout.projectId || [],
          orgIds: this.layout.orgId || [],
          productsetIds: this.layout.productId || [],
          startDateStr: dayjs(this.dateValue[0]).format('YYYY-MM'),
          endDateStr: dayjs(this.dateValue[1]).format('YYYY-MM'),
          queryType: this.queryType
        }
      }

      this.tableLoading = true
      // this.$set(this.formData, 'projectId', this.$route.params.id)

      const res = await getPipDetail(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },

    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style scoped lang="scss">
.topBox {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  color: #2C2E36;
  width: 180px;
  a {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }
  span {
    margin-left: 8px;
    font-size: 26px;
font-style: normal;
font-weight: 700;
  }
}
:deep(.vxe-table--body-wrapper ) {
  height: 200px!important;
}
</style>
