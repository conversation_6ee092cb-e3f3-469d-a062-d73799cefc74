<template>
  <el-dialog :close-on-click-modal="false" title="数据详情" v-model:visible="visible" :before-close="close">

    <div v-if="type == 'trend'" style="margin-bottom:20px">
      <div class="topBox">
        <a style="background-color:#F5BF1D" />
        <span :class="[valueData.type == '1' ? 'showTitle': '']">存量</span>
        <span :style="{ 'font-size':valueData.type == '1'? '26px' : '18px', 'font-weight': 700 }">
          {{ valueData &&valueData.residue }}
        </span>
      </div>
      <div class="topBox">
        <a style="background-color:#3E7BFA" />
        <span :class="[valueData.type == '0' ? 'showTitle': '']">新增</span>
        <span :style="{'font-size':valueData.type == '0'? '26px' : '18px', 'font-weight': 700 }">
          {{ valueData &&valueData.add }}
        </span>
      </div>
      <div class="topBox">
        <a style="background-color:#18BF82" />
        <span :class="[valueData.type == '2' ? 'showTitle': '']">完成</span>
        <span :style="{'font-size':valueData.type == '2'? '26px' : '18px', 'font-weight': 700 }">
          {{ valueData &&valueData.close }}
        </span>
      </div>
    </div>
    <div>
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        min-height="200"
        max-height="300"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="标题" field="name" min-width="240" fixed="left" class-name="name_col custom-title-style" show-overflow="ellipsis" tree-node>
          <template #default="{ row }">
            <el-tooltip v-if="!row.groupType" v-showWorkItemTooltips :content="row.code + ' ' + row.name" placement="top-start" :visible-arrow="false">
              <span class="custom-title-main" @click="goto(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}"
                />
                <span class="custom-title-style-text">{{ row.code + " " + row.name }}</span>
              </span>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="160">
          <template #default="{row}">
            <issueStatus v-if="row&&!row.groupType" :key="Date.now()" :workitem="row" :no-permission="!$permission('project_issue_flow')" />
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{row}">
            <vone-user-avatar :avatar-path="leadingUser(row).avatarPath" :name="leadingUser(row).name" />
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <vone-user-avatar :avatar-path="handleUser(row).avatarPath" :name="handleUser(row).name" />
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{row}">
            <vone-user-avatar :avatar-path="putUser(row).avatarPath || ''" :name="putUser(row).name || ''" />
          </template>
        </vxe-column>
        <vxe-column title="计划开始时间" field="planStime" width="180">
          <template #default="{row}">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="180">
          <template #default="{row}">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="180">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="150">
          <template #default="{row}">
            <span v-if="prioritData(row)">
              <i :class="`iconfont ${prioritData(row).icon}`" :style="{color: prioritData(row).color, fontSize: '16px', paddingRight: '6px'}" />
              {{ prioritData(row).name }}
            </span>
            <span v-else>""</span>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" style="position: static;" @update="gettabledata" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDemandDetail, getDemandPaiDetail, getDemandTrendDetail } from '@/api/vone/dashboard/index'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import dayjs from 'dayjs'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
export default {
  components: { issueStatus },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    queryType: {
      type: String,
      default: ''
    },
    layout: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    },
    date: {
      type: String,
      default: ''
    },
    valueData: {
      type: Object,
      default: () => {}
    },
    typeData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      prioritList: []
    }
  },
  computed: {
    handleUser() {
      return function(row) {
        return row?.echoMap?.handleBy || {}
      }
    },
    leadingUser() {
      return function(row) {
        return row?.echoMap?.leadingBy || {}
      }
    },
    putUser() {
      return function(row) {
        return row?.echoMap?.putBy || {}
      }
    },
    prioritData() {
      return function(row) {
        return this.prioritList.find(item => item.code == row.priorityCode) || null
      }
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (this.type == 'count') {
          this.getInitTableData(val)
        } else if (this.type == 'trend') {
          this.getTrendTableData(val)
        } else {
          this.getPieTableData(val)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getPrioritList()
  },
  methods: {
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    gettabledata() {
      if (this.type == 'count') {
        this.getInitTableData(this.layout)
      } else if (this.type == 'trend') {
        this.getTrendTableData(this.layout)
      } else {
        this.getPieTableData(this.layout)
      }
    },
    // 初始化进入页面列表
    async getInitTableData(data) {
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: {
          projectIds: data?.projectId || [],
          orgIds: data?.orgId || [],
          productsetIds: data?.productId || [],
          date: {
            end: dayjs(data?.time && data.time[1]).format('YYYY-MM-DD'),
            start: dayjs(data?.time && data.time[0]).format('YYYY-MM-DD')
          },
          queryType: this.queryType || ''
        }
      }

      this.tableLoading = true
      // this.$set(this.formData, 'projectId', this.$route.params.id)

      const res = await getDemandDetail(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    // 初始化进入页面列表
    async getPieTableData(data) {
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: {
          projectIds: data.projectId || [],
          orgIds: data.orgId || [],
          productsetIds: data.productId || [],
          date: {
            end: dayjs(data?.time && data.time[1]).format('YYYY-MM-DD'),
            start: dayjs(data?.time && data.time[0]).format('YYYY-MM-DD')
          },
          queryType: this.queryType || '',
          priorityCode: this.typeData?.valueCode ? [this.typeData.valueCode] : []
        }
      }
      this.tableLoading = true
      const res = await getDemandPaiDetail(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    // 初始化进入页面列表
    async getTrendTableData(data) {
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: {
          projectIds: data.projectId || [],
          orgIds: data.orgId || [],
          productsetIds: data.productId || [],
          queryType: this.queryType || '',
          date: {
            end: dayjs(this.date).format('YYYY-MM-DD'),
            start: dayjs(this.date).format('YYYY-MM-DD')
          }
        }
      }

      this.tableLoading = true

      const res = await getDemandTrendDetail(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    close() {
      this.$emit('update:visible', false)
    },
    async goto(row) {
      if (this.layout.latitude == 'product') {
        const newpage = await this.$router.resolve({ path: `/reqmcenter/require/requireList`, query: {
          showDialog: true,
          queryId: row?.id
        }})
        const winOpen = window.open(newpage.href, '_blank')
        winOpen.opener = null
      } else {
        const projectKey = row.echoMap?.projectInfo?.code || row.echoMap?.productId?.code
        const projectTypeCode = row.echoMap?.projectInfo?.typeCode || row.echoMap?.productId?.typeCode
        const projectId = row.echoMap?.projectInfo?.id || row.echoMap?.productId?.id
        const newpage = await this.$router.resolve({ path: `/project/issue/${projectKey}/${projectTypeCode}/${projectId}`, query: {
          showDialog: true,
          queryId: row?.id,
          rowTypeCode: row?.typeCode,
          stateCode: row?.stateCode,
          projectId: projectId
        }})
        const winOpen = window.open(newpage.href, '_blank')
        winOpen.opener = null
      }
    }
  }
}
</script>
<style scoped lang="scss">
.topBox {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  color: #2C2E36;
  width: 180px;
  a {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }
  span {
    margin-left: 8px;
    // font-size: 26px;
    font-style: normal;
    // font-weight: 700;
  }
  .showTitle {
    font-size: 20px;
    font-weight: 700;
  }
}
:deep(.vxe-table--body-wrapper ) {
  height: 200px!important;
}
</style>
