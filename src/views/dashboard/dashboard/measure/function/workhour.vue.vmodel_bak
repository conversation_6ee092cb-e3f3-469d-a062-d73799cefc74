<template>
  <div style="width: 100%;height: 100%">
    <vone-echarts-card :title="'工时概览'" class="dashboard_card">
      <span slot="tips" style="height: 24px;">
        <el-tooltip placement="right">
          <div slot="content">
            预估工时总数：按工作项预估工时对统计时间段内的所有预估工时求和。<br>
            登记工时总数：按工时填报日期对统计时间段内的所有登记工时求和。
          </div>
          <i
            style="color: var(--font-disabled-color); margin-left: 4px;"
            class="iconfont el-icon-tips-info-circle"
          />
        </el-tooltip>
      </span>
      <div class="box" style="margin-top: 12px" @click="getDetail('hour')">
        预估工时总数 <span>{{ countData.estimateHour }}</span> 小时
      </div>
      <div class="box" @click="getDetail('check')">
        登记工时总数 <span>{{ countData.filledHour }}</span> 小时
      </div>
    </vone-echarts-card>
    <detailDialog v-if="detailParams.visible" :layout="layout" :query-type="detailParams.queryType" v-bind="detailParams" v-model:visible="detailParams.visible" />
  </div>
</template>
<script>
import { getHourCount } from '@/api/vone/dashboard/index'
import detailDialog from './hour-dialog.vue'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog
  },
  props: {
    layout: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      countData: {},
      detailParams: { visible: false }
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (val && val.time && ((val.latitude == 'org' && val.orgId?.length > 0) || (val.latitude == 'project' && val.projectId?.length > 0) || (val.latitude == 'product' && val.productId?.length > 0))) {
          this.getData(val)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {

  },
  methods: {
    getData(data) {
      getHourCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        startDate: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        endDate: dayjs(data.time && data.time[1]).format('YYYY-MM-DD')
      }).then(res => {
        if (res.isSuccess) {
          this.countData = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getDetail(type) {
      this.detailParams = {
        visible: true,
        queryType: type
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dashboard_card {
	height: 100%;
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
	.box {
		background: #F7F7F7;
		border: 1px solid #F0F0F0;
		border-radius: 4px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    width: 100%;
    margin-bottom: 20px;
    span {
      font-size: 26px;
      font-style: normal;
      font-weight: 700;
      line-height: 34px;
      cursor: pointer;
    }
	}
}
</style>
