<template>
  <el-dialog :close-on-click-modal="false" :title="formInfo ? formInfo.title : '通用配置'" v-model:visible="pipelinevisible" @closed="close">
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="标题" label-width="80px">
        <el-input v-model="formData.title" style="width: 90%" />
      </el-form-item>
      <el-form-item v-if="formInfo.title == '流水线统计'" prop="dateType" label="项目间隔" label-width="80px">
        <el-select v-model="formData.dateType" clearable style="width:90%" filterable>
          <el-option v-for="item in dateTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="save"
      >确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import _ from 'lodash'
export default {
  name: 'PipelineDialog',
  props: {
    isEditForm: Boolean,
    pipelinevisible: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: null
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      formData: {
        title: this.formInfo.title

      },
      rules: {
        dateType: [{ required: true, message: '请选择时间间隔', trigger: 'blur' }]
      },
      dateTypeList: [
        {
          value: 'LAST_DAY',
          label: '当天'
        },
        {
          value: 'LAST_WEEK',
          label: '最近1周'
        },
        {
          value: 'LAST_MONTH',
          label: '最近1月'
        },
        {
          value: 'LAST_QUARTER',
          label: '最近1季'
        },
        {
          value: 'LAST_YEAR',
          label: '最近1年'
        }
      ],
      cmdbTypeList: [
        {
          value: '主机',
          label: '主机'
        },
        {
          value: '服务应用数量',
          label: '服务应用数量'
        },
        {
          value: '应用组件',
          label: '应用组件'
        },
        {
          value: '数据库组',
          label: '数据库组'
        }
      ],
      envList: []
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.formData = this.formInfo
    }
    this.getEnvList()
  },
  methods: {
    changeProject(val) {
      this.getPlanList(val)
    },
    // 获取环境标签
    getEnvList() {
      apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      }).then(res => {
        if (res.isSuccess) {
          this.envList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.formData.id && item.widgetCode == this.formData.key) {
            item.queryParams = JSON.stringify({ ...this.formData })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.formData }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:pipelinevisible', false)
      this.$emit('success')
    },
    close() {
      this.$emit('update:pipelinevisible', false)
      this.$refs.form.resetFields()
    }
  }
}
</script>

