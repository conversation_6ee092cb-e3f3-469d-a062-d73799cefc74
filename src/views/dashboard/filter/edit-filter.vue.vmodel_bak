<template>
  <el-dialog class="dialogContainer" :title="title" v-model:visible="visible" width="456px" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <!-- 表单部分 -->
    <el-form ref="filterForm" :model="filterForm" :rules="rules" label-position="top">
      <el-form-item label="名称" prop="name">
        <el-input v-model="filterForm.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="权限" prop="scope">
        <el-radio-group v-model="filterForm.scope">
          <el-radio :label="0">公有</el-radio>
          <!-- <el-radio :label="1">项目</el-radio> -->
          <el-radio :label="2">私有</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="filterForm.sort" type="number" :min="1" placeholder="请输入排序" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="filterForm.description" type="textarea" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="saveFilter">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { opeartionFilter } from '@/api/vone/dashboard/filter'
import { cloneDeep } from 'lodash'
export default {
  props: {
    visible: Boolean,
    rowData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      filterForm: {
        name: '',
        scope: 0,
        description: '',
        sort: 1,
        tableType: 'work_item_entity',
        type: 'ISSUE'
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }],
        role: [{ required: true, message: '请输入角色编号', trigger: 'blur' }]
      },
      title: '新增筛选器',
      loading: false
    }
  },
  mounted() {
    if (this.rowData) {
      this.title = '编辑筛选器'
      this.filterForm = cloneDeep(this.rowData)
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$emit('update:rowData', null)
    },
    async saveFilter() {
      try {
        await this.$refs.filterForm.validate()
      } catch (e) {
        return
      }
      this.loading = true
      let res = null
      if (!this.rowData) {
        res = await opeartionFilter(this.filterForm, 'post')
      } else {
        res = await opeartionFilter(this.filterForm, 'put')
      }
      this.loading = false
      if (res.isSuccess) {
        this.$emit('success')
        this.close()
      }
    }
  }
}
</script>
