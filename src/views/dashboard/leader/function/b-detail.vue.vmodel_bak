<template>
  <div class="box" style="background: rgba(62, 123, 250, 0.04)">
    <!--rgba(247, 161, 74, 0.08)-->
    <!-- rgba(247, 161, 74, 0.04) -->
    <div class="title" style="background: rgba(62, 123, 250, 0.08)">{{ data.name }}</div>
    <div class="detail">
      <div><span>组织机构：</span>{{ (data.org && data.org.name) || data.name }}</div>
      <div><span>研发产品：</span>{{ product && product.join('、') }}</div>
      <div style="display: flex">
        <span>项目经理：</span>
        <vone-user-avatar
          :avatar-path="data.leadingUser && data.leadingUser.avatarPath"
          :name="data.leadingUser && data.leadingUser.name"
        />
      </div>
      <div><span>现有人员规模：</span>{{ data.userNum }}</div>
      <div>
        <span>进度：</span>
        <span @click="allIssue" style="cursor: pointer">
          <el-progress style="width: 100%; display: inline-block" :percentage="data.schedule"></el-progress> </span
        ><el-tooltip placement="right">
          <div slot="content">
            <span> 进度=已完成需求/需求总数*100% </span>
          </div>
          <i style="color: var(--font-disabled-color)" class="iconfont el-icon-tips-info-circle" />
        </el-tooltip>
      </div>
      <div>
        <span>风险：</span>
        <span @click="allRisk" style="width: 20px; cursor: pointer">
          {{ data.riskNum }}
        </span>
        <el-tooltip placement="right">
          <div slot="content">
            <span> 参数为当前项目“未关闭”的风险数 </span>
          </div>
          <i style="color: var(--font-disabled-color)" class="iconfont el-icon-tips-info-circle" />
        </el-tooltip>
      </div>
    </div>
    <detailDialog
      v-if="detailParams.visible"
      v-bind="detailParams"
      v-model:visible="detailParams.visible"
      :type="detailParams.type"
      :layout="layout"
      :query-type="detailParams.queryType"
      :project-id="data.id"
      :org-id="data.org.id"
      :user-id="data.userId"
      :code="'all'"
    />
  </div>
</template>
<script>
import detailDialog from './detail-dialog.vue'
export default {
  components: {
    detailDialog
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    layout: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      product: [],
      detailParams: {
        visible: false
      },
      total: ''
    }
  },
  computed: {
    requirementResult() {
      return this.data?.chartData?.requirementResult || []
    },
    progressPercentage() {
      const total =
        this.requirementResult[0]?.value + this.requirementResult[1]?.value + this.requirementResult[2]?.value
      return total > 0 ? (this.requirementResult[2]?.value / total) * 100 : 0
    }
  },
  mounted() {
    this.product = this.data.productInfos?.map(item => {
      return item.name
    })
  },
  methods: {
    allIssue() {
      this.detailParams = { visible: true, type: 'issue' }
    },
    allRisk() {
      this.detailParams = { visible: true, type: 'risk' }
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  border-radius: 4px;
}
.title {
  height: 38px;
  line-height: 38px;
  padding-left: 16px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
}
.detail {
  padding: 12px 16px;
  div {
    margin-bottom: 8px;
    line-height: 22px;
    span {
      color: #091961;
      width: 120px;
      display: inline-flex;
    }
  }
}
.iconfont {
  color: var(--font-disabled-color);
  margin-left: 4px;
  vertical-align: middle;
}
:deep(.el-progress-bar) {
  margin-right: -59px;
}
</style>
