<template>
  <div>
    <el-dialog
      v-loading="loading"
      title="设置状态轮巡策略"
      v-model:visible="visible"
      width="30%"
      :close-on-click-modal="false"
      :before-close="onClose"
      v-on="$listeners"
    >
      <el-form :model="strageForm">
        <el-form-item label="状态轮巡策略" prop="monitorCron">
          <el-select v-model="strageForm.monitorCron" style="width:70%">
            <el-option label="从不" value="0" />
            <el-option label="每5分钟" value="5" />
            <el-option label="每10分钟" value="10" />
            <el-option label="每30分钟" value="30" />
            <el-option label="每1小时" value="60" />
            <el-option label="每6小时" value="360" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiCmdbHostMonitorSelectByHostId,
  apiCmdbHostMonitorSave } from '@/api/vone/cmdb/host'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    ips: {
      type: Array,
      default: undefined
    }
  },
  data() {
    return {
      loading: false,
      strageForm: {
        monitorCron: '5'
      },
      saveLoading: false
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      // this.getInfo()
      this.strageForm.monitorCron = '5'
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    // 查询轮巡策略
    async getInfo() {
      if (this.ips.length !== 1) {
        return
      }
      this.loading = true
      const { success, data, message } = await apiCmdbHostMonitorSelectByHostId(this.ips[0])
      this.loading = false
      if (!success) {
        return this.$message.error(message)
      }
      if (data) {
        this.strageForm.monitorCron = data.cronTag
      }
    },
    // 保存轮巡策略
    async save() {
      this.saveLoading = true
      // let cron = ''
      if (parseInt(this.strageForm.monitorCron) > 0) {
        if (parseInt(this.strageForm.monitorCron) < 60) {
          // cron = '0 0/' + this.strageForm.monitorCron + ' * * * ? *'
        } else {
          // cron = '0 0 0/' + parseInt(this.strageForm.monitorCron) / 60 + ' * * ? *'
        }
      }
      const { isSuccess, msg } = await apiCmdbHostMonitorSave({
        hostIds: this.ips,
        patrolStrategy: this.strageForm.monitorCron
        // cron: cron,
        // cronTag: parseInt(this.strageForm.monitorCron) === 0 ? undefined : this.strageForm.monitorCron
      })
      this.saveLoading = false
      if (!isSuccess) {
        this.saveLoading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }
}
</script>
