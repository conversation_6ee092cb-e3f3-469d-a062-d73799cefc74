<template>
  <div>
    <el-dialog title="同步服务器" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" v-on="$listeners">
      <el-form label-width="70px" :model="saltform">
        <el-form-item v-if="saltIdList.length > 1" label="Salt引擎" prop="salt">
          <el-select v-model="saltform.salt" placeholder="请选择Salt引擎" style="width: 50%">
            <el-option label="全部" value="" />
            <el-option v-for="item in saltIdList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <span v-if="hostKey.length == 0 && saltIdList.length > 1" style="color: #f5222d">请选择引擎进行同步，关闭此弹框或刷新页面将停止后续服务器同步</span>
      <span v-else-if="overNumber != hostKey.length || hostKey.length == 0" style="color: #f5222d">请等待同步完成，关闭此弹框或刷新页面将停止后续服务器同步</span>
      <p v-else-if="overNumber == hostKey.length">
        同步完成，总共同步{{ total }}台服务器，其中成功{{
          successNumber
        }}台，失败{{ faileNumber }}台
      </p>
      <el-progress :percentage="sum" :format="format" class="mt-3" />
      <div
        class="textera mt-3"
        style="
          min-height: 60px;
          border: 1px solid rgb(221, 221, 221);
          padding: 15px;
          padding-bottom: 0;
        "
      >
        <el-row>
          <el-col v-for="item in hostKey" :key="item.ip" :md="6">
            <p>
              <!-- :type="successStatus[item.success].type" -->
              <el-tag>
                {{ item }}
                <!-- <i :class="successStatus[item.success].icon" /> -->
              </el-tag>
            </p>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="hostKey.length > 0 && overNumber != hostKey.length" @click="cancle">取消</el-button>
        <el-button v-if="overNumber == 0 || overNumber == hostKey.length" @click="onClose">关闭</el-button>
        <el-button v-if="overNumber == 0 && saltIdList.length > 1" type="primary" @click="asynHost(saltform.salt)">同步</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiCmdbHostSelectAllSyncHostKey,
  apiCmdbHostSyncHostById
} from '@/api/vone/cmdb/host'
import {
  apiBaseEngineNoPage
} from '@/api/vone/base/engine'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    ips: {
      type: Array,
      default: undefined
    }
  },
  data() {
    return {
      saltform: {
        salt: ''
      },
      percentage: 0,
      hostKey: [],
      total: 1,
      successNumber: 0,
      faileNumber: 0,
      overNumber: 0,
      idList: [],
      saltIdList: [],
      dataList: [],
      successStatus: {
        0: { icon: 'el-icon-loading', type: null },
        1: { icon: 'el-icon-circle-check', type: 'success' },
        2: { icon: 'el-icon-circle-close', type: 'danger' },
        3: { icon: '', type: null }
      }
    }
  },
  computed: {
    sum() {
      const percentage = (this.overNumber / this.total) * 100
      return percentage
    }
  },
  watch: {
    visible(v) {
      if (!v) return
    }
  },
  mounted() {
    this.selectEngineList()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$emit('success')
    },
    format() {
      return `${this.overNumber}/${this.total}`
    },
    cancle() {
      this.$confirm('确定取消同步吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$emit('update:visible', false)
      })
    },
    async selectEngineList() {
      const res =
        await apiBaseEngineNoPage({
          instance: 'SALT_STACK'
        })
      if (!res.isSuccess) {
        return
      }
      this.saltIdList = res.data
      if (this.saltIdList.length === 1) {
        this.asynHost(this.saltIdList[0].id)
      }
    },
    async asynHost(val) {
      const res = await apiCmdbHostSelectAllSyncHostKey(
        val ? [val] : []
      )

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }

      // const IP = res.data.map(R=>({}))
      const hosts = res.data
      // const hosts = res.data.map(([ip, id]) => ({ ip, id, success: 3 }))

      // this.hostKey = res.data

      //   res.data.forEach(element => {

      //   });

      const list = []
      res.data.map(item => {
        item.minionKeys.map(itm => {
          list.push(itm)
        })
      })

      this.hostKey = list

      this.total = Number(list.length)

      this.overNumber = 1
      const result = []
      hosts.forEach(async(host) => {
        host.success = 0

        host.minionKeys.forEach(async element => {
          var data = await apiCmdbHostSyncHostById(host.engine.id, element)
          this.$set(host, 'success', data.isSuccess ? 1 : 2)

          result.push(host.success)

          this.successNumber = result.filter((h) => h === 1).length
          this.faileNumber = result.filter((h) => h === 2).length
          this.overNumber = result.filter((h) => h !== 0).length
        })
      })
      this.$message.success('同步服务器成功')
      this.$emit('success')
    }
  }
}
</script>
