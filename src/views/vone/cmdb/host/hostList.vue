<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="host_table"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          show-basic
          v-model:extra="extraData"
          :table-ref="$refs['host_table']"
          @getTableData="getTableList"
        />
      </template>
      <template slot="actions">

        <el-button icon="iconfont el-icon-tips-plus-circle" type="primary" class="ml-16" :disabled="!$permission('cmdb_host_add')" @click="addHost">新增</el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getTableList"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{height: tableHeight}">
      <vxe-table
        ref="host_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="refreshloading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="IP" field="ip" sortable>
          <template #default="{ row }">
            <a ref="drawer" @click="showInfo(row.id)">
              {{ row.ip }}
            </a>
          </template>
        </vxe-column>
        <vxe-column field="name" title="主机名" />
        <vxe-column field="state" title="状态">
          <template #default="scope">
            <el-button v-if="scope.row.state" type="text" :disabled="!$permission('cmdb_host_refresh')" class="safe" @click="refreshHostList(scope)">
              <i class="el-icon-success mr-1" />正常
            </el-button>
            <el-button v-else type="text" :disabled="!$permission('cmdb_host_refresh')" class="danger" @click="refreshHostList(scope)">
              <i class="el-tips-exclamation-circle-fillmr-1" />异常
            </el-button>
          </template>
        </vxe-column>
        <vxe-column field="status" title="管理状态">
          <template #default="{row}">
            <span v-if="row.status"> {{ row.status.desc }}</span>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column title="来源" field="createType">
          <template #default="{row}">
            <span v-if="row.createType && row.createType.desc"> {{ row.createType.desc }}</span>
            <span v-else>{{ row.createType }}</span>
          </template>
        </vxe-column>
        <vxe-column title="服务器类型" field="type">
          <template #default="{row}">
            <span v-if="row.type">{{ row.type.desc }}</span>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column title="所属机构" field="orgId">
          <template #default="{row}">
            <span v-if="row.orgId && row.echoMap && row.echoMap.orgId">{{ row.echoMap.orgId.name }}</span>
            <span v-else>--</span>
          </template>
        </vxe-column>

        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row,rowIndex }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button type="text" :disabled="!$permission('cmdb_host_edit')" icon="iconfont el-icon-application-edit" @click="updateHost(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button type="text" :disabled="!$permission('cmdb_host_delete')" icon="iconfont el-icon-application-delete" @click="deleteHostById(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e(row)">
              <el-button type="text" icon="iconfont el-icon-application-more" class="operation-dropdown" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :disabled="!$permission('cmdb_host_authority')" icon="iconfont el-icon-application-user-permission" :command="() => allocation(row)">
                  <span>权限分配</span>
                </el-dropdown-item>
                <el-dropdown-item :disabled="!$permission('cmdb_host_accept')||getStatus(rowIndex)" icon="el-icon-s-platform" :command="() => acceptHost(row)">
                  <span>纳管</span>
                </el-dropdown-item>
                <el-dropdown-item :disabled="!$permission('cmdb_host_offine')" icon="el-icon-warning-outline" :command="() => offLineHost(row)">
                  <span>下线</span>
                </el-dropdown-item>
                <el-dropdown-item :disabled="!$permission('cmdb_host_strage')" icon="el-icon-sort" :command="() => strageHost(row)">
                  <span>设置状态轮巡策略</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableList" />

    <!-- 同步 -->
    <sync-host-dialog v-if="syncHostData.visible" v-model="syncHostData.visible" :ips="syncHostData.ips" @success="getTableList" />
    <!-- 状态轮询 -->
    <strage-host-dialog v-if="strageHostData.visible" v-model="strageHostData.visible" :ips="strageHostData.ips" @success="getTableList" />
    <!-- 导出服务器 -->
    <export-host-dialog v-if="exportHostData.visible" v-model="exportHostData.visible" :params="exportHostData.params" @success="getTableList" />
    <!-- 删除完成弹出对话框表格 -->
    <deleteDialog v-bind="deleteParam" v-model="deleteParam.visible" @success="getTableList" />

    <!-- 基本信息抽屉 -->
    <basicInfoDrawer v-if="basicDrawerParam.visible" v-bind="basicDrawerParam" v-model="basicDrawerParam.visible" :table-list="tableList" />
    <!-- 权限分配抽屉 -->
    <hostDivision v-if="allocationDrawerParam.visible" v-bind="allocationDrawerParam" v-model="allocationDrawerParam.visible" />

  </page-wrapper>
</template>
<script>

import {
  apiCmdbHostSeletByParams,
  apiCmdbHostDeleteByIds,
  apiCmdbHostRefreshByIds,
  apiCmdbHostAcceptByIds,
  apiCmdbHostOffLineByIds,
  apiBaseDictSelectByParentKey
} from '@/api/vone/cmdb/host'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import syncHostDialog from './function/sync-host'
import strageHostDialog from './function/strage-host'
import exportHostDialog from './function/export-host'
import deleteDialog from './function/delete-dialog'
import basicInfoDrawer from './function/basicInfo-drawer'
import hostDivision from './function/host-division.vue'
// import setData from '@/utils/setData'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    syncHostDialog,
    strageHostDialog,
    exportHostDialog,
    deleteDialog,
    basicInfoDrawer,
    hostDivision
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {}, defaultFileds: [
        {
          key: 'status',
          name: '管理状态',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择管理状态'
        },
        {
          key: 'operatingSystemKernel',
          name: '操作系统内核',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择操作系统内核'
        },
        {
          key: 'ip',
          name: '主机IP',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入主机IP'
        },
        {
          key: 'type',
          name: '服务器类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择服务器类型'
        },
        {
          key: 'createType',
          name: '创建方式',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择创建方式'
        }
      ],
      tableList: [],
      loading: true,
      refreshloading: false,
      deleteParam: { visible: false },
      allocationDrawerParam: {
        visible: false
      },
      basicDrawerParam: { visible: false },
      tableData: {},
      tableSelected: [],
      formData: {
        status: '',
        operatingSystemKernel: '',
        ip: '',
        type: ''
      },
      saltTagDatas: [],
      actions: [
        // {
        //   name: '导出服务器',
        //   icon: 'el-icon-plus',
        //   fn: this.exportHost
        // },
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete',
          fn: this.deleteHost,
          disabled: !this.$permission('cmdb_host_delete')
        },
        {
          name: '设置状态轮巡策略',
          icon: 'iconfont el-icon-application-setting',
          fn: this.strageHost,
          disabled: !this.$permission('cmdb_host_strage')
        },
        {
          name: '纳管',
          icon: 'iconfont el-icon-application-cashier',
          fn: this.acceptHost,
          disabled: !this.$permission('cmdb_host_accept')
        },
        {
          name: '下线',
          icon: 'iconfont el-icon-application-offline',
          fn: this.offLineHost,
          disabled: !this.$permission('cmdb_host_offine')
        },
        {
          name: '同步',
          icon: 'iconfont el-icon-application-swap',
          fn: this.syncHost,
          disabled: !this.$permission('cmdb_host_sync')
        },
        {
          name: '刷新',
          icon: 'iconfont el-icon-application-renew',
          fn: this.refreshHost,
          disabled: !this.$permission('cmdb_host_refresh')
        }

      ],
      syncHostData: {
        visible: false,
        ips: undefined
      },
      strageHostData: {
        visible: false,
        ips: undefined
      },
      exportHostData: {
        visible: false,
        params: {}
      },
      hostQuickEntry: this.$route.query.hostQuickEntry
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getDIcHostStatus() // 管理状态
    this.getDIcOperatingSystemType() // 系统内核版本
    this.getDicHostType() // 服务器类型
    // this.getSaltTagDatas()
    this.getDicHostCreateType() // 创建方式
    // this.getTableList()
  },
  methods: {
    getStatus(index, row) {
      return this.tableData.records[index].createType && this.tableData.records[index].createType.code === 'OMP'
    },
    // 纳管状态
    async getDIcHostStatus() {
      const res = await apiBaseDictEnumList(
        ['HostStatus']
      )
      if (!res.isSuccess) {
        return
      }
      res.data.HostStatus.forEach(element => {
        element.name = element.label
        element.code = element.value
        element.id = element.value
      })

      this.setData(this.defaultFileds, 'status', res.data.HostStatus)
    },
    // 系统内核版本
    async getDIcOperatingSystemType() {
      const res = await apiBaseDictEnumList(
        ['OperatingSystemType']
      )
      if (!res.isSuccess) {
        return
      }
      res.data.OperatingSystemType.forEach(element => {
        element.name = element.label
        element.code = element.value
        element.id = element.value
      })
      this.setData(this.defaultFileds, 'operatingSystemKernel', res.data.OperatingSystemType)
    },
    // 服务器类型
    async getDicHostType() {
      const res = await apiBaseDictEnumList(
        ['HostType']
      )
      if (!res.isSuccess) {
        return
      }
      res.data.HostType.forEach(element => {
        element.name = element.label
        element.code = element.value
        element.id = element.value
      })
      this.setData(this.defaultFileds, 'type', res.data.HostType)
    },

    // HostCreateType
    async getDicHostCreateType() {
      const res = await apiBaseDictEnumList(
        ['HostCreateType']
      )
      if (!res.isSuccess) {
        return
      }

      res.data.HostCreateType.forEach(element => {
        element.name = element.label
        element.code = element.value
        element.id = element.value
      })
      this.setData(this.defaultFileds, 'createType', res.data.HostCreateType)
    },

    // 获取table 选中的行
    selecteTableData(val) {
      this.tableSelected = val
    },
    // 连接终端
    connect(row) {
      this.$router.push({
        name: 'cmdb_host_ssh',
        params: { id: row.id }
      })
    },
    selectAllEvent({ checked }) {
      this.tableSelected = this.$refs.host_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.tableSelected = this.$refs.host_table.getCheckboxRecords()
    },
    async getTableList() {
      this.refreshloading = true
      let params = {}
      const tableAttr = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...tableAttr,
        ...sortObj,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await apiCmdbHostSeletByParams(params)
      this.refreshloading = false
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }

      this.tableData = res.data
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
    },
    // refresh() {
    //   this.$refs['hostTable'].refresh(true)
    // },
    async getSaltTagDatas() {
      this.loading = true
      const res = await apiBaseDictSelectByParentKey(
        'base_system_tag_host'
      )
      if (!res.isSuccess) {
        this.loading = false
        return
      }
      this.saltTagDatas = res.data
      this.loading = false
    },
    addHost() {
      this.$router.push({
        name: 'cmdb_host_add'
      })
    },
    updateHost(data) {
      this.$router.push({
        name: 'cmdb_host_edit',
        params: { id: data.id }
      })
    },
    async deleteHostById(row) {
      await this.$confirm(`确定删除【${row.ip}】服务器吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
      this.loading = false
      const res = await apiCmdbHostDeleteByIds(
        [row.id]
      )
      if (!res.isSuccess) {
        this.loading = false
        return this.$message.error(res.msg)
      }
      this.$message.success('删除成功')
      this.getTableList()
      // if (data[0].success == true) {
      //   this.loading = false
      //   this.$message.success(res.msg)
      //   this.getTableList()
      // } else {
      //   this.loading = false
      //   this.$message.error(data[0].res.msg)
      // }
    },
    async deleteHost(scope) {
      if (this.tableSelected.length === 0) {
        return this.$message.error('请选择要删除的服务器')
      }
      // const IP = this.tableSelected.map(r => r.ip).join(',')
      await this.$confirm(`确定删除 ${this.tableSelected.length} 条数据吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
      const idsArr = []
      this.tableSelected.forEach((item) => {
        idsArr.push(item.id)
      })
      this.loading = true
      const res = await apiCmdbHostDeleteByIds(
        idsArr
      )

      if (!res.isSuccess) {
        this.loading = false
        return this.$message.error(res.msg)
      }
      this.$message.success('删除成功')
      // this.deleteParam = { visible: true, result: res.data }
      this.loading = false
      // this.refresh()
      this.getTableList()
    },
    async refreshHostList(scope) {
      // if (scope.row.createType.code === 'MANUAL_ENTRY') {
      //   return this.$message.error(
      //     '服务器' + scope.row.ip + '来源为人工录入,无法刷新状态'
      //   )
      // }

      this.loading = true
      const res = await apiCmdbHostRefreshByIds(
        [scope.row.id]
      )
      this.loading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.$message.success('服务器状态刷新成功')
      this.getTableList()
    },
    async refreshHost(scope) {
      let idsArr = []
      if (scope == 'refresh') {
        idsArr = ['all']
      } else if (scope === undefined) {
        if (this.tableSelected.length === 0) {
          return this.$message.error('请选择要刷新的服务器')
        }
        // const idsCreateArr = []
        // this.tableSelected.forEach((item) => {
        //   if (item.createType.code === 'MANUAL_ENTRY') {
        //     idsCreateArr.push(item.id)
        //   }
        //   idsArr.push(item.id)
        // })
        // if (idsCreateArr.length !== 0) {
        //   return this.$message.error(
        //     '服务器' + idsCreateArr.join(',') + '来源为人工录入,无法刷新'
        //   )
        // }
        // this.tableSelected = []
      } else {
        idsArr.push(scope.row.id)
      }
      this.refreshloading = true
      const res = await apiCmdbHostRefreshByIds(
        idsArr
      )
      this.tableSelected = []
      if (!res.isSuccess) {
        this.refreshloading = false
        const test = res.msg.split('健康状态失败')
        const arr = []
        test.forEach((item) => {
          if (item.indexOf('成功') === -1 && item.indexOf('刷新服务器') != -1) {
            arr.push(item.substr(item.indexOf('刷新服务器') + 5))
          }
        })
        return this.$message.error('服务器状态刷新失败：' + arr)
      }
      this.refreshloading = false
      this.$message.success('服务器状态刷新成功')
      // this.refresh()
      this.getTableList()
    },
    // 同步
    syncHost(scope) {
      const list = this.tableSelected.filter(r => r.createType && r.createType.code == 'OMP')
      if (list.length) {
        this.$message.warning('当前选中的服务器中含有创建方式为OMP的服务器,请重新选择')
        return
      }

      const ipsArr = []
      this.tableSelected.forEach((item) => {
        if (item.createType && item.createType.code !== 'OMP') {
          ipsArr.push(item.ip)
        }
      })

      this.syncHostData = {
        visible: true,
        ips: ipsArr
      }
    },
    // 下线
    async offLineHost(scope) {
      const idsArr = []
      if (scope === undefined) {
        if (this.tableSelected.length === 0) {
          return this.$message.error('请选择要下线的服务器')
        }
        const idsOffLineArr = []
        this.tableSelected.forEach((item) => {
          if (item.status !== 2) {
            idsOffLineArr.push(item.id)
          }
          idsArr.push(item.id)
        })
        if (idsOffLineArr.length !== 0) {
          return this.$message.error(
            '服务器' + idsOffLineArr.join(',') + '的管理状态不是已分配,无法下线'
          )
        }
      } else {
        idsArr.push(scope.id)
      }
      await this.$confirm('服务器下线后不能恢复,确定下线服务器吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
      this.loading = true
      const res = await apiCmdbHostOffLineByIds(
        idsArr
      )

      if (!res.isSuccess) {
        this.loading = false
        return this.$message.error(res.msg)
      }
      this.$message.success('服务器下线成功')

      this.getTableList()
    },
    // 设置状态轮巡策略
    strageHost(scope) {
      const idsArr = []
      if (scope === undefined) {
        if (!this.tableSelected.length) {
          return this.$message.error('请选择要设置状态轮巡策略的服务器')
        }
        const idsCreateArr = []
        this.tableSelected.forEach((item) => {
          if (item.createType === 1) {
            idsCreateArr.push(item.id)
          }
          idsArr.push(item.id)
        })
        if (idsCreateArr.length !== 0) {
          return this.$message.error(
            '服务器' +
            idsCreateArr.join(',') +
            '来源为人工录入,无法设置状态轮巡策略'
          )
        }
      } else {
        idsArr.push(scope.id)
      }
      this.strageHostData = {
        visible: true,
        ips: idsArr
      }
    },
    // 纳管服务器
    async acceptHost(scope) {
      const list = this.tableSelected.filter(r => r.createType && r.createType.code == 'OMP')
      if (list.length) {
        this.$message.warning('当前选中的服务器中含有创建方式为OMP的服务器,请重新选择')
        return
      }
      const idsArr = []
      if (scope === undefined) {
        if (this.tableSelected.length === 0) {
          return this.$message.error('请选择要纳管的服务器')
        }
        const idsAcceptArr = []
        const idsCreateArr = []
        this.tableSelected.forEach((item) => {
          if (item.status !== 0 && item.createType === 0) {
            idsAcceptArr.push(item.id)
          }
          if (item.createType === 1) {
            idsCreateArr.push(item.id)
          }
          idsArr.push(item.id)
        })
        if (idsCreateArr.length !== 0) {
          return this.$message.error(
            '服务器' + idsCreateArr.join(',') + '来源为人工录入,无法纳管'
          )
        }
        if (idsAcceptArr.length !== 0) {
          return this.$message.error(
            '服务器' + idsAcceptArr.join(',') + '管理状态不是未纳管,无法纳管'
          )
        }
      } else {
        idsArr.push(scope.id)
      }
      this.loading = true
      const res = await apiCmdbHostAcceptByIds(
        idsArr
      )

      if (!res.isSuccess) {
        this.loading = false
        return this.$message.error(res.msg)
      }
      this.$message.success('服务器纳管成功')
      this.getTableList()
    },
    exportHost() {
      let ipStr = ','
      this.tableSelected.forEach(ele => {
        ipStr += ele.ip + ','
      })
      this.exportHostData = {
        visible: true,
        params: { ip: ipStr }
      }
    },
    showInfo(id) {
      this.basicDrawerParam = { visible: true, id: id }
    },
    // 分配
    async allocation(row) {
      this.allocationDrawerParam = {
        visible: true,
        id: row.id
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.danger {
  color: red !important;
}
.safe {
  color: #67c23a !important;
}
.mr-1 {
  margin-left: 2px;
}
</style>
