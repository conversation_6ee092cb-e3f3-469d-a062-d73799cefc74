<template>
  <div>
    <el-card style="margin-bottom:10px">

      <el-row>
        <el-col :span="12">
          <div class="name">{{ ruleForm.name }}</div>
        </el-col>
        <el-col :span="12" style="text-align:right">
          <div>
            <el-button
              type="primary"
              @click="resDivision(id)"
            >权限分配</el-button>
          </div>
        </el-col>
      </el-row>
      <el-form :model="ruleForm" class="formBox">
        <el-row>
          <el-col :span="8">
            <el-form-item label="标识">
              {{ ruleForm.id }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护人">
              {{ ruleForm.responsible }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属机构">
              {{ ruleForm.org }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="代码质量引擎">
              {{ ruleForm.sonarQubeEngineName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="持续集成引擎">
              {{ ruleForm.jenkinsMasterEngineName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="环境">
              <template v-for="(item, index) in regionList">
                <div :key="index" class="region_env" :value="item" />
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="描述">
              {{ ruleForm.description }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否需要资源">
              {{
                ruleForm.envResource == 0 ? "否" : "是"
              }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否需要安全扫描">
              {{
                ruleForm.securityScan == 0 ? "否" : "是"
              }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="系统投产状态">
              {{
                ruleForm.productionState == 0 ? "在建" : "已投产"
              }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="首次投产日期">
              {{
                ruleForm.firstProductionTime
              }}
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
    </el-card>

    <el-card>

      <el-tabs v-model="tabActive" @tab-click="handleClick">
        <el-tab-pane
          v-for="item in types"
          :key="item.type"
          :label="item.name"
          :name="item.type"
        />
      </el-tabs>
      <template>
        <server v-if="tabActive === 'server'" :id="id" />
        <version v-if="tabActive === 'version'" :id="id" />
        <pipeLine v-if="tabActive === 'pipeLine'" :id="id" />
      <!-- <relation v-if="tabActive === 'relation'" :id="id" /> -->

      </template>
    </el-card>
    <!-- 权限分配抽屉 -->
    <division-drawer
      v-bind="divisionDrawerParam"
      v-model:visible="divisionDrawerParam.visible"
      @success="getFullInfo"
    />

  </div>
</template>

<script>
import { apiBaseBusinessSystemSelectById } from '@/api/vone/cmdb/business'
import divisionDrawer from './division-drawer'
import server from '../server/server'
import version from '../system-tab/version.vue'
import pipeLine from '../system-tab/pipeLine.vue'
// import relation from '../system-tab/relation'

export default {
  components: {
    divisionDrawer,
    server,
    version,
    pipeLine
    // relation
    // envTopo,
    // featureModule
  },
  data() {
    return {
      pageLoading: false,
      tabActive: this.$route.params.tab,
      types: [
        {
          type: 'server',
          name: '服务应用'
        },
        {
          type: 'version',
          name: '版本信息'
        },
        {
          type: 'pipeLine',
          name: '流水线'
        }
        // {
        //   type: 'relation',
        //   name: '关联业务系统'
        // },
        // {
        //   type: 'envTopo',
        //   name: '环境拓扑图'
        // },
        // {
        //   type: 'featureModule',
        //   name: '功能模块'
        // }
      ],
      divisionDrawerParam: { visible: false },
      ruleForm: {},
      orgNameList: [],
      regionList: [],
      id: this.$route.params.id
    }
  },
  mounted() {
    this.getFullInfo()
  },
  methods: {
    async getFullInfo() {
      // this.pageLoading = true;
      const { data, success, message } = await apiBaseBusinessSystemSelectById(
        this.id
      )
      if (!success) {
        this.$message.warning(message)
        return
      }
      // this.pageLoading = false;
      data.org = data.businessSystemOrgDatas
        .map((r) => r.orgName)
        .map((r) => r)
        .join(',')

      const AA = data.businessSystemEngineDatas.filter(
        (r) => r.dictEngineType == 'base_system_engine_CICD'
      )

      const BB = data.businessSystemEngineDatas.filter(
        (r) => r.dictEngineType == 'base_system_engine_codeQuality'
      )

      data.jenkinsMasterEngineName = AA.map((r) => r.engineName).join(',')
      data.sonarQubeEngineName = BB.map((r) => r.engineName).join(',')

      this.ruleForm = data

      this.regionList = data.businessSystemEnvDatas.map(
        (r) => r.dictEnvironmenId
      )
    },
    // 权限分配
    resDivision(id) {
      this.divisionDrawerParam = { visible: true, id: id }
    },

    handleClick(tab, event) {
      const id = this.$route.params.id
      this.$router.replace(
        `/cmdb/business/system/configure/${id}/${tab.name}`
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.region_env {
  margin-right: 2px;
}
.name{
  font-size: 22px;
  font-weight: bold;
}
.formBox{
  margin: 5px 0 5px 5px;
  :deep(.el-col-8) {
    height: 30px;
  }
}
</style>
