<template>
  <el-dialog
    :title="dicTitle"
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    class="dialog"
   
  >
    <el-form
      ref="basicForm"
      v-loading="formLoading"
      :model="basicForm"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item label="标识" prop="id">
        <el-input
          v-model="basicForm.id"
          placeholder="请输入标识"
          :disabled="!!data"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="basicForm.name" placeholder="请输入名称" />
      </el-form-item>
      <!-- <el-form-item
        label="名称"
        prop="name"
        :rules="nameOldRules"
        v-if="!!data"
      >
        <el-input v-model="basicForm.name" placeholder="请输入名称" />
      </el-form-item> -->
      <el-form-item label="环境标签" prop="envId">
        <!-- <el-select
            v-model="basicForm.envId"
            multiple
            placeholder="请选择环境"
          >
            <el-option-group
              v-for="group in envList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.env"
                :key="item.dictKey"
                :label="item.envName"
                :value="item.dictKey"
              >
              </el-option>
            </el-option-group>
          </el-select> -->

        <el-cascader
          v-model="basicForm.envId"
          :show-all-levels="false"
          :options="envList"
          :props="{ multiple: true }"
          style="width: 100%"
          filterable
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="是否包含准生产环境" prop="preEnv">
        <el-select
          v-model="basicForm.preEnv"
          clearable
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in preEnvList"
            :key="index"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="是否需要资源" prop="envResource">
        <el-select
          v-model="basicForm.envResource"
          clearable
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in envResourceList"
            :key="index"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否需要安全扫描" prop="securityScan">
        <el-select
          v-model="basicForm.securityScan"
          clearable
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in securityScanList"
            :key="index"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="持续集成引擎" prop="jenkinsMasterEngineId">
        <el-select
          v-model="basicForm.jenkinsMasterEngineId"
          clearable
          placeholder="请选择持续集成引擎"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in jekinsList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码质量引擎" prop="sonarQubeEngineId">
        <el-select
          v-model="basicForm.sonarQubeEngineId"
          clearable
          placeholder="请选择代码质量引擎"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in sonaList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="测试平台引擎" prop="jettoProEngineId">
        <el-select
          v-model="basicForm.jettoProEngineId"
          clearable
          placeholder="请选择测试平台引擎"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in jettoProList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="测试项目" prop="testProjectId">
        <el-select
          v-model="basicForm.testProjectId"
          clearable
          placeholder="请选择测试项目"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in issueList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="维护人" prop="responsible">
        <el-select
          v-model="basicForm.responsible"
          filterable
          clearable
          placeholder="请选择维护人"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in responList"
            :key="index"
            :label="item.name"
            :value="item.id"
          >
            <!-- <JUser :value="item.id" :select-list="item" :size="22" /> -->
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="需求配置" prop="proInfos">
        <el-select
          v-model="basicForm.proInfos"
          clearable
          placeholder="请选择需求配置"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in issueList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="basicForm.description" autocomplete="off" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>&nbsp;
      <el-button type="primary" @click="submit()">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  apiBaseBuniessSelectByCondition,
  apiBaseDictenvComponent,
  apiBaseBuniessSelectIssuce,
  apiBasBusinessSystemAdd,
  apiBasBusinessSystemUpdate,
  apiBaseGetBuniessInfoById
} from '@/api/vone/cmdb/business'

import {
  apiBaseAllUserNoPage
} from '@/api/vone/cmdb/business'

import reduce from 'lodash/reduce'
import keys from 'lodash/keys'

export default {
  props: {
    dicTitle: {
      default: undefined,
      type: String
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    data: {
      type: Object,
      default: undefined
    }
  },
  data() {
    return {
      formLoading: false,
      envList: [],
      envListMap: {},
      jekinsList: [],
      issueList: [],
      sonaList: [],
      jettoProList: [],
      responList: [],
      typeList: [],
      basicForm: {
        id: '',
        name: '',
        envId: [],
        sonarQubeEngineId: '',
        jettoProEngineId: '',
        testProjectId: '',
        responsible: this.$store.state.user.userInfo.id,
        proInfos: '',
        description: '',
        preEnv: 0,
        envResource: 0,
        securityScan: 0
      },
      nameNewRules: [],
      nameOldRules: [
        {
          pattern: '^([\\u4E00-\\u9FA5A-Za-z0-9（）()]){1,100}$',
          message: '请输入不超过100个数字、字母或中英文括号组成的名称'
        }
      ],
      rules: {
        id: [
          { required: true, message: '请输入编号', trigger: 'blur' },
          {
            pattern: '^[a-zA-Z0-9\\_\\-]{1,32}$',
            message: '请输入不超过32个数字、字母、-和_组成的编号'
          }
        ],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            pattern: '^([\\u4E00-\\u9FA5A-Za-z0-9（）()-—_]){1,100}$',
            message:
              '请输入不超过100个字符的中英文、中划线、下划线、中英文括号组成的名称'
          },
          {
            pattern:
              "[^`~#^=|{}':;',\\[\\].<>/?~#￥……|{}@&$*!@&￥*！【】‘；：”“'。，、？%]+$",
            message: '禁止输入非法字符'
          }
        ],
        envId: [{ required: true, message: '请选择环境标签', trigger: 'blur' }],
        jenkinsMasterEngineId: [
          { required: false, message: '请选择持续集成引擎', trigger: 'change' }
        ],
        sonarQubeEngineId: [
          { required: false, message: '请选择代码质量引擎', trigger: 'change' }
        ],
        jettoProEngineId: [
          { required: false, message: '测试平台引擎', trigger: 'blur' }
        ],
        testProjectId: [
          { required: false, message: '测试项目', trigger: 'blur' }
        ],
        responsible: [
          { required: true, message: '请选择维护人', trigger: 'blur' }
        ],
        proInfos: [
          { required: false, message: '请选择需求配置', trigger: 'blur' }
        ],
        description: [
          {
            required: false,
            message: '请输入不超过100个字符的属性描述',
            trigger: 'blur',
            min: 0,
            max: 100
          }
        ]
      },
      preEnvList: [
        { value: '是', id: 1 },
        { value: '否', id: 0 }
      ],
      envResourceList: [
        { value: '是', id: 1 },
        { value: '否', id: 0 }
      ],
      securityScanList: [
        { value: '是', id: 1 },
        { value: '否', id: 0 }
      ]
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      this.$nextTick(() => {
        this.$refs.basicForm.clearValidate()
      })
      // if (this.data) {
      //   this.basicForm = Object.assign({}, this.data);
      //   this.$set(this.basicForm, "envId", this.data.envIds);
      // }
      if (this.data) {
        this.getBunidessInfoById()
      }
    }
  },
  mounted() {
    // this.getSelectByTypeId();
    this.getEnvList()
    this.getJekins()
    this.getSonaList()
    this.getJettoList()
    this.getIssueList()
    this.getResponseList()
  },
  methods: {
    onClose() {
      this.$nextTick(() => {
        this.$refs.basicForm.resetFields()
        this.$emit('update:visible', false)
      })
    },

    // 根据id查询业务系统信息
    async getBunidessInfoById() {
      this.formLoading = true
      const res = await apiBaseGetBuniessInfoById(
        this.id
      )
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      const AA = res.data.businessSystemEngineDatas.filter(
        (r) => r.dictEngineType == 'base_system_engine_CICD'
      )

      const BB = res.data.businessSystemEngineDatas.filter(
        (r) => r.dictEngineType == 'base_system_engine_codeQuality'
      )

      res.data.jenkinsMasterEngineId = AA.map((r) => r.engineId).join(',')
      res.data.sonarQubeEngineId = BB.map((r) => r.engineId).join(',')

      res.data.envId = res.data.businessSystemEnvDatas.map(({ dictEnvironmenId }) => [
        this.envListMap[dictEnvironmenId].type,
        dictEnvironmenId
      ])

      this.basicForm = res.data
      this.$refs.basicForm.clearValidate()
    },
    // 查询环境下拉框数据
    async getEnvList() {
      const res = await apiBaseDictenvComponent()
      if (!res.isSuccess) {
        return
      }

      const envType = {
        0: '开发环境',
        1: '测试环境',
        2: '生产环境'
      }

      // 格式化数据字典数据，取出高级属性中的type
      const dictArr = res.data.map((dict) => {
        const _dict = { label: dict.name, value: dict.dictKey }
        if (dict.advancedAttributeDatas) {
          dict.advancedAttributeDatas.some((attr) => {
            if (attr.attributesKey === 'type') {
              _dict.type = attr.attributesValue
              return true
            }
          })
        }
        return _dict
      })

      // 按type将数据分组
      const dictTreeMap = reduce(
        dictArr,
        (r, v) => {
          r[v.type] = r[v.type] || []
          r[v.type].push(v)
          return r
        },
        {}
      )

      // 格式化成级联的数据
      const dictTree = keys(dictTreeMap).map((key) => {
        return {
          label: envType[key],
          value: key,
          children: dictTreeMap[key]
        }
      })

      // 将数据字典数据转成Map，用于修改数据回显，回显一定要先加载环境数据字典数据
      this.envListMap = reduce(dictArr, (r, v) => (r[v.value] = v) && r, {})
      this.envList = dictTree
    },
    // 查询持续集成引擎下拉列表数据
    async getJekins() {
      const res = await apiBaseBuniessSelectByCondition({
        engineCategoryId: 'JenkinsMaster',
        state: 0
      })
      if (!res.isSuccess) {
        return
      }
      this.jekinsList = res.data
    },
    // 查询代码质量引擎下拉框数据
    async getSonaList() {
      const res = await apiBaseBuniessSelectByCondition({
        engineCategoryId: 'SonarQube',
        state: 0
      })
      if (!res.isSuccess) {
        return
      }
      this.sonaList = res.data
    },
    // 查询测试平台引擎下拉框数据
    async getJettoList() {
      const res = await apiBaseBuniessSelectByCondition({
        engineCategoryId: 'JettoPro3.0',
        state: 0
      })
      if (!res.isSuccess) {
        return
      }
      this.jettoProList = res.data
    },
    // 查询测试项目下拉框数据
    async getIssueList() {
      const res = await apiBaseBuniessSelectIssuce()
      if (!res.isSuccess) {
        return
      }
      this.issueList = res.data
    },
    // 查询维护人下拉框数
    async getResponseList() {
      const res = await apiBaseAllUserNoPage()
      if (!res.isSuccess) {
        return
      }
      this.responList = res.data
      const o = res.data.find(
        (item) => item.id == this.$store.state.user.userInfo.id
      )
      this.basicForm.responsible = o ? o.id : ''
    },
    // 保存
    async submit() {
      try {
        await this.$refs.basicForm.validate()
      } catch (error) {
        return
      }

      if (this.data) {
        const businessSystemEnvDatas = this.basicForm.envId.map((r) => ({
          businessSystemId: this.basicForm.id,
          dictEnvironmenId: r[1]
        }))

        const businessSystemEngineDatas = []
        if (this.basicForm.jenkinsMasterEngineId) {
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.jenkinsMasterEngineId,
            dictEngineType: 'base_system_engine_CICD',
            dictEngineCategory: 'JenkinsMaster'
          })
        }
        if (this.basicForm.sonarQubeEngineId) {
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.sonarQubeEngineId,
            dictEngineType: 'base_system_engine_codeQuality',
            dictEngineCategory: 'SonarQube'
          })
        }
        this.basicForm.jettoProEngineId &&
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.jettoProEngineId,
            dictEngineType: 'base_system_engine_autoTest',
            dictEngineCategory: 'JettoPro3.0'
          })
        const res = await apiBasBusinessSystemUpdate({
          businessSystemEnvDatas: businessSystemEnvDatas,
          // envId: this.basicForm.envId.map(r => r).join(","),
          id: this.basicForm.id,
          name: this.basicForm.name,
          responsible: this.basicForm.responsible,
          description: this.basicForm.description,
          // jenkinsMasterEngineId: this.basicForm.jenkinsMasterEngineId,
          // sonarQubeEngineId: this.basicForm.sonarQubeEngineId,
          // jettoProEngineId: this.basicForm.jettoProEngineId,
          businessSystemEngineDatas: businessSystemEngineDatas,
          // businessSystemIssueConfigDatas:businessSystemIssueConfigDatas,
          testProjectId: this.basicForm.testProjectId,
          proInfos: this.basicForm.proInfos,
          // preEnv: this.basicForm.preEnv,
          envResource: this.basicForm.envResource,
          securityScan: this.basicForm.securityScan
        })
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          // this.$emit("update:visible", false);
          return
        }
        this.$message.success('保存成功')
        this.basicForm = {}
        this.$emit('success')
        this.onClose()
      } else {
        var arr1 = this.basicForm.envId.map((r) => r[1])
        var arr2 = [].concat.apply([], arr1)

        const businessSystemEnvDatas = arr2.map((r) => ({
          businessSystemId: this.basicForm.id,
          dictEnvironmenId: r
        }))
        const businessSystemEngineDatas = []
        if (this.basicForm.jenkinsMasterEngineId) {
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.jenkinsMasterEngineId,
            dictEngineType: 'base_system_engine_CICD',
            dictEngineCategory: 'JenkinsMaster'
          })
        }
        if (this.basicForm.sonarQubeEngineId) {
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.sonarQubeEngineId,
            dictEngineType: 'base_system_engine_codeQuality',
            dictEngineCategory: 'SonarQube'
          })
        }
        this.basicForm.jettoProEngineId &&
          businessSystemEngineDatas.push({
            businessSystemId: this.basicForm.id,
            engineId: this.basicForm.jenkinsMasterEngineId,
            dictEngineType: 'base_system_engine_autoTest',
            dictEngineCategory: 'JettoPro3.0'
          })
        const res = await apiBasBusinessSystemAdd({
          // envId: this.basicForm.envId.map(r => r).join(","),
          businessSystemEnvDatas: businessSystemEnvDatas,
          id: this.basicForm.id,
          name: this.basicForm.name,
          responsible: this.basicForm.responsible,
          description: this.basicForm.description,
          // jenkinsMasterEngineId: this.basicForm.jenkinsMasterEngineId,
          // sonarQubeEngineId: this.basicForm.sonarQubeEngineId,
          // jettoProEngineId: this.basicForm.jettoProEngineId,
          businessSystemOrgDatas: [],
          businessSystemEngineDatas: businessSystemEngineDatas,
          testProjectId: this.basicForm.testProjectId, // 测试项目
          proInfos: this.basicForm.proInfos, // 需求配置
          businessSystemTestProject: [],
          issueEngine: this.issueList.length > 0 ? this.issueList[0].id : null,
          // preEnv: this.basicForm.preEnv,
          envResource: this.basicForm.envResource,
          securityScan: this.basicForm.securityScan
        })

        // this.basicForm = {};
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.basicForm = {}
        this.$emit('success')
        this.onClose()
      }
    }
  }
}
</script>
<style lang="scss" scoped>

.dialog{
  :deep(.el-dialog__body) {
  height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

}

</style>
