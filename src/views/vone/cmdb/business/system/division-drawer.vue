<template id="tab_keyTag">
  <vone-auth-org v-bind="$attrs" :visible="visible" :tree-data="treeData" :loading="loading" :value="value" :disabled-value="disabledValue" :success="success" />
</template>
<script>
import { orgList } from '@/api/vone/base/org'
import { apiBaseSaveBusinessSystemOrg, apiBaseBusinessSystemSelectById } from '@/api/vone/cmdb/business'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      treeData: [],
      loading: true,
      value: [],
      disabledValue: []
    }
  },
  watch: {
    visible: {
      async handler(v) {
        this.loading = true
        if (!v) return
        await this.getOrgChildTree()
        await this.getSelectOrgById()
        this.$nextTick(() => (this.loading = false))
      },
      immediate: true
    }
  },
  methods: {
    // 获取权限分配树结构数据信息
    async getOrgChildTree() {
      const { data, isSuccess, message } = await orgList()
      if (!isSuccess) {
        this.$message.warning(message)
        return
      }
      this.treeData = data
    },

    // 获取选中的节点
    async getSelectOrgById() {
      const { data, isSuccess, message } = await apiBaseBusinessSystemSelectById(this.id)
      if (!isSuccess) {
        this.$message.warning(message)
        return
      }

      this.value = data.businessSystemOrgDatas.map(r => r.orgId)
      this.disabledValue = data.businessSystemOrgDatas.filter(r => r.isdefault === 0).map(r => r.orgId)
    },
    // 保存权限分配数据
    async success(checked) {
      const businessSystemOrgDatas = checked.map(orgId => ({
        orgId,
        businessSystemId: this.id
      }))
      const { isSuccess, message } = await apiBaseSaveBusinessSystemOrg({
        id: this.id,
        businessSystemOrgDatas
      })
      if (!isSuccess) {
        this.$message.warning(message)
        return
      }
      this.$message.success(message)
      this.$emit('update:visible', false)
      this.$emit('success')
      return true
    }
  }
}
</script>
