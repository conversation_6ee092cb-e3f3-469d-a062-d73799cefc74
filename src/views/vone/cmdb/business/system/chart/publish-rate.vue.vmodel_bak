<template>
  <div>
    <el-card style="height: 100%">
      <div slot="header">
        <span>发布频率趋势图</span>
        <a @click="showDialog">
          <i class="iconfont el-icon-application-setting" style="float: right; padding: 3px 0" />
        </a>
      </div>
      <div>
        <div v-if="noData">
          <vone-empty />
        </div>
        <div v-if="!noData" class="echart">
          <vone-echarts :options="lineData" />
        </div>
      </div>
    </el-card>
    <!-- 对话框 -->
    <el-dialog
      title="筛选条件"
      width="40%"
      v-model:visible="dialogFormVisible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form">
        <el-form-item label="服务应用" prop="systemId">
          <el-select
            v-model="form.systemId"
            placeholder="请选择服务应用"
            @change="searchEnv"
          >
            <el-option
              v-for="(item, index) in serveList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="环境" prop="envId">
          <el-select v-model="form.envId" placeholder="请选择环境" multiple>
            <el-option
              v-for="(item, index) in envList"
              :key="index"
              :label="item.name"
              :value="item.dictKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间维度" prop="cycle">
          <el-radio-group v-model="form.cycle">
            <el-radio :label="'月'">月</el-radio>
            <el-radio :label="'周'">周</el-radio>
            <el-radio :label="'日'">日</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="form.cycle === '月'"
          label="开始时间"
          prop="startMonth"
        >
          <el-date-picker
            key="startMonth"
            v-model="form.startMonth"
            value-format="yyyy-MM-dd"
            type="month"
            :picker-options="startDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item
          v-if="form.cycle === '周'"
          label="开始时间"
          prop="startWeek"
        >
          <el-date-picker
            key="startWeek"
            v-model="form.startWeek"
            type="week"
            format="yyyy 第 WW 周"
            value-format="yyyy-MM-dd"
            :picker-options="startDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item
          v-if="form.cycle === '日'"
          label="开始时间"
          prop="startDay"
        >
          <el-date-picker
            key="startDay"
            v-model="form.startDay"
            value-format="yyyy-MM-dd"
            type="date"
            :picker-options="startDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>

        <el-form-item
          v-if="form.cycle === '月'"
          label="结束时间"
          prop="endMonth"
        >
          <el-date-picker
            key="endMonth"
            v-model="form.endMonth"
            value-format="yyyy-MM-dd"
            type="month"
            :picker-options="endDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item
          v-if="form.cycle === '周'"
          label="结束时间"
          prop="endWeek"
        >
          <el-date-picker
            key="endWeek"
            v-model="form.endWeek"
            type="week"
            format="yyyy 第 WW 周"
            value-format="yyyy-MM-dd"
            :picker-options="endDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item v-if="form.cycle === '日'" label="结束时间" prop="endDay">
          <el-date-picker
            key="endDay"
            v-model="form.endDay"
            value-format="yyyy-MM-dd"
            type="date"
            :picker-options="endDateOpt"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { apiBaseTrendOfFrequLine } from '@/api/vone/cmdb/business'

export default {
  props: {
    envList: {
      type: Array,
      default: () => []
    },
    serveList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      noData: false,
      lineData: null,
      // envList:[],
      form: {
        cycle: '月',
        startMonth: '',
        startWeek: '',
        startDay: '',
        endMonth: '',
        endWeek: '',
        endDay: ''
      }
    }
  },
  computed: {
    startDateOpt() {
      const that = this
      return {
        disabledDate(time) {
          if (that.form.cycle == '月') {
            return time.getTime() > new Date(that.form.endMonth).getTime()
          }
          if (that.form.cycle == '周') {
            return time.getTime() > new Date(that.form.endWeek).getTime()
          }
          if (that.form.cycle == '日') {
            return time.getTime() > new Date(that.form.endDay).getTime()
          }
        }
      }
    },
    endDateOpt() {
      const that = this
      return {
        disabledDate(time) {
          if (that.form.cycle == '月') {
            return time.getTime() < new Date(that.form.startMonth).getTime()
          }
          if (that.form.cycle == '周') {
            return time.getTime() < new Date(that.form.startWeek).getTime()
          }
          if (that.form.cycle == '日') {
            return time.getTime() < new Date(that.form.startDay).getTime()
          }
        }
      }
    }
  },
  mounted() {
    this.initInfo()
  },
  methods: {
    onClose() {
      this.dialogFormVisible = false
      this.$refs.form.resetFields()
    },
    async initInfo() {
      const { data, success, message } = await apiBaseTrendOfFrequLine({
        businessId: this.$route.params.id,
        cycle: this.form.cycle ? this.form.cycle : '月',
        endDay: this.form.endDay,
        endMonth: this.form.endMonth,
        endWeek: this.form.endWeek,
        envId: this.form.envId ? this.form.envId.join(',') : '',
        startDay: this.form.startDay,
        startMonth: this.form.startMonth,
        startWeek: this.form.startWeek,
        systemId: this.form.systemId
      })
      if (!success) {
        this.$message.warning(message)
        return
      }

      if (data.length > 0) {
        this.lineData = {
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((r) => r.ym),
            splitLine: {
              show: false // 去掉网格线
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            backgroundColor: '#fff',
            borderColor: 'none',
            extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
            textStyle: {
              color: '#53565C'
            }
          },
          grid: {
            left: '40px',
            right: '37px', // 与容器右侧的距离
            bottom: '33px'
          },
          yAxis: {
            type: 'value',
            name: '次数',
            'axisTick': { // y轴刻度线
              'show': false
            },
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          },
          series: [
            {
              name: '次数',
              data: data.map((r) => r.sum),
              type: 'line',
              areaStyle: {}
            }
          ]
        }
      } else {
        this.noData = true
      }
    },
    showDialog() {
      this.dialogFormVisible = true
    },
    // 根据服务应用查环境
    async searchEnv() {
      this.$set(this.form, 'envId', '')
      this.$emit('changeApp', this.form.systemId)
    },
    // 对话框保存
    async saveInfo() {
      await this.initInfo()
      this.onClose()
    }
  }
}
</script>

<style>
</style>
