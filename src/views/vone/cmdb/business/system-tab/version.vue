<template>
  <div>
    <el-row style="margin-bottom:10px">

      <el-button type="primary" icon="el-icon-plus" @click="addPlain(IDS)">添加发版计划</el-button>

    </el-row>
    <el-table :table-data="tableData" :table-options="tableOptions" height="calc(100vh - 346px)" @getTableData="getTableData">

      <template>
        <el-table-column prop="version" label="版本号" width="175" />
        <el-table-column prop="versionState" label="版本状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 0">待发布</span>
            <span v-if="scope.row.state == 1">发布中</span>
            <span v-if="scope.row.state == 2">已发布</span>
          </template>
        </el-table-column>
        <el-table-column prop="owner" label="负责人" width="150">
          <template slot-scope="scope">
            {{ scope.row.owner }}
          </template>
        </el-table-column>
        <el-table-column prop="deployTime" label="预计发版时间" width="200" />

        <el-table-column prop="age" label="发版历史" header-align="center">
          <el-table-column prop="historyDeployTime" label="实际发版时间" width="175" />
          <el-table-column prop="duration" label="发版时长">
            <template slot-scope="scope">
              {{ minDurationTime(scope.row.duration) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">成功</span>
              <span v-else-if="scope.row.status == 1">失败</span>
              <span v-else />
            </template>
          </el-table-column>
          <!-- <el-table-column-actions :actions="innerRowActions" width="50" /> -->
        </el-table-column>
        <!-- <el-table-column-actions prop="actions" :actions="rowActions" /> -->
      </template>
    </el-table>
    <modalDialog v-model="dialogParam.visible" v-bind="dialogParam" @success="getTableData" />

    <!--

    <versionDetailDialog v-model="versionDetailParam.visible" v-bind="versionDetailParam" /> -->

  </div>
</template>

<script>
import { apiBaseVersionWithHistory,
  apiBaseVersionEditDeleteId
} from '@/api/vone/cmdb/business'
import modalDialog from './modal-dialog'
// import versionDetailDialog from './versionDetailDialog'

export default {
  components: {
    modalDialog
    // versionDetailDialog
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      // 表格配置项
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号
        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '100', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editById // 操作事件
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteById // 操作事件
            }
          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [
          ]
        }
      },
      dialogParam: { visible: false },
      versionDetailParam: { visible: false },
      rowList: [],
      tableData: {},
      IDS: '',
      versionStateList: []
      // innerRowActions: [
      //   {
      //     text: '详情',
      //     icon: 'el-icon-document',
      //     hidden: ({ row }) => { return row.status != 0 },
      //     onClick: ({ row }) => this.versionDetail(row)
      //   }
      // ],
    }
  },
  mounted() {
    this.IDS = this.id
    this.getTableData()
  },
  methods: {
    minDurationTime(diffValue) {
      if (diffValue && !isNaN(diffValue)) {
        diffValue = parseInt(diffValue)
        if (diffValue < 0) {
          return ''
        }
        var second = 1000
        var minute = 1000 * 60
        var hour = minute * 60
        var day = hour * 24
        var days = diffValue > day ? parseInt(diffValue / day) : 0
        var hours = diffValue > hour ? parseInt((diffValue % day) / hour) : 0
        var mins = diffValue > minute ? parseInt(((diffValue % day) % hour) / minute) : 0
        var seconds = diffValue > second ? parseInt((((diffValue % day) % hour) % minute) / second) : 0
        var millisecond = parseInt(diffValue - day * days - hour * hours - minute * mins - second * seconds)
        if (days > 0) {
          return days + '天 ' + hours + '小时 ' + mins + '分钟 ' + seconds + '秒'
        } else if (hours > 0) {
          return hours + '小时 ' + mins + '分钟 ' + seconds + '秒'
        } else if (mins > 0) {
          return mins + '分钟 ' + seconds + '秒'
        } else if (seconds > 0) {
          var str = ''
          if (millisecond) {
            str = seconds + '秒 ' + millisecond + '毫秒'
          } else {
            str = seconds + '秒'
          }
          return str
        } else {
          return millisecond + '毫秒'
        }
      } else {
        return ''
      }
    },

    async getTableData() {
      const res = await apiBaseVersionWithHistory({
        businessSystemId: this.IDS
      })
      if (!res.success) {
        this.$message.warning(res.message)
        return
      }

      const list = []; const row = []; const index = 0
      for (let i = 0; i < res.data.length; i++) {
        if (res.data[i].businessSystemVersionHistoryDatas.length == 0) {
          const map = {}
          map.businessSystemId = res.data[i].businessSystemId
          map.version = res.data[i].version
          map.state = res.data[i].state
          map.realDeployTime = res.data[i].realDeployTime
          map.ownerName = res.data[i].ownerName
          map.owner = res.data[i].owner
          map.name = res.data[i].name
          map.groupReleasePlanId = res.data[i].groupReleasePlanId
          map.description = res.data[i].description
          map.deployTime = res.data[i].deployTime
          map.buildId = ''
          map.historyDeployTime = ''
          map.duration = ''
          map.status = 3
          map.pipelineId = ''
          list.push(map)
          row.push(1)
        } else {
          for (let j = 0; j < res.data[i].businessSystemVersionHistoryDatas.length; j++) {
            if (j == 0) {
              row.push(res.data[i].businessSystemVersionHistoryDatas.length + index)
            } else {
              row.push(0)
            }
            const map = {}
            map.businessSystemId = res.data[i].businessSystemId
            map.version = res.data[i].version
            map.state = res.data[i].state
            map.realDeployTime = res.data[i].realDeployTime
            map.ownerName = res.data[i].ownerName
            map.owner = res.data[i].owner
            map.name = res.data[i].name
            map.groupReleasePlanId = res.data[i].groupReleasePlanId
            map.description = res.data[i].description
            map.deployTime = res.data[i].deployTime
            map.buildId = res.data[i].businessSystemVersionHistoryDatas[j].buildId
            map.historyDeployTime = res.data[i].businessSystemVersionHistoryDatas[j].deployTime
            map.duration = res.data[i].businessSystemVersionHistoryDatas[j].duration
            map.status = res.data[i].businessSystemVersionHistoryDatas[j].state
            map.pipelineId = res.data[i].businessSystemVersionHistoryDatas[j].pipelineId
            list.push(map)
          }
        }
      }

      // const DATA = this.$gainItData(res).data
      this.tableData = this.$gainItData(res).data
      this.rowList = row
      res.data
        .map(r => r.businessSystemVersionHistoryDatas)
        .forEach(element => {
          this.versionStateList = element
        })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex in [0, 1, 2, 3, 4] || column.property == 'actions') {
        return {
          rowspan: this.rowList[rowIndex],
          colspan: 1
        }
      }
    },
    // 编辑
    editById(row) {
      this.dialogParam = {
        visible: true,
        dicTitle: '编辑发版计划',
        data: row
      }
    },
    // 删除
    async deleteById(row) {
      await this.$confirm('确定删除该版本吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
        .then(async action => {
          const { success, message } = await apiBaseVersionEditDeleteId({
            businessSystemId: row.businessSystemId,
            version: row.version
          })
          if (!success) {
            this.$message.warning(message)
            return
          }
          this.$message.success('删除成功')
          this.getTableData()
        })
        .catch(() => { })
    },
    // 新增
    addPlain(IDS) {
      this.dialogParam = {
        visible: true,
        dicTitle: '新增发版计划',
        id: IDS
      }
    },
    // 版本发布详情
    async versionDetail(row) {
      this.versionDetailParam = {
        visible: true,
        dicTitle: row.version + '版本发布成功详情',
        rowData: row
      }
    }
  }
}
</script>
