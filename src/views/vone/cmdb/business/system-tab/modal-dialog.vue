<template>
  <div>
    <el-dialog
      :title="dicTitle"
      width="50%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
     
    >
      <el-form ref="basicForm" :model="basicForm" :rules="rules" @submitValid="submit">
        <el-form-item label="版本名称" prop="name">
          <el-input v-model="basicForm.name" placeholder="请输入长度不超过50个字符的版本名称" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="basicForm.version" placeholder="请输入长度不超过50个字符的版本号" :disabled="!!data" />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-select
            v-model="basicForm.owner"
            placeholder="请选择负责人"
            filterable
            style="width:100%"
          >
            <el-option
              v-for="item in userData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预计发版时间" prop="deployTime">
          <el-date-picker
            v-model="basicForm.deployTime"
            type="date"
            placeholder="选择日期"
            style="width:100%"
          />
        </el-form-item>
        <el-form-item v-if="data" label="实际发版时间" prop="realDeployTime">
          <el-date-picker
            v-model="basicForm.realDeployTime"
            type="date"
            placeholder="选择日期"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item v-if="data" label="状态" prop="state">
          <el-radio-group v-model="basicForm.state">
            <el-radio-button :label="0">待发布</el-radio-button>
            <el-radio-button :label="1">发布中</el-radio-button>
            <el-radio-button :label="2">已发布</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="basicForm.description" placeholder="请输入长度不超过255个字符的描述" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button type="primary" @click="submit()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { apiBaseVersionAdd, apiBaseVersionEditById } from '@/api/vone/cmdb/business'

import { apiBaseAllUserNoPage } from '@/api/vone/base/user'

import dayjs from 'dayjs'
export default {
  props: {
    dicTitle: {
      default: undefined,
      type: String
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    data: {
      type: Object,
      default: () => undefined
    }
  },
  data() {
    return {
      envList: [],
      jekinsList: [],
      issueList: [],
      sonaList: [],
      jettoProList: [],
      responList: [],
      typeList: [],
      basicForm: {},
      rules: {
        name: [
          {
            required: true,
            message: '版本名称不能为空',
            min: 1,
            max: 50,
            trigger: 'blur'
          }
        ],
        version: [
          {
            required: true,
            message: '版本名称不能为空',
            min: 1,
            max: 50,
            trigger: 'blur'
          }
        ],
        deployTime: [
          { required: true, message: '发版时间不能为空', trigger: 'blur' }
        ],
        owner: [
          { required: true, message: '负责人不能为空', trigger: 'blur' }
        ],
        description: [{ required: false, trigger: 'blur' }]
      },
      userData: []
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      this.getUserData()
      if (this.data) {
        this.basicForm = this.data
      }
    }
  },
  mounted() {},
  methods: {
    onClose() {
      this.basicForm = {}
      this.$emit('update:visible', false)
      this.$emit('success')
    },
    async getUserData() {
      const { data, success, message } = await apiBaseAllUserNoPage()
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.userData = data
    },
    // 保存
    async submit() {
      if (this.data) {
        const { success, message } = await apiBaseVersionEditById({
          businessSystemId: this.data.businessSystemId,
          name: this.basicForm.name,
          version: this.basicForm.version,
          owner: this.basicForm.owner,
          description: this.basicForm.description,
          deployTime: dayjs(this.basicForm.deployTime).toDate().toGMTString(),
          realDeployTime: dayjs(this.basicForm.realDeployTime).toDate().toGMTString(),
          state: this.basicForm.state
        })
        if (!success) {
          this.$message.warning(message)
          return
        }
        this.$message.success('成功修改一条数据')
        this.basicForm = {}
        this.$emit('success')
        this.$emit('update:visible', false)
      } else {
        const { success, message } = await apiBaseVersionAdd({
          businessSystemId: this.id,
          name: this.basicForm.name,
          version: this.basicForm.version,
          owner: this.basicForm.owner,
          description: this.basicForm.description,
          deployTime: this.basicForm.deployTime.toGMTString(),
          state: 0
        })
        this.basicForm = {}
        if (!success) {
          this.$message.warning(message)
          return
        }
        this.$message.success('新增一条数据')
        this.$emit('success')
        this.$emit('update:visible', false)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// .abow_dialog{
//   height: 500px;
//   overflow-y: auto;
// }
.dialog-footer{
  text-align: right;
}
</style>
