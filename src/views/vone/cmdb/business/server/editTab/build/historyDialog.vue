<template>
  <div>
    <el-dialog
      title="下发历史记录"
      width="60%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <main style="height:calc(100vh - 270px)">
        <vxe-table
          ref="build_history_table"
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth:'120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column title="引擎" field="name" width="100">
            <template #default="{row}">
              <span v-if="row.engineId && row.echoMap && row.echoMap.engineId">
                {{ row.echoMap.engineId.engineIp }}
              </span>
            </template>
          </vxe-column>
          <vxe-column field="dataStorage" title="dataStorage" width="90" />
          <vxe-column field="hostIp" title="主机IP" width="130" />
          <vxe-column title="版本号" field="scriptWarehouseVersion" width="70" />
          <vxe-column title="下发地址" field="destPath" />
          <vxe-column title="下发时间" field="createTime" width="160" />
          <vxe-column title="下发人" field="createdBy">
            <template #default="{row}">
              <span v-if="row.createdBy && row.echoMap && row.echoMap.createdBy">
                <vone-user-avatar
                  :avatar-path="row.echoMap.createdBy.avatarPath"
                  :name="row.echoMap.createdBy.name"
                />
              </span>
              <span v-else>
                {{ row.createdBy }}
              </span>
            </template>
          </vxe-column>
        </vxe-table>
      </main>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getTableList" />

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { apiCmdbBuildFileSendHistory } from '@/api/vone/cmdb/server'
export default {
  props: {
    id: {
      type: String,
      default: undefined
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      formData: {

      },

      tableOptions: {

        isOperation: false, // 表格有操作列时设置
        operation: {
          // isFixed: true,

        }
      }
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.getTableList()
    })
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    async getTableList() {
      try {
        this.tableLoading = true
        let params = {}

        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }

        this.$set(tableAttr, 'sort', 'buildFileId')
        params = {
          ...tableAttr,
          extra: {},
          model: { ...this.formData }
        }

        const res = await apiCmdbBuildFileSendHistory(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        res.data.records.forEach((item) => {
          if (
            !item.engineId ||
            !item.echoMap ||
            !item.echoMap.engineId ||
            !item.echoMap.engineId.engineExtendeds.length
          ) {
            return
          }

          const attrs = item.echoMap.engineId.engineExtendeds
          const _dataStorage = attrs.filter(
            (a) => a.key === 'dataStorage'
          )
          if (!_dataStorage.length) return
          item.dataStorage =
            _dataStorage[0].value ? '共享存储' : '非共享存储'
          // if (_dataStorage[0].value) {
          //   item.engineIp = item.engineData.engineIp
          // }
        })

        this.tableData = res.data
      } catch (e) {
        this.tableLoading = false
      }

      // this.tableData = data
    }
  }
}
</script>
