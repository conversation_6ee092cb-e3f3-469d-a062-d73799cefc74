<template>
  <el-dialog title="删除目标机参数文件" width="60%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <main style="height:calc(100vh - 270px)">
      <vxe-table
        ref="delete_history_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="引擎" field="name" width="100">
          <template #default="{row}">
            <span v-if="row.engineId && row.echoMap && row.echoMap.engineId">
              {{ row.echoMap.engineId.engineIp }}
            </span>
          </template>
        </vxe-column>
        <vxe-column field="dataStorage" title="存储方式" width="90" />
        <vxe-column field="hostIp" title="主机IP" width="130" />
        <vxe-column title="版本号" field="scriptWarehouseVersion" width="70" />
        <vxe-column title="下发地址" field="destPath" />
        <vxe-column title="下发时间" field="createTime" width="160" />
        <vxe-column title="下发人" field="createdBy">
          <template #default="{row}">
            <span v-if="row.createdBy && row.echoMap && row.echoMap.createdBy">
              <vone-user-avatar
                :avatar-path="row.echoMap.createdBy.avatarPath"
                :name="row.echoMap.createdBy.name"
              />
            </span>
            <span v-else>
              {{ row.createdBy }}
            </span>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableList" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="deleteScript">删除</el-button>
    </div>
  </el-dialog>

</template>

<script>
import { apiCmdbBuildFileSendPage, apiCmdbBuildFileSendDel } from '@/api/vone/cmdb/server'
export default {
  props: {
    id: {
      type: String,
      default: undefined
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      loading: false,
      formData: {
        buildFileId: this.id
      },
      tableData: {},
      tableSelected: [],
      tableOptions: {
        isOperation: false // 表格有操作列时设置
      }
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.getTableList()
    })
  },

  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    selectAllEvent({ checked }) {
      this.tableSelected = this.$refs.delete_history_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.tableSelected = this.$refs.delete_history_table.getCheckboxRecords()
    },
    async getTableList() {
      try {
        this.tableLoading = true
        let params = {}

        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }

        this.$set(tableAttr, 'sort', 'buildFileId')
        params = {
          ...tableAttr,
          extra: {},
          model: { ...this.formData }
        }

        const res = await apiCmdbBuildFileSendPage(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        res.data.records.forEach((item) => {
          if (
            !item.engineId ||
            !item.echoMap ||
            !item.echoMap.engineId ||
            !item.echoMap.engineId.engineExtendeds.length
          ) {
            return
          }

          const attrs = item.echoMap.engineId.engineExtendeds
          const _dataStorage = attrs.filter(
            (a) => a.key === 'dataStorage'
          )
          if (!_dataStorage.length) return
          item.dataStorage =
            _dataStorage[0].value ? '共享存储' : '非共享存储'
          // if (_dataStorage[0].value) {
          //   item.engineIp = item.engineData.engineIp
          // }
        })

        this.tableData = res.data
      } catch (e) {
        this.tableLoading = false
      }
    },
    async deleteScript() {
      if (this.tableSelected.length === 0) {
        return this.$message.error('请选择要删除的目标机参数文件')
      }
      try {
        this.loading = true
        const { isSuccess, msg } = await apiCmdbBuildFileSendDel(this.id,
          this.tableSelected.map(r => r.hostIp))
        this.loading = false
        if (!isSuccess) {
          this.loading = false
          return this.$message.error(msg)
        }
        this.$message.success('删除成功')
        this.getTableList()
        this.tableSelected = []
      } catch (e) {
        this.loading = false
        this.tableSelected = []
      }
    }
  }
}
</script>
