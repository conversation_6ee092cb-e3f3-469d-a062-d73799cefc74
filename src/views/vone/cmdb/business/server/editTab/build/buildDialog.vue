<template>
  <div>
    <el-dialog title="修改构建参数文件" width="60%" :close-on-click-modal="false" v-model="visible" :before-close="onClose" :loading="loading">
      <vone-code-edit v-model="code" :name="name" :read-only="false" @input="getData" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="updataScript">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  apiCmdbBuildFileGetById,
  apiCmdbBuildFilePut
} from '@/api/vone/cmdb/server'
export default {
  props: {
    id: {
      type: String,
      default: undefined
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      code: null,
      name: null,
      newCode: null
    }
  },

  mounted() {
    this.editScript()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    getData(val) {
      this.newCode = val
    },
    // 获取脚本数据
    async editScript() {
      this.loading = true
      const { data, isSuccess, msg } = await apiCmdbBuildFileGetById(
        this.id
      )
      this.loading = false
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.code = data.content
      this.name = data.scriptName
    },
    // 保存脚本
    async updataScript() {
      this.loading = true
      const { isSuccess, msg } = await apiCmdbBuildFilePut({
        id: this.id,
        content: this.newCode
      })
      this.loading = false
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.$message.success('修改成功')
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }
}
</script>

<style lang="scss" scoped>
.cm {
  height: 50vh;
}
</style>
