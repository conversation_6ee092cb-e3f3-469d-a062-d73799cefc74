<template>
  <div>
    <el-dialog
      title="上传"
      width="50%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-steps v-if="configList.length>0" :active="activeIndex" align-center>
        <template v-for="(item,index) in configList">
          <el-step
            :key="index"
            :title="item.title"
            :status="configList[index].status"
            @click.native="itemClick(item.step)"
          />
        </template>
      </el-steps>

      <div v-if="configList.length>0">
        <div v-for="(item,index) in configList" v-show="cur===item.step" :key="index">
          <div v-if="item.status !== 'wait'" class="log">
            <p class="title">日志--{{ item.title }}</p>
            <p class="logDetail">{{ item.log }}</p>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">关闭</el-button>&nbsp;
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   apiBuniessProductAsyncMessage,
//   apiBuniessProductAsyncMessagePropUuid
// } from '@/api/business'

export default {
  props: {
    dicTitle: {
      default: undefined,
      type: String
    },
    visible: {
      type: Boolean,
      default: false
    },
    uuid: {
      type: String,
      default: ''
    },
    productId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isActive: 0,
      configList: [],
      activeIndex: 0,
      cur: 1
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      // this.getDialogData()
      // this.getPropUuid()
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },

    itemClick(item) {
      this.cur = item
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-step__title) {
  font-size: 12px;
}
.log {
  border: 1px solid var(--disabled-bg-color,#ebeef5);;
  padding: 12px 16px;

  .title {
    border-bottom: 1px solid var(--disabled-bg-color,#ebeef5);;
    height: 30px;
    line-height: 30px;
  }
  .logDetail {
    padding: 10;
    white-space: "pre-line";
    word-break: "break-word";
  }
}
</style>
