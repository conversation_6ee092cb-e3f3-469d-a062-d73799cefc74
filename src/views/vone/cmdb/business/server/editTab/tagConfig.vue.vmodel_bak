<template>
  <!-- 分支配置 -->
  <div>
    <vone-search-wrapper>

      <template slot="actions">
        <el-button v-if="TYPE != 1" type="primary" icon="el-icon-plus" @click="addBranch">新增</el-button>
      </template>
    </vone-search-wrapper>
    <main style="height:calc(100vh - 446px)">
      <vxe-table
        ref="tagConfig_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="标识" field="tagKey" />
        <vxe-column field="updatedBy" title="提交人">
          <template #default="{row}">
            <vone-user-avatar v-if="row.echoMap.updatedBy" :avatar-path="row.echoMap.updatedBy &&row.echoMap.updatedBy.avatarPath" :name="row.echoMap.updatedBy &&row.echoMap.updatedBy.name" width="20px" height="20px" />
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" />
        <vxe-column title="描述" field="description" />
        <vxe-column v-if="TYPE != 1" title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <a @click="openDelete(row)">
              <i class="iconfont el-icon-application-delete" />
            </a>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    <!-- 新增分支 -->
    <el-dialog
      v-if="newTagVisible"
      title="新增Tag"
      v-model:visible="newTagVisible"
      width="30%"
      :close-on-click-modal="false"
      :before-close="newDialogClose"
    >
      <el-form ref="tagForm" :model="tagForm" label-width="auto" :rules="rules">
        <el-form-item label="Tag" prop="tagKey">
          <el-select v-model="tagForm.tagKey" filterable placeholder="请选择Tag" style="width:100%">
            <el-option v-for="item in tagList" :key="item.name" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="tagForm.description" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newTagVisible = false">取消</el-button>
        <el-button type="primary" @click="sureDAdd()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiCmdbServerTagList,
  apiCmdbServerTagDel,
  apiCmdbServerTagBySystemId,
  apiCmdbServerTagListAdd
} from '@/api/vone/cmdb/server'
export default {
  components: {

  },
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tableData: {},
      deleteForm: {
        deleteRemote: 0
      },
      tableOptions: {},
      rules: {
        tagKey: [
          { required: true, message: '请选择Tag', trigger: 'change' }
        ]
      },
      TYPE: '',
      tableLoading: false,
      formData: {
        applicationId: this.$route.params.systemId
      },
      newTagVisible: false,
      tagForm: {
        tagKey: '',
        description: ''
      },
      tagList: [],
      dictBranch: []
    }
  },
  mounted() {
    this.TYPE = this.$route.params.type
    this.getTableData()
    if (this.type === 1) {
      // this.getCodeLine();
    } else if (this.type === 0) {
      this.tableData = [{}]
    }
  },
  methods: {
    addBranch() {
      this.newTagVisible = true
      this.getBranchList()
    },
    newDialogClose() {
      this.newTagVisible = false
      this.$refs.tagForm.resetFields()
    },
    async getTableData() {
      this.tableLoading = true
      let params = {}
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }

      const res = await apiCmdbServerTagList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    async openDelete(row) {
      await this.$confirm(`确定删除吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
      const res = await apiCmdbServerTagDel(
        [row.id]
      )
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableData()
    },
    async sureDAdd() {
      // 从代码库查出的分支名称与分支字典CODE作对比,字典里有这个分支code则取字典的分支名称,code,branchKey,没有则取代码库实际的名称,同时把名称作为code保存,branchKey为空
      this.$refs.tagForm.validate((valid) => {
        if (valid) {
          this.savefun()
        }
      })
    },
    async savefun() {
      const { isSuccess, msg } = await apiCmdbServerTagListAdd(
        {
          applicationId: this.$route.params.systemId,
          description: this.tagForm.description,
          tagKey: this.tagForm.tagKey
        }
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('新增分支成功')
      this.newDialogClose()
      this.getTableData()
    },
    async getBranchList() {
      const { data, isSuccess, msg } = await apiCmdbServerTagBySystemId(this.$route.params.systemId)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tagList = data
    }
  }
}
</script>
