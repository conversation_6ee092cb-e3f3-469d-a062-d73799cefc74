<template>
  <!-- 分支配置 -->
  <div>
    <vone-search-wrapper>
      <template slot="actions">
        <el-button v-if="TYPE != 1" type="primary" icon="el-icon-plus" @click="addBranch">新增</el-button>
      </template>
    </vone-search-wrapper>
    <main style="height:calc(100vh - 446px)">
      <vxe-table
        ref="codeline_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="标识" field="code" />
        <vxe-column field="name" title="名称" />
        <vxe-column field="status" title="状态">
          <template #default="{row}">
            <span v-if="row.status && row.status.desc">{{ row.status.desc }}</span>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column v-if="TYPE != 1" title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <a @click="openDelete(row)">
              <i class="iconfont el-icon-application-delete" />
            </a>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    <el-row v-if="$route.params.type == 0">
      <div style="float: left">
        <el-button v-show="active > 0" @click="stepFoaward">上一步</el-button>
      </div>
      <div style="float: right">
        <el-button type="primary" @click="saveNext">保存并下一步</el-button>
      </div>
    </el-row>

    <!-- 删除对话框 -->
    <el-dialog title="删除分支" v-model:visible="deleteVisible" :close-on-click-modal="false" :before-close="deleteDialogClose">
      <el-form ref="deleteForm" :model="deleteForm" label-width="auto">
        <el-form-item label="是否删除服务端分支" prop="deleteRemote">
          <el-radio-group v-model="deleteForm.deleteRemote">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="sureDelete()">确定</el-button>
      </span>
    </el-dialog>
    <!-- 新增分支 -->
    <el-dialog
      title="新增分支"
      v-model:visible="newBranchVisible"
      width="30%"
      :close-on-click-modal="false"
      :before-close="newDialogClose"
    >
      <el-form ref="branchForm" :model="branchForm" label-width="auto" :rules="rules">
        <el-form-item label="分支" prop="name">
          <el-select v-model="branchForm.name" filterable placeholder="请选择分支" style="width:100%">
            <el-option v-for="item in branchList" :key="item.id" :label="item.branchName" :value="item.branchName" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="branchForm.description" placeholder="请输入描述" />

        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newBranchVisible = false">取消</el-button>
        <el-button type="primary" @click="sureDAdd()">确定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>

import {
  apiBuniessSelectCodeLineBySystemId,
  apiBuniessDeleteCodeLineBySystemId,
  apiCmdbCheckCodeLinePath,
  apiCmdbFindBranch,
  apiCmdbNewBranch
} from '@/api/vone/cmdb/server'

import { apiBaseDictNoPage } from '@/api/vone/base/dict'

export default {
  components: {

  },
  props: {
    active: {
      type: Number,
      default: 0
    }

  },
  data() {
    return {
      tableData: {},

      deleteVisible: false,
      deleteForm: {
        deleteRemote: 0
      },
      tableOptions: {

      },
      rules: {
        name: [
          { required: true, message: '请选择分支名称', trigger: 'change' }
        ]
      },
      TYPE: '',
      tableLoading: false,
      formData: {
        applicationId: this.$route.params.systemId
      },
      newBranchVisible: false,
      branchForm: {
      },
      branchList: [],
      dictBranch: []
    }
  },
  mounted() {
    this.TYPE = this.$route.params.type

    this.getTableData()

    if (this.type === 1) {
      // this.getCodeLine();
    } else if (this.type === 0) {
      this.tableData = [{}]
    }
  },
  methods: {
    async getDictList() {
      const res = await apiBaseDictNoPage({
        type: 'BRANCH',
        state: true
      })
      if (!res.isSuccess) {
        return
      }
      this.dictBranch = res.data
    },
    addBranch() {
      this.newBranchVisible = true
      this.getBranchList()
      this.getDictList()
    },
    deleteDialogClose() {
      // this.$refs.deleteForm.reset()
      this.deleteVisible = false
    },
    newDialogClose() {
      this.newBranchVisible = false
      this.$refs.branchForm.resetFields()
    },

    async getTableData() {
      this.tableLoading = true
      let params = {}
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }

      const res = await apiBuniessSelectCodeLineBySystemId(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },

    async openDelete(row) {
      // this.deleteForm.codelineId = row.dictCodelineId
      // this.deleteVisible = true

      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
      const res = await apiBuniessDeleteCodeLineBySystemId(
        this.$route.params.systemId, row.code
      )
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableData()
    },
    async sureDelete() {

    },
    async sureDAdd() {
      // 从代码库查出的分支名称与分支字典CODE作对比,字典里有这个分支code则取字典的分支名称,code,branchKey,没有则取代码库实际的名称,同时把名称作为code保存,branchKey为空
      this.$refs.branchForm.validate((valid) => {
        if (valid) {
          this.savefun()
        }
      })
    },
    async savefun() {
      const hasDictBranch = this.dictBranch.filter(r => r.code == this.branchForm.name)

      const { isSuccess, msg } = await apiCmdbNewBranch(
        {

          applicationId: this.$route.params.systemId,
          branchKey: hasDictBranch.length ? hasDictBranch[0].code : '',
          code: hasDictBranch.length ? hasDictBranch[0].code : this.branchForm.name,
          description: this.branchForm.description,
          name: hasDictBranch.length ? hasDictBranch[0].name : this.branchForm.name,
          status: 'UNDER_DEVELOPMENT'
        }
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('新增分支成功')
      this.newDialogClose()
      this.getTableData()
    },
    async getBranchList() {
      const { data, isSuccess } = await apiCmdbFindBranch(this.$route.params.systemId)
      if (!isSuccess) {
        return
      }
      this.branchList = data
    },

    async checkById(row) {
      const { success } = await apiCmdbCheckCodeLinePath({
        systemId: this.$route.params.systemId,
        branchName: row.codeWarehousePath,
        path: `/${this.codeRepPath}`
      })
      if (!success) {
        this.$message.warning('分支检测失败')
        return
      }
      this.$message.success('分支检测成功')
      this.getTableData()
    },

    stepFoaward() {
      this.$emit('stepFoaward')
    },
    async saveNext() {
      // await this.saveModule();
      // this.activeIndex++;
      this.$emit('success')
    }
  }
}
</script>
