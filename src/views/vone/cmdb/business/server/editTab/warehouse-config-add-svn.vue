<template>
  <el-dialog title="新增仓库" v-bind="$attrs" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <el-form ref="form" :model="formModel" :rules="formRules">
      <el-form-item label="操作类型" prop="manageType">
        <el-radio-group v-model="formModel.manageType">
          <el-radio label="import">导入</el-radio>
          <el-radio label="create">创建</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formModel.manageType === 'import'" label="仓库地址" prop="name">
        <el-row type="flex" class="custom-group">
          <div class="el-input-group__prepend">{{ url + "/" }}</div>
          <el-select v-if="adressList.length > 0" v-model="formModel.name" placeholder="请选择或输入库名称">
            <el-option v-for="(item, index) in adressList" :key="index" :label="item" :value="item" />
          </el-select>
          <el-input v-if="adressList.length === 0" v-model="formModel.adress" placeholder="请选择或输入库名称" />
        </el-row>
      </el-form-item>
      <el-form-item v-if="formModel.manageType === 'create'" label="仓库地址" prop="name">
        <el-input v-model="formModel.name" placeholder="请输库名称">
          <template slot="prepend">{{ url + "/" }}</template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="formModel.manageType === 'create' && strage !== 'strategy_parallel'" label="创建分支" prop="dictCodelineId">
        <el-select v-model="formModel.dictCodelineId" multiple clearable placeholder="请选择" style="width: 100%">
          <el-option v-for="(item, index) in strageList" :key="index" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="des">
        <el-input v-model="formModel.des" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <div class="footer">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="save">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  apiBuniesGetRepNameListByEngineId,
  apiBuniesScreateSvnRepPath,
  apiBuniessDictCodeLineStrategy } from '@/api/vone/cmdb/server'
export default {
  props: {
    url: {
      type: String,
      default: null
    },
    engineId: {
      type: String,
      default: null
    },
    strage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formModel: {
        manageType: 'import'
      },
      formRules: {
        manageType: [
          {
            required: true,
            message: '请选择操作类型'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入库名称'
          },
          {
            pattern: '^[\\u4E00-\\u9FA5A-Za-z0-9_\\-]{0,100}$',
            message: '仓库地址不合法，只允许中文、字母、数字和下划线、横线'
          }
        ],
        dictCodelineId: [
          {
            required: true,
            message: '请选择分支'
          }
        ]
      },
      adressList: [],
      strageList: [],
      saveLoading: false
    }
  },
  watch: {
    '$attrs.visible'(v) {
      if (v) {
        this.getAdress()
        this.getStrage()
      }
    }
  },
  methods: {
    async getAdress() {
      const { data, success, message } = await apiBuniesGetRepNameListByEngineId({
        engineId: this.engineId
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.adressList = data
    },
    async getStrage() {
      const { data, success, message } = await apiBuniessDictCodeLineStrategy(this.strage)
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.strageList = data
    },
    async save() {
      this.saveLoading = true
      const formData = {
        engineId: this.engineId,
        strategy: this.strage,
        ...this.formModel
      }
      if (formData.manageType === 'create' && this.strage !== 'strategy_parallel') {
        formData.dictCodelineId = formData.dictCodelineId.join(',')
      }
      const { success, message } = await apiBuniesScreateSvnRepPath(formData)
      this.saveLoading = false
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.$message.success(message)
      this.$emit('update:visible', false)
      this.$emit('success', this.formModel.name)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-group {
  display: table;
  width: 100%;
  :deep() {
    :not(:first-child) {
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}
.footer{
  text-align: right;
}
</style>

