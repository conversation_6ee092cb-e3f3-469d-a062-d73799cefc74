<template>
  <el-dialog title="编辑" v-bind="$attrs" :close-on-click-modal="false" :visible="visible">
    <el-form ref="dialogForm" v-loading="loading" :model="ruleForm" :rules="rules">
      <el-form-item label="应用名称" prop="name">
        <el-input v-model.trim="ruleForm.name" />
      </el-form-item>
      <el-form-item label="CI/CD类型" prop="type">
        <el-select v-model="ruleForm.type" clearable placeholder="请选择CI/CD类型" style="width:100%" disabled>
          <el-option label="构建" value="CI" />
          <el-option label="部署" value="CD" />
          <el-option label="构建+部署" value="CI_CD" />
        </el-select>
      </el-form-item>
      <el-form-item label="责任人" prop="responsible">
        <vone-remote-user v-model="ruleForm.responsible" />
      </el-form-item>
      <el-form-item label="环境" prop="envDatas">

        <el-cascader v-model="ruleForm.envDatas" :show-all-levels="false" :options="envList" :props="props" style="width: 100%" filterable clearable />
      </el-form-item>
      <el-form-item label="应用描述" prop="description">
        <el-input v-model="ruleForm.description" type="textarea" maxlength="200" />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="_close">取 消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit()">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import {
  apiBaseSelectBaseDatagById,
  apiBasePutApplicationBaseData
} from '@/api/vone/cmdb/server'

import pick from 'lodash/pick'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {

      props: {
        multiple: true,
        value: 'code',
        children: 'children',
        label: 'name',
        expandTrigger: 'hover'
      },
      loading: false,
      envList: [],
      envListMap: {},

      ruleForm: {},
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过200位的应用名称',
            min: 1,
            max: 200
          }
        ],
        personLiable: [{ required: true, message: '请选择责任人' }],
        type: [{ required: true, message: '请选择类型' }],
        envDatas: [
          {
            type: 'array',
            required: true,
            validator: (r, v, c) => {
              if (this.ruleForm.envDatas && this.ruleForm.envDatas.length) c()
              else c('请选择环境')
            }
          }
        ]
      },
      saveLoading: false
    }
  },
  mounted() {
    this.getBasicInfo()
  },
  methods: {
    async getBasicInfo() {
      await this.getEnvList()

      this.loading = true
      var res = await apiBaseSelectBaseDatagById(this.id
      )
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.ruleForm = res.data

      const ENV = res.data.applicationEnvs
        .map(r => {
          const env = r.echoMap.envKey.parentId
          if (env) {
            return [undefined, r.envKey]
          }
        })
        .filter(v => !!v)
      this.$set(this.ruleForm, 'type', res.data.type ? res.data.type.code : '')
      this.$set(this.ruleForm, 'envDatas', ENV)
    },
    async submit() {
      try {
        this.saveLoading = true
        const formData = pick(this.ruleForm, ['id', 'name', 'personLiable', 'type', 'description', 'responsible'])
        formData.applicationEnvs = this.ruleForm.envDatas.map(r => ({
          applicationId: formData.id,
          envKey: r[1]
        }))
        const res = await apiBasePutApplicationBaseData(formData)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          this.$emit('update:visible', false)
          return
        }
        this.$message.success('保存成功')
        this.$emit('success')
        this.$emit('update:visible', false)
      } catch (e) {
        this.saveLoading = false
      }
    },

    // 查询环境下拉框数据
    async getEnvList() {
      this.loading = true
      const res1 = await apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      }
      )

      if (!res1.isSuccess) {
        this.loading = false
        return
      }

      res1.data.forEach(element => {
        element.parentId = element.echoMap && element.echoMap.parentId ? element.echoMap.parentId.id : ''
        element.parentName = element.echoMap && element.echoMap.parentId ? element.echoMap.parentId.name : ''
      })

      // 按parentId将数据分组
      const map = {}

      res1.data.forEach((item) => {
        map[item.parentId] = map[item.parentId] || []
        map[item.parentId].push(item)
      })

      const DATA = Object.keys(map).map((parentId) => {
        return {
          value: parentId,
          name: map[parentId].find(r => r.parentId == parentId).parentName,
          children: map[parentId]
        }
      })

      // 将数据字典数据转成Map，用于修改数据回显，回显一定要先加载环境数据字典数据
      // this.envListMap = reduce(dictArr, (r, v) => (r[v.value] = v) && r, {})

      this.envList = DATA

      this.loading = false
    },
    _close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
