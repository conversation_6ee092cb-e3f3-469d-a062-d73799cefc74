<template>
  <div>

    <vone-edit-wrapper show-footer>
      <vone-card-wrapper title="基本信息" class="contain">

        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-position="top">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="应用名称" prop="name">
                <el-input v-model.trim="ruleForm.name" placeholder="请输入长度不超过200位的应用名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="应用编号" prop="code">
                <el-input v-model="ruleForm.code" placeholder="请输入应用编号" />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="责任人" prop="responsible">
                <vone-remote-user v-model="ruleForm.responsible" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="CI/CD类型" prop="type">
                <el-radio-group v-model="ruleForm.type">
                  <el-radio :label="'CI'">构建</el-radio>
                  <el-radio :label="'CD'">部署</el-radio>
                  <el-radio :label="'CI_CD'">构建+部署</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="应用类型" prop="programType">
                <el-select v-model="ruleForm.programType" placeholder="请选择应用类型" style="width:100%">
                  <el-option v-for="(item, index) in typeList" :key="index" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="版本管理引擎" prop="versionEngine">
                <el-select v-model="ruleForm.versionEngine" placeholder="请选择版本管理引擎" style="width:100%">
                  <el-option v-for="(item, index) in engineList" :key="index" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="描述" prop="description">
                <el-input v-model="ruleForm.description" placeholder="请输入描述" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                v-if="
                  ruleForm.type === 'CI' ||
                    ruleForm.type === 'CI_CD'
                "
                label="分支策略"
                prop="branchingStrategyKey"
                :rules="[
                  { required: true, message: '请选择分支策略'} ]"
              >
                <el-select v-model="ruleForm.branchingStrategyKey" placeholder="请选择版分支策略" style="width:100%">
                  <el-option v-for="(item, index) in strategyList" :key="index" :label="item.name" :value="item.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="环境" prop="envCode">
                <el-cascader v-model="ruleForm.envCode" :show-all-levels="false" :options="envList" :props="props" style="width: 100%" filterable clearable />

              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-select v-model="ruleForm.status" placeholder="请选择状态" style="width:100%">
                  <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value" />
                </el-select>

              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      </vone-card-wrapper>
      <el-row slot="footer">
        <el-button @click="cancle">取消</el-button>

        <el-button type="primary" @click="saveInfo">确定</el-button>
      </el-row>
    </vone-edit-wrapper>

    <!-- 对话框 -->
    <el-dialog title="选择环境" width="60%" v-model="dialogFormVisible" :close-on-click-modal="false">
      <el-form />

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="sure">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
const typeList = [
  { name: '服务器端', id: 'WEB_APP' },
  { name: '移动应用-安卓', id: 'MOBILE_APP' }
]
const devLanguageList = [
  { name: 'Java', id: 'Java' },
  { name: 'NodeJS', id: 'NodeJS' },
  { name: 'PHP', id: 'PHP' },
  { name: 'Python', id: 'Python' },
  { name: 'Go', id: 'Go' },
  { name: '其它脚本语言', id: 'other' }
]

import {
  apiBaseAddApplicationBaseData
} from '@/api/vone/cmdb/server'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import { apiBaseEngineNoPage } from '@/api/vone/base/engine'

export default {
  data() {
    return {

      loading: false,
      dialogFormVisible: false,
      ruleForm: {
        type: 'CI'
      },
      strategyList: [],
      dialogList: [],
      checkList: [],
      envList: [],
      versionList: [],
      rules: {
        code: [
          { required: true, message: '请输入应用编号' },
          {
            pattern: '^[a-zA-Z0-9\\_\\-]{1,50}$',
            message: '请输入不超过50个数字、字母、-和_组成的编号'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过200位的应用名称',
            min: 1,
            max: 200
          }
        ],
        responsible: [{ required: true, message: '请选择责任人' }],
        type: [{ required: true, message: '请选择类型' }],
        programType: [{ required: true, message: '请选择应用类型' }],
        // devLanguage: [{ required: true, message: '请选择' }],
        // devVersion: [{ required: true, message: '请选择' }],
        versionEngine: [{ required: true, message: '请选择版本管理引擎' }],
        description: [{ required: false }],
        // strategy: [{ required: true, message: '请选择分支策略' }],
        envCode: [
          {
            type: 'array',
            required: true,
            validator: (r, v, c) => {
              if (this.ruleForm.envCode && this.ruleForm.envCode.length) c()
              else c('请选择环境')
            }
          }
        ]
      },
      typeList,
      devLanguageList,
      props: {
        multiple: true,
        value: 'code',
        children: 'children',
        label: 'name',
        expandTrigger: 'hover'
      },
      engineList: [],
      statusList: []
      // devVersionList
    }
  },
  mounted() {
    this.getVersion()
    this.getEnvList()
    this.getStrategyList()
    this.getStatusList()
  },
  methods: {
    // 查询状态下拉框
    async getStatusList() {
      const res = await apiBaseDictEnumList(['ApplicationStatus'])
      if (!res.isSuccess) {
        this.loading = false
        return
      }
      this.statusList = res.data.ApplicationStatus
    },

    // 查询环境下拉框数据
    async getEnvList() {
      this.loading = true
      const res1 = await apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      }
      )

      if (!res1.isSuccess) {
        this.loading = false
        return
      }

      res1.data.forEach(element => {
        element.parentId = element.echoMap && element.echoMap.parentId ? element.echoMap.parentId.id : ''
        element.parentName = element.echoMap && element.echoMap.parentId ? element.echoMap.parentId.name : ''
      })

      // 按parentId将数据分组
      const map = {}

      res1.data.forEach((item) => {
        map[item.parentId] = map[item.parentId] || []
        map[item.parentId].push(item)
      })

      const DATA = Object.keys(map).map((parentId) => {
        return {
          code: parentId,
          name: map[parentId].find(r => r.parentId == parentId).parentName,
          children: map[parentId]
        }
      })

      // 将数据字典数据转成Map，用于修改数据回显，回显一定要先加载环境数据字典数据
      // this.envListMap = reduce(dictArr, (r, v) => (r[v.value] = v) && r, {})

      this.envList = DATA

      this.loading = false
    },
    // 查询分支策略下拉框数据
    async getStrategyList() {
      this.loading = true
      const res = await apiBaseDictNoPage({
        type: 'BRANCHING_STRATEGY'
      }
      )
      this.loading = false
      if (!res.isSuccess) {
        return
      }
      this.strategyList = res.data
    },

    // 查询版本管理引擎下拉框数据
    async getVersion() {
      this.loading = true
      const res = await apiBaseEngineNoPage({
        instance: 'SCM_AGENT'
      })
      this.loading = false
      if (!res.isSuccess) {
        this.loading = false
        return
      }
      this.engineList = res.data
    },
    // 环境对话框
    shoeEnv() {
      this.dialogFormVisible = true
    },
    sure() {
      this.dialogFormVisible = false
      // this.ruleForm.envId = this.checkList
      // this.$refs.ruleForm.validateField('envId')
    },
    async saveInfo() {
      await this.$refs.ruleForm.validate()

      this.loading = true

      const applicationEnvs = this.ruleForm.envCode.map((r) => ({
        envKey: r[1]
      }))

      const ENGINE = this.engineList.filter(
        (c) => c.id == this.ruleForm.versionEngine
      )

      const applicationEngines = ENGINE.map(r => ({
        engineClassifyKey: r.classify,
        engineId: r.id,
        engineInstanceKey: r.instance.code
      }))

      const res = await apiBaseAddApplicationBaseData(

        {
          branchingStrategyKey: this.ruleForm.branchingStrategyKey,
          code: this.ruleForm.code,
          description: this.ruleForm.description,
          name: this.ruleForm.name,
          programType: this.ruleForm.programType,
          responsible: this.ruleForm.responsible,
          status: this.ruleForm.status,
          type: this.ruleForm.type,
          applicationEngines,
          applicationEnvs
        }
      )
      this.loading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.$router.go(-1)
    },
    async startConfig() {
      if (await this.submitForm()) {
        this.$router.push({
          name: 'bussiness_details_addServerConfig',
          params: { systemId: this.ruleForm.id, type: 0 }
        })
      }
    },
    cancle() {
      this.$router.go(-1)
    }

  }
}
</script>

<style lang="scss" scoped>
.dialog {
  list-style: none;
  span {
    margin-right: 30px;
  }
  li {
    height: 40px;
    line-height: 40px;
  }
}
.env {
  height: 20px;
  width: 30px;
  background-color: #909399;
  padding: 5px;
  margin: 0 2px;
  color: #fff;
}
.contain {
  height: calc(100vh - 150px);
  overflow-y: auto;
}
:deep(.el-col-6) {
  height: 90px;
}
</style>
