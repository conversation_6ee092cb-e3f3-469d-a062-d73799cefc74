<template>
  <page-wrapper>
    <vone-cards :loading="pageLoading" :data="tableData" :row-count="3">
      <template slot="toolbar">
        <div class="cards-actions">
          <el-button type="primary" icon="el-icon-plus" @click="addBussiness">新建</el-button>
        </div>
      </template>
      <template v-slot="{ row }">
        <a @click="configure(row.id)">
          <vone-card :title="row.name" :title-content="row.name" :actions="rowActions">
            <template v-slot:icon>
              <i class="el-icon-menu" style="color:#409EFF" />
            </template>
            <el-row class="cardText">
              <div class="cardText">维护人:&nbsp;&nbsp;{{ row.responsibleName }}</div>
              <div class="cardText">标识:&nbsp;&nbsp;{{ row.id }}</div>
              <div class="cardText">描述 :&nbsp;&nbsp;{{ row.description }}</div>
            </el-row>
            <template v-slot:desc>
              {{ row.content }}
            </template>
          </vone-card>
        </a>
      </template>
    </vone-cards>

    <!-- 新增的对话框 -->
    <add-dialog
      v-model="bussinessDialogParam.visible"
      v-bind="bussinessDialogParam"
      @success="getSystemList"
    />
  </page-wrapper>
</template>

<script>
import {
  apiBaseBusinessSystemList,
  apiBasBusinessSystemDeleteById
} from '@/api/vone/cmdb/business'
import addDialog from './system/add-dialog.vue'
export default {
  components: {
    addDialog
  },
  filters: {
    // 当标题字数超出时，超出部分显示’...‘。此处限制超出部分即触发隐藏效果
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 31) {
        return value.slice(0, 31) + '...'
      }
      return value
    }
  },
  data() {
    return {
      pageLoading: false,
      loading: false,
      keyWord: '',
      bussinessDialogParam: { visible: false },
      form: {},
      tableData: [],
      total: 0,
      dataList: [],
      rowActions: [
        {
          text: '概览',
          icon: 'el-icon-pie-chart',
          onClick: ({ row }) => this.overView(row.id)
        },
        {
          text: '配置',
          icon: 'iconfont el-icon-application-setting',
          onClick: ({ row }) => this.configure(row.id)
        },
        {
          text: '修改',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editById(row)
        },
        {
          text: '删除',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.deleteById(row)
        }
      ]
    }
  },
  mounted() {
    this.getSystemList()
  },

  methods: {
    // 查询列表数据
    async getSystemList(params) {
      this.pageLoading = true
      this.loading = true
      const { data, success, message } = await apiBaseBusinessSystemList({
        ...params
      })
      if (!success) {
        this.pageLoading = false
        this.loading = false
        return this.$message.error(message)
      }
      this.tableData = data
      this.dataList = data.data

      this.pageLoading = false
      this.loading = false
    },
    // async getSystemListByKey() {
    //   this.pageLoading = true;
    //   this.loading = true;
    //   const { data, success, message } = await apiBaseBusinessSystemList({
    //     keyword: this.keyWord,
    //   });
    //   if (!success) {
    //     this.pageLoading = false;
    //     this.loading = false;
    //     return this.$message.warning(message);
    //   }
    //   this.pageLoading = false;
    //   this.loading = false;
    //   this.tableData = data;
    //   this.dataList = data;
    // },
    // 新建业务系统
    addBussiness() {
      this.bussinessDialogParam = {
        visible: true,
        dicTitle: '业务系统新增'
      }
    },
    // 配置
    configure(id) {
      this.$router.push({
        name: 'cmdb_business_configure',
        params: { id, tab: 'server' }
      })
    },
    // 修改
    editById(row) {
      this.bussinessDialogParam = {
        visible: true,
        dicTitle: '业务系统编辑',
        id: row.id,
        data: row
      }
    },
    // 删除
    async deleteById(row) {
      await this.$confirm('确定删除该信息吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
        .then(async(actions) => {
          const { success, message } = await apiBasBusinessSystemDeleteById({
            id: row.id
          })
          if (!success) {
            return this.$message.error(message)
          }
          this.$message.success(message)
          // this.$refs.cmdbTable.refresh()
          this.getSystemList()
        })
        .catch(() => { })
    },
    // 概览
    overView(id) {
      this.$router.push({
        name: 'cmdb_business_over_view',
        params: { id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.cardText{
  padding-left: 5px;
  padding-top: 5px;
  color: #909399;
  font-size: 12px;
}
.cards-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
</style>
