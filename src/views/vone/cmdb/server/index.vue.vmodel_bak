<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="server-card"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          show-basic
          v-model:extra="extraData"
          @getTableData="getServerList"
        />

      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('cmdb_server_addServer')" @click="addServer">新增</el-button>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getServerList"
        />
      </template>
    </vone-search-wrapper>

    <vone-cards ref="server-card" v-loading="loading" :data="tableData" :row-count="4" :height="cardHeight" @updateData="getServerList">

      <template v-slot="{ row }">
        <div :class="[ !$permission('cmdb_server_detail_view') ?'disabledLinkCls':'linkCls']" @click="toConfigDetails(row)">
          <vone-card :title="row.name" :title-content="row.name" :actions-num="3" :actions="rowActions">
            <template v-slot:icon>
              <i class="iconfont el-icon-webxitong" style="color:#409EFF" />
            </template>

            <el-row style="font-size: 12px;color: #909399;">
              <vone-toolitip :content="row.code" :label="'应用编号 '" />
              <vone-toolitip :content="row.updateTime" :label="'更新时间 '" />
              <vone-toolitip :content="row.description" :label="'描述 '" />
            </el-row>

            <div slot="desc">
              <span v-if="row.responsible && row.echoMap && row.echoMap.responsible">
                <vone-user-avatar :avatar-path="row.echoMap.responsible.avatarPath" :name="row.echoMap.responsible.name" />
              </span>
              <span v-else> {{ row.responsible }}</span>
            </div>
          </vone-card>
        </div>

      </template>
    </vone-cards>

    <editServerDialog v-if="editParam.visible" v-bind="editParam" v-model:visible="editParam.visible" @success="getServerList" />

    <!-- 权限分配抽屉 -->
    <division v-if="divisionDrawerParam.visible" v-bind="divisionDrawerParam" v-model:visible="divisionDrawerParam.visible" @success="getServerList" />

  </page-wrapper>
</template>
<script>

import editServerDialog from '../business/server/edit-server-dialog.vue'
import division from '../business/server/division.vue'

import { apiBaseDelApplicationBaseData, apiBaseGetapplication } from '@/api/vone/cmdb/server'

export default {
  components: {
    editServerDialog,
    division
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '服务应用名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入服务应用名称'
        },
        {
          key: 'responsible',
          name: '负责人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择负责人'
        }

      ],
      formData: {},
      loading: false,
      divisionDrawerParam: { visible: false },
      editParam: { visible: false },

      tableData: {},
      rowActions: [

        {
          type: 'text',
          text: '配置',
          icon: 'iconfont el-icon-application-setting',
          onClick: ({ row }) => this.editById(row, 'edit'),
          disabled: !this.$permission('cmdb_server_set')
        },
        {
          type: 'text',
          text: '权限分配',
          icon: 'iconfont el-icon-application-user-permission',
          onClick: ({ row }) => this.resDivision(row),
          disabled: !this.$permission('cmdb_server_authority')
        },
        // {
        //   type: 'text',
        //   text: '拓扑图',
        //   icon: 'iconfont el-icon-application-topology',
        //   onClick: ({ row }) => this.openGraph(row),
        //   disabled: !this.$permission('cmdb_server_graph')
        // },
        {
          type: 'text',
          text: '编辑',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editBaseInfo(row),
          disabled: !this.$permission('cmdb_server_editServer')

        },
        {
          type: 'text',
          text: '删除',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.deleteById(row),
          disabled: !this.$permission('cmdb_server_delete')
        }
      ]
    }
  },
  computed: {
    cardHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(100vh - 190px - ${height})`
    }
  },
  mounted() {
    // this.getServerList()
  },
  methods: {
    // 编辑基本信息
    editBaseInfo(row) {
      this.editParam = { visible: true, id: row.id }
    },
    // 权限分配
    resDivision(row) {
      this.divisionDrawerParam = { visible: true, id: row.id }
    },
    async getServerList() {
      this.loading = true
      let params = {}

      const tableAttr = this.$refs['server-card'].exportTableQueryData()

      this.$set(tableAttr, 'sort', 'updateTime')
      this.$set(tableAttr, 'order', 'descending')
      params = {
        ...tableAttr,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await apiBaseGetapplication(
        params
      )
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },
    // 新建服务应用
    addServer() {
      this.$router.push({
        name: 'cmdb_server_addServer'
      })
    },
    // 修改
    editById(row, type) {
      this.$router.push({
        name: 'cmdb_server_set',
        params: { systemId: row.id, type: 2 }
      })
    },
    // 查看
    toConfigDetails(row) {
      if (!this.$permission('cmdb_server_detail_view')) return
      // if (this.$permission('cmdb_server_detail_view')) {
      this.$router.push({
        name: 'cmdb_server_detail_view',
        params: { systemId: row.id, type: 1 }
      })
      // }
    },
    // 跳转拓扑图
    openGraph(row) {
      this.$router.push({
        path: '/cmdb/server/graph/' + row.id
        // name: 'cmdb_server_graph',
        // params: { systemId: row.id }
      })
    },
    // 删除
    async deleteById(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      })
        .then(async(actions) => {
          const res = await apiBaseDelApplicationBaseData(
            [row.id]
          )
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.getServerList()
          this.$message.success('删除成功')
        })
        .catch(() => { })
    }

  }
}
</script>

<style lang="scss" scoped>
.cardText {
  padding-top: 5px;
  color: #909399;
  font-size: 12px;
}
.cards-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.disabledLinkCls {
  cursor: not-allowed !important;
}
.linkCls {
  cursor: pointer;
}
.list-view {
  // height: calc(100vh - 182px);
  // margin-top: -8px;
}
</style>
