<template>
  <vone-drawer
    v-model="visible"
    size="lg"
    :before-close="onClose"
  >
    <div slot="title" class="drawer-title">
      <div class="drawer-title-text">{{ `数据库组件【${dbData.name}】详情` }}
        <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />
        <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
      </div>
    </div>

    <div v-loading="drawerLoading" class="pageBox">
      <div class="title" style="margin-bottom: 0;">
        <strong>
          基本信息
        </strong>
      </div>
      <div class="formBox" style="padding-top: 0px; padding-bottom: 10px;">
        <vone-desc :column="2">
          <vone-desc-item label="名称">
            {{ dbData.name }}
          </vone-desc-item>
          <vone-desc-item label="类型">
            {{ dbData.dbComponentsTypeKey }}
          </vone-desc-item>
          <vone-desc-item label="组件IP">
            {{ dbData.serverIp }}
          </vone-desc-item>
          <vone-desc-item label="组件端口">
            {{ dbData.serverPort }}
          </vone-desc-item>
          <vone-desc-item label="安装路径">
            {{ dbData.installationPath }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="描述">
            {{ dbData.description }}
          </vone-desc-item>
        </vone-desc>

      </div>
      <div class="title">
        <strong>
          关联信息
        </strong>
      </div>
      <div class="formBox">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <!-- 高级属性 -->
          <el-tab-pane label="高级属性" name="attribute">
            <main style="height:calc(100vh - 400px)">
              <vxe-table
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="attributeData"
                :column-config="{ minWidth:'120px' }"
                row-id="id"
              >
                <vxe-column title="标识" field="key" />
                <vxe-column field="name" title="名称" />
                <vxe-column field="value" title="值">
                  <template #default="{row}">
                    {{ row.key == 'INIT_PASSWORD' ? '******' :row.value }}
                  </template>
                </vxe-column>
              </vxe-table>
            </main>
          </el-tab-pane>
          <!-- 服务应用 -->
          <el-tab-pane label="服务应用" name="apply">
            <main style="height:calc(100vh - 440px)">
              <vxe-table
                ref="db-apply-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="dbApplyData.records"
                :column-config="{ minWidth:'120px' }"
                row-id="id"
              >
                <vxe-column title="应用名称" field="name" />
                <vxe-column field="type" title="CI/CD类型">
                  <template #default="{row}">
                    <span v-if="row.type">
                      {{ row.type.desc }}
                    </span>
                    <span v-else>{{ row.type }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="applicationEnvs" title="环境">
                  <template #default="{row}">
                    <span v-if="row.applicationEnvs && row.applicationEnvs.length">
                      {{ row.applicationEnvs.map(r=>r.envKey).join(',') }}
                    </span>
                    <span v-else>{{ row.applicationEnvs }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </main>
            <vone-pagination ref="pagination" style="position: static;" :total="dbApplyData.total" @update="getApplyPage" />
          </el-tab-pane>
          <!-- 实例用户 -->
          <el-tab-pane label="实例用户" name="exampleUser">
            <main style="height:calc(100vh - 400px)">
              <vxe-table
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="exampleUser"
                :column-config="{ minWidth:'120px' }"
                row-id="id"
              >
                <vxe-column title="实例名称" field="instanceName" />
                <vxe-column field="instanceDesc" title="实例描述" />
                <vxe-column field="userName" title="用户名称" />
                <vxe-column field="userDesc" title="用户描述" />
              </vxe-table>
            </main>
          </el-tab-pane>
          <!-- DB执行历史 -->
          <el-tab-pane label="DB执行历史" name="history">
            <div style="height:calc(100vh - 440px)">
              <vxe-table
                ref="db-history-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="dbLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="dbHistoryData.records"
                :column-config="{ minWidth:'120px' }"
                row-id="id"
              >
                <vxe-column title="SQL文件路径" field="sqlPath" />
                <vxe-column field="tagStyle" title="环境">
                  <template #default="{row}">
                    <el-tooltip
                      v-if="row.tagStyle !== undefined"
                      :content="row.env"
                    >
                      <el-tag effect="dark" :color="row.tagStyle">{{ row.envKey }}</el-tag>
                    </el-tooltip>
                    <span v-else>{{ row.env }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="success" title="执行结果">
                  <template #default="{row}">
                    <span v-if="row.success == 1">成功</span>
                    <span v-else>失败</span>
                  </template>
                </vxe-column>
                <vxe-column field="executionTime" title="执行时间" />
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </div>
            <vone-pagination ref="hitoryPagination" :total="dbHistoryData.total" style="position: static;" @update="getDbHistoryPage" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>
<script>
import {
  apiCmdbDbSeletById,
  apiCmdbDbHistoryPage
} from '@/api/vone/cmdb/database'
import { apiBaseGetapplication } from '@/api/vone/cmdb/server'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    tableList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableOptions: {},
      activeName: 'attribute',
      drawerLoading: false,
      tableLoading: false,
      dbData: {},
      userInstanceDatas: [],
      applicationDbCompentsDatas: [],
      executionDatas: [],
      attributeData: [], // 高级属性
      exampleUser: [], // 实例用户
      currentIndex: undefined, // 当前数据的索引
      tableHistoryOptions: {},
      dbLoading: false, // db执行历史列表loading
      dbHistoryData: {}, // db执行历史
      formData: {
        dbComponentsId: this.id
      },
      applyLoading: false, // 服务应用列表loading
      formDataApply: { // 服务应用查询条件
        dbComponentsId: this.id
      },
      tableApplyOptions: {}, // 服务应用操作
      dbApplyData: {} // 服务应用
    }
  },
  watch: {
    visible(v) {
      if (!v) return
    }
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex(item => item.id === this.id)
    this.activeName = 'attribute'
    this.selectDbDataById()
  },
  methods: {
    async selectDbDataById(val) {
      this.drawerLoading = true
      const { data, isSuccess, msg } = await apiCmdbDbSeletById(val || this.id)
      this.drawerLoading = false
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.dbData = data
      this.activeName = 'attribute'

      // 高级属性--------------
      this.attributeData = data.dbComponentsExtends
      // 服务应用信息--------------
      this.applicationDbCompentsDatas = data.applicationDbs

      // 实例用户----------------
      const userInstanceDatas = []
      data.dbComponentsUserInstances.forEach((item) => {
        const userInstanceData = {}
        data.dbComponentsUsers.forEach((userItem) => {
          if (item.dbComponentsUserId === userItem.id) {
            userInstanceData.userName = userItem.userName
            userInstanceData.userDesc = userItem.description
          }
        })
        data.dbComponentsInstances.forEach((instanceItem) => {
          if (item.dbComponentsInstanceId === instanceItem.id) {
            userInstanceData.instanceName = instanceItem.name
            userInstanceData.instanceDesc = instanceItem.description
          }
        })
        userInstanceDatas.push(userInstanceData)
      })
      this.exampleUser = userInstanceDatas

      this.loading = false
    },
    // 分页查询服务应用
    async getApplyPage() {
      this.applyLoading = true
      let params = {}
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataApply }
      }
      const res = await apiBaseGetapplication(params)
      this.applyLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.dbApplyData = res.data
    },

    handleClick(tab, event) {
      if (tab.name == 'history') {
        this.getDbHistoryPage()
      } else if (tab.name == 'apply') {
        this.getApplyPage()
      }
    },
    // 分页查询db执行历史
    async getDbHistoryPage() {
      this.dbLoading = true
      let params = {}
      const tableAttr = this.$refs.hitoryPagination?.pageObj || { current: 1, size: 20 }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiCmdbDbHistoryPage(params)
      this.dbLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.dbHistoryData = res.data
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.selectDbDataById(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.selectDbDataById(this.tableList[this.currentIndex].id)
    }
  }
}
</script>
<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .formBox {
    padding: 10px;
    padding-bottom: 0;
  }
  :deep(.el-form .el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-form-item__content) {
    display: flex;
  }
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
