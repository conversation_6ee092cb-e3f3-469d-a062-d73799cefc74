<template>
  <el-dialog
    class="dialogContainer"
    :title="title"
    :visible="visible"
    width="45%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form ref="versionForm" :model="versionForm" label-position="top" :rules="rules" label-width="120px">
      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="versionForm.name" placeholder="请输入名称" maxlength="20" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布状态" prop="stateId">
            <el-select v-model="versionForm.stateId" placeholder="请选择发布状态">
              <el-option label="规划中" :value="1" />
              <el-option label="进行中" :value="2" />
              <el-option label="发布成功" :value="3" />
              <el-option label="发布失败" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="计划开始时间" prop="planStime">
            <el-date-picker
              v-model="versionForm.planStime"
              format="yyyy-MM-dd HH:mm"
              type="datetime"
              placeholder="选择计划开始时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划发布时间" prop="planRtime">
            <el-date-picker
              v-model="versionForm.planRtime"
              :picker-options="pickerOptions"
              format="yyyy-MM-dd HH:mm"
              type="datetime"
              placeholder="选择计划发布时间"
              style="width: 100%"
              @change="changeDate"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="关联产品集版本" prop="productsetVersionId">
            <el-select v-model="versionForm.productsetVersionId" placeholder="请选择产品集版本" multiple>
              <el-option v-for="item in productsetVersionList" :key="item.id" :label="item.name" :value="item.id"
                >{{ item.echoMap.productsetInfo.name }} {{ item.name }}</el-option
              >
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布时间" prop="releaseTime">
            <el-date-picker
              v-model="versionForm.releaseTime"
              :picker-options="pickerOptions"
              format="yyyy-MM-dd HH:mm"
              type="datetime"
              placeholder="选择发布时间"
              style="width: 100%"
              @change="changeDate"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="isShowInheritanceVersion">
          <el-form-item prop="inheritanceVersionId">
            <template #label>
              <span>基于版本创建</span>
              <el-tooltip effect="dark" content="自动将基于的版本下的所有功能模块添加到版本下" placement="right">
                <i class="el-icon-info" style="margin-left: 5px"></i>
              </el-tooltip>
            </template>
            <el-select v-model="versionForm.inheritanceVersionId" placeholder="请选择">
              <el-option v-for="item in versionList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="描述">
            <el-input
              v-model="versionForm.description"
              show-word-limit
              maxlength="200"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> </el-row
    ></el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" size="small" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>

import { operationVersion } from '@/api/vone/product'
import dayjs from 'dayjs'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
    title: {
      type: String,
      default: ''
    },
    /**
     * 继承版本列表
     */
    versionList: {
      type: Array,
      default: []
    },
    /**
     * 是否显示基于版本创建
     */
    isShowInheritanceVersion: {
      type: Boolean,
      default: true
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (!this.versionForm.planRtime) {
        callback()
      } else if (value > this.versionForm.planRtime) {
        callback(new Error('开始日期不能大于结束日期!'))
      } else {
        callback()
      }
    }
    return {
      pickerOptions: {
        disabledDate: time => {
          if (this.versionForm.planStime) {
            return time.getTime() < new Date(this.versionForm.planStime).getTime() - 1 * 24 * 60 * 60 * 1000 + 1
          }
        }
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' }
        ],
        planStime: [{ validator: validatePass }],
        stateId: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      versionForm: {
        productsetVersionId: [],
        name: '',
        stateId: null,
        planStime: '',
        planRtime: '',
        releaseTime: '',
        description: '',
        // 继承版本id
        inheritanceVersionId: ''
      },
      orgData: [],
      productsetVersionList: []
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          // 编辑时数据回显
          if (Object.keys(this.rowData).length > 0) {
            this.versionForm = JSON.parse(JSON.stringify(this.rowData))
          } else {
            this.versionForm.stateId = 1
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    changeDate(v) {
      if (this.versionForm.planStime < v) {
        this.$refs['versionForm'].clearValidate('planStime')
      }
    },
    // 保存
    save() {
      this.$refs.versionForm.validate(valid => {
        if (valid) {
          const param = this.versionForm
          param.productId = this.$route.params.productId
          param.planStime = dayjs(param.planStime).format('YYYY-MM-DD HH:mm:ss')
          param.planRtime = dayjs(param.planRtime).format('YYYY-MM-DD HH:mm:ss')
          param.releaseTime = dayjs(param.releaseTime).format('YYYY-MM-DD HH:mm:ss')
          if (this.title == '编辑版本') {
            // 编辑保存
            operationVersion(param, 'put').then(res => {
              if (res.isSuccess) {
                this.$message.success('修改成功')
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          } else {
            // 新增保存
            operationVersion(param, 'post').then(res => {
              if (res.isSuccess) {
                this.$message.success('新增成功')
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        }
      })
    },
    close() {
      this.$refs.versionForm.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px !important;
}
</style>
