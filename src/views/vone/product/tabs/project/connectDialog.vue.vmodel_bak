<template>
  <div>
    <el-dialog
      :title="title"
      width="520px"
      v-model:visible="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <el-form-item label="项目名称" prop="projectId">
          <el-select v-model="formData.projectId" placeholder="请选择项目" multiple filterable clearable>
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :title="item.name"
              :label="`${item.code}  ${item.name}`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button
          :loading="saveLoading"
          type="primary"
          @click="sureAdd"
        >保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAssociableProjectInfo, setBatchSave } from '@/api/vone/product/index'

export default {
  components: {

  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    title: {
      type: String,
      default: undefined
    },
    projectType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      projectList: [],
      formData: {
        projectId: []
      },
      formRules: {
        projectId: [{ required: true, message: '请选择项目' }]
      },
      saveLoading: false
    }
  },

  mounted() {
    this.getProject()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.formData.resetFields()
    },
    async getProject() {
      const res = await getAssociableProjectInfo(
        this.$route.params.productId,
        { classify: this.projectType }
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.projectList = res.data
    },

    async sureAdd() {
      this.$set(this.formData, 'productsetId', this.$route.params.productId)

      this.saveLoading = true
      try {
        await this.$refs.formData.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }

      const params = this.formData.projectId.map(e => {
        return {
          isHost: false,
          productId: this.$route.params.productId,
          projectId: e
        }
      })
      const { isSuccess, msg } = await setBatchSave(params).catch(() => {
        this.saveLoading = false
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('保存成功')
      this.onClose()
      this.$emit('success')
    }

  }
}
</script>
