<template>
  <page-wrapper>
    <div class="header">
      <div class="header-l">
        <span class="m-r-16">版本3.0.11</span>
        <el-tag class="m-r-26">进行中</el-tag>
      </div>
    </div>
    <chart />
    <vone-echarts-card style="margin-top: 16px;" :height="'calc(100vh - 302px)'" :width="'100%'">
      <vone-echarts :options="options" />
    </vone-echarts-card>
    <release-dialog v-if="visible" v-model:visible="visible" @success="getsuccess" />
  </page-wrapper>
</template>
<script>
import Chart from '../../components/chart.vue'

export default {
  components: {
    Chart
  },
  data() {
    return {
      visible: false,
      options: {}
    }
  },

  mounted() {
    this.getCharts()
  },
  methods: {
    getCharts() {
      this.options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          }
        },
        title: {
          text: '迭代完成情况'
        },
        legend: {
          x: 'right',
          textStyle: {
            color: '#8A8F99'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'Direct',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [320, 302, 301, 334, 390, 330, 320]
          },
          {
            name: 'Mail Ad',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: 'Affiliate Ad',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'Video Ad',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [150, 212, 201, 154, 190, 330, 410]
          },
          {
            name: 'Search Engine',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [820, 832, 901, 934, 1290, 1330, 1320]
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.m-r{
  &-16{
    margin-right: 16px;
  }
  &-26{
    margin-right: 26px;
  }
}
.header {
  display: flex;
  justify-content: flex-end;
  margin: 0 -16px;
  padding: 0 16px 16px;
  border-bottom: 1px solid #ebeef5;
  .header-l {
    flex: 1;
    display: flex;
    align-items: center;
  }
  .header-r {
    flex: 1;
    display: flex;
  }
}
.el-card :deep(.el-card__header) {
  display: none;
}
</style>
