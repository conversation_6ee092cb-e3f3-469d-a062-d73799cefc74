<!-- 需求 -->
<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="workIssue-table"
          v-model:model="formData"
          v-model:extra="extraData"
          :table-ref="$refs['workIssue-table']"
          show-grouping
          :grouping-options="tableOptions.groupingOptions"
          :hide-columns="tableOptions.hideColumns"
          v-model:default-fileds="defaultFileds"
          show-basic
          :show-column-sort="false"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 184rem)">
      <vxe-table
        ref="workIssueTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column
          title="标题"
          field="name"
          min-width="480"
          fixed="left"
          class-name="name_col custom-title-style"
          show-overflow="ellipsis"
          tree-node
        >
          <template #default="{ row }">
            <span v-if="row.delay" style="position: absolute; left: 0">
              <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top">
                <i class="el-icon-warning-outline color-danger ml-2" />
              </el-tooltip>
            </span>
            <el-tooltip
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`
                  }"
                />
                <span class="custom-title-style-text">{{ row.code + ' ' + row.name }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' -4px',
                right: '-40px',
                display: copyRow && copyRow.id == row.id ? 'block' : ''
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="e => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown" class="custom-title-copy-dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" command="title">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" command="code">
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="100" sortable>
          <template #default="{ row, rowIndex }">
            <issueStatus
              v-if="row"
              :workitem="row"
              :no-permission="!$permission('reqm_center_require_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>

        <vxe-column title="负责人" field="leadingBy" min-width="150">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm-center-require-edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                class="remoteuser"
                :default-data="[row.echoMap.handleBy]"
                :disabled="!$permission('reqm-center-require-edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.putBy"
                class="remoteuser"
                :default-data="[row.echoMap.putBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'putBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="提出时间" width="120" show-overflow-tooltip sortable>
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="100" sortable>
          <template #default="{ row }">
            <vone-icon-select
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('reqm_center_require_priority')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px'
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column field="sourceCode" title="需求来源" show-overflow-tooltip width="100">
          <template #default="{ row }">
            <span v-if="row.sourceCode && row.sourceCode && row.echoMap.sourceCode">
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column show-overflow-tooltip field="projectId" title="归属项目" width="90">
          <template #default="{ row }">
            <span v-if="row.projectId && row.echoMap && row.echoMap.projectId">
              {{ row.echoMap.projectId.name }}
            </span>
            <span v-else>{{ row.projectId }}</span>
          </template>
        </vxe-column>

        <vxe-column title="功能模块" field="typeCode" min-width="100">
          <template v-slot="{ row }">
            {{ row.echoMap.typeCode ? row.echoMap.typeCode.name : row.typeCode }}
          </template>
        </vxe-column>
        <!-- <vxe-column field="putBy" title="提出人" width="120">
          <template slot-scope="scope">
            <vone-user-avatar
              :avatar-path="getputByInfo(scope.row) ? getputByInfo(scope.row).avatarPath : ''"
              :name="getputByInfo(scope.row) ? getputByInfo(scope.row).name : ''"
            />
          </template>
        </vxe-column> -->
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item, index) in row.tag" :key="index">
              <el-tag style="margin-right: 6px" type="success">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </vxe-column>

        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm-center-require-edit')"
                  icon="iconfont el-icon-application-edit"
                  @click="editIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm-center-require-del')"
                  icon="iconfont el-icon-application-delete"
                  @click="deleteIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more icon_click" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="item in moreOpear"
                    :key="item.name"
                    :command="() => item.handler(row)"
                    :disabled="item.disabled"
                  >
                    <i :class="item.icon" />
                    <span>{{ item.label }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
      <!-- 编辑 -->
      <vone-custom-edit
        v-if="issueParam.editvisible"
        :key="issueParam.key"
        v-model="issueParam.editvisible"
        v-bind="issueParam"
        :type-code="'ISSUE'"
        :left-tabs="leftTabs"
        :right-tabs="rightTabs"
        @success="getInitTableData"
      />

      <!-- 新增 -->
      <vone-custom-add
        v-if="issueParam.addvisible"
        :key="issueParam.key"
        v-model="issueParam.addvisible"
        v-bind="issueParam"
        :title="'新增需求'"
        :type-code="'ISSUE'"
        @success="getInitTableData"
      />

      <!-- 详情 -->
      <vone-custom-info
        v-if="issueInfoParam.editvisible"
        v-model="issueInfoParam.editvisible"
        v-bind="issueInfoParam"
        :type-code="'ISSUE'"
        :left-tabs="leftTabs"
        :right-tabs="rightTabs"
        :isShowCreateBtn="false"
        @success="getInitTableData"
      />

      <!-- 批量编辑 -->
      <editAll
        v-if="editAllParam.visible"
        v-bind="editAllParam"
        v-model="editAllParam.visible"
        :type-code="'ISSUE'"
        is-req
        @success="getInitTableData"
      />

      <!-- 导入 -->
      <vone-import-file
        v-if="importParam.visible"
        v-bind="importParam"
        v-model="importParam.visible"
        @success="getInitTableData"
      />

      <!-- 批量编辑 -->
      <editAll
        v-if="editAllParam.visible"
        v-bind="editAllParam"
        v-model="editAllParam.visible"
        :type-code="'ISSUE'"
        is-req
        @success="getInitTableData"
      />
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />
  </div>
</template>

<script>
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'

import { editById, getWorkItemState } from '@/api/vone/project/index'
import { apiAlmIssueInfo, apiAlmIssuePage } from '@/api/vone/project/issue'
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { requirementDel, findByClassify } from '@/api/vone/reqmcenter/require'
import editAll from '@/views/vone/project/common/edit-all'
import { download, catchErr } from '@/utils'
import { queryFieldList } from '@/api/common'
import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'
import { getModule } from '@/api/vone/product/index'
import setDataMixin from '@/mixin/set-data'
export default {
  components: {
    issueStatus,
    editAll
  },
  // props: {
  //   formData: {
  //     type: Object,
  //     default: () => {}
  //   }
  // },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [],
      pageLoading: false,
      formData: {},
      tableData: { records: [] },
      createSimple: false,
      prioritMap: {},
      moreOpear: [
        {
          type: 'icon', // 为icon则是图标
          label: '复制标题', // 功能名称
          icon: 'iconfont el-icon-application-copy', // icon class
          handler: this.copy // 操作事件
        },
        {
          disabled: !this.$permission('product_work_item_graph'),
          type: 'icon', // 为icon则是图标
          label: '拓扑', // 功能名称
          icon: 'iconfont el-icon-application-topology', // icon class
          handler: this.gotoTuoPu // 操作事件
        }
      ],
      issueInfoParam: {
        // 详情
        visible: false
      },
      tableList: [], // 用于编辑时切换上一个下一个
      issueParam: {
        visible: false,
        demoDiolog: false
      },
      tableOptions: {
        isOperation: true,
        isSelection: true,
        hideColumns: [
          'files',
          'code',
          'description',
          'delay',
          'estimatePoint',
          'planStime',
          'projectId',
          'ideaId',
          'sourceCode',
          'rateProgress',
          'typeCode',
          'putBy',
          'leadingBy'
        ] // 默认隐藏列
      },
      actions: [
        {
          name: '批量删除',

          fn: this.deleteTableSelect,
          disabled: !this.$permission('reqm-center-require-del')
        },
        {
          name: '批量编辑',

          fn: this.editAll,
          disabled: !this.$permission('reqm-center-require-edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('reqm_center_issue_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('reqm_center_issue_export')
        }
      ],
      importParam: { visible: false }, // 用户导入
      exportLoading: false,
      editAllParam: { visible: false }, // 批量编辑
      prioritList: [],
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [
        {
          label: '需求',
          name: 'IssueToIssue'
        },
        {
          label: '测试用例',
          name: 'TestCase'
        },
        {
          label: '关联代码',
          name: 'DevelopTab'
        }
      ],
      copyRow: null
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
    gethandleByInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    },
    getputByInfo() {
      return function (row) {
        return row?.putBy && row?.echoMap?.putBy
      }
    }
  },
  mounted() {
    this.getQueryFieldList()
    // this.getInitTableData()
    // this.getPrioritList()
  },
  methods: {
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
      res.data.map(item => {
        this.prioritMap[item.code] = {
          color: item.color,
          icon: item.icon,
          name: item.name
        }
      })
    },
    async getQueryFieldList(typeCodes) {
      const fixedField = ['name', 'handleBy', 'stateCode', 'tagId', 'createTime', 'typeCode']
      const form = {
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE',
        typeCodes: typeCodes
      }
      const res = await queryFieldList(form)
      if (!res.isSuccess) {
        return
      }
      const vId = ['productId', 'planId']
      const filter = res.data.filter(r => r.isSearch && r.key != 'projectId')
      filter.forEach(element => {
        if (element.key == 'productModuleFunctionId') {
          element.type.code = 'TREE'
        }
        element.isBasicFilter = !fixedField.includes(element.key)
        element.multiple = element.type.code != 'ICON'

        element.valueType = vId.includes(element.key) ? 'id' : null
      })
      this.defaultFileds = filter
      this.getOptions()
    },
    getOptions() {
      this.getPrioritList()
      this.getIssueType()
      this.getStateList()
      this.getDaley()
      this.getsourceCode()
      this.productList()
      this.getplanId()
    },
    getDaley() {
      const delay = [
        { name: '是', code: true, id: '1' },
        { name: '否', code: false, id: '2' }
      ]
      this.setData(this.defaultFileds, 'delay', delay)
    },
    // // 查询需求类型
    // async getIssueType() {
    //   const res = await apiAlmGetTypeNoPage(
    //     this.$route.params.productId,
    //     "ISSUE"
    //   );
    //   if (!res.isSuccess) {
    //     return;
    //   }
    //   // this.typeCodeList = res.data

    // },
    // 迭代计划
    async getplanId() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.productId || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'planId', res.data)
    },
    // 查询来源
    async getsourceCode() {
      // if (this.maps['sourceCode'].length > 0) return
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'sourceCode', res.data)
    },
    async productList() {
      // if (this.maps['productId']?.length > 0) return
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productId', res.data)
    },
    // 产品模块功能
    async getModuleList(value) {
      const isArray = typeof value == 'object' && Array.isArray(value)
      // 查询产品功能模块
      const res = await getModule({
        model: {
          productId: isArray ? value.join(',') : value
        }
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productModuleFunctionId', gainTreeList(res.data))
    },
    onTypeChange(item) {
      // this.getQueryFieldList(item)
      if (item.key == 'productId') {
        if (item.value?.length != 0) {
          this.getModuleList(item.value)
        }
      }
    },

    async getInitTableData() {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      // if (this.formData?.productModuleFunctionId && typeof this.formData?.productModuleFunctionId == 'string') {
      //   this.formData.productModuleFunctionId = [this.formData?.productModuleFunctionId]
      // }
      this.$set(this.formData, 'productId', [this.$route.params.productId])
      // this.$set(this.formData, 'typeCode', ['ISSUE'])
      const params = {
        ...tableAttr,
        // ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      if (this.formData.createTime && this.formData.createTime.length > 0) {
        params.model.createTime = {
          start: this.formData.createTime[0],
          end: this.formData.createTime[1]
        }
      }
      // let params = {}
      // const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }

      // this.$set(this.formData, 'productId', [this.$route.params.productId])
      // this.$set(this.formData, 'typeCode', ['ISSUE'])
      // params = {
      //   ... pageObj,
      //   extra: {},
      //   model: { ...this.formData }
      // }
      const res = await apiAlmIssuePage(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      res.data.records.forEach(element => {
        element.tag = element.echoMap && element.echoMap.tags ? element.echoMap.tags.map(r => r.name) : []
      })

      this.tableData = res.data || {}
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('requirement', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    showPopovers() {
      this.showTag = true
      this.getIssueType() // 需求分类
      this.getStateList() // 状态
    },
    changeView(val) {
      this.showView = val
      // if (val == 'table') {
      //   this.view = '表格视图'
      //   await this.getInitTableData()
      // }
    },
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIssueInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(res.data, 'tag', res.data.echoMap.tagId ? res.data.echoMap.tagId.map(r => r.name) : [])
      }
      this.tableData.records.splice(index, 1, res.data)
    },

    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '需求',
        url: '/api/alm/alm/requirement/excel/downloadImportTemplate',
        importUrl: '/api/alm/alm/requirement/excel/import'
      }
    },

    // 导出
    async exportFlie() {
      try {
        this.exportLoading = true
        download(`需求信息.xls`, await apiBaseFileLoad('/api/alm/alm/requirement/excel/export', this.formData))
        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
        return
      }
    },

    // 查询需求分类
    async getIssueType() {
      const res = await findByClassify('ISSUE')
      if (!res.isSuccess) {
        return
      }

      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },

    // 查状态
    async getStateList() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },

    // 批量删除
    async deleteTableSelect() {
      this.selectData = this.getVxeTableSelectData('reqm-req-table')
      if (this.selectData.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        }).then(async () => {
          const deleteArr = []
          this.selectData.map(item => deleteArr.push(item.id))
          const res = await requirementDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },

    // 复制标题到剪贴板
    copy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(
        function (e) {
          _this.$message.success('复制成功')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },

    gotoTuoPu(row) {
      // this.$router.push({
      //   path: '/reqmcenter/require/requireList/graph/' + row.id,
      //   query: {
      //     code: row.code
      //   }
      // })
      const { productCode, productId } = this.$route.params
      this.$router.push({
        path: `/product/tabs/workItem/graph/${productCode}/${productId}/${row.id}`,
        query: {
          code: row.code
        }
      })
    },
    getTracking(row) {
      this.$router.push({
        path: '/reqmcenter/require/requireList/trackingView'
      })
    },
    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新增需求',
        key: Date.now(),
        infoDisabled: false
      }
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑需求',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
    },
    showInfo(row) {
      this.issueInfoParam = {
        key: Date.now(),
        editvisible: true,
        title: '需求详情',
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
      this.projectId = row.projectId
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      })

      const { isSuccess, msg } = await requirementDel(row.length ? row : [row.id])
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量编辑
    editAll() {
      this.selectData = this.getVxeTableSelectData('reqm-req-table')
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    }
  }
}
</script>
<style lang="scss" scoped>
.tag {
  padding: 2px 6px;
  border-radius: 2px;
  background: #f0f0f0;
}
:deep(.vone-vxe-table .vxe-tree-cell a .iconfont) {
  display: inline-block;
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  margin-left: 5px;
  display: inline-block;
  max-width: 90%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 5px;
}

.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
</style>
<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
