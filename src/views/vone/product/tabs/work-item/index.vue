<!--  -->
<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <el-tabs v-model="active" class="vone-tabs" style="margin-right:12px">
          <el-tab-pane v-for="item in types" :key="item.type" :name="item.type" :label="item.name" />
        </el-tabs>

        <!-- <vone-search-dynamic
          table-search-key="workIssueTable"
          :model="formData"
          :table-ref="$refs['workIssueTable']"
          hide-filter-form
          @getTableData="getInitTableData"
        /> -->
        <!-- <el-input v-model="formData.name" placeholder="请输入名称" style="width: 260px;" @change="getInitTableData" /> -->
      </template>

    </vone-search-wrapper>

    <component
      :is="active"
      ref="component"
      :active-tab="active"
      :form-data="formData"
    />
  </page-wrapper>
</template>

<script>
import UserIssueTable from './user-issue-table.vue'
import ProductIssueTable from './product-issue-table.vue'
import DefectTable from './defect-table.vue'
import simpleAddIssue from '@/views/vone/reqm-center/require/function/simple-add-issue.vue'
export default {
  components: {
    UserIssueTable,
    ProductIssueTable,
    DefectTable,
    simpleAddIssue
  },
  data() {
    return {
      formData: {},
      createSimple: false,
      active: 'ProductIssueTable',
      types: [
        // {
        //   type: 'UserIssueTable',
        //   name: '用户需求'
        // },
        {
          type: 'ProductIssueTable',
          name: '需求'
        },

        {
          type: 'DefectTable',
          name: '缺陷'
        }

      ],
      actions: [
        {
          name: '批量删除',

          fn: this.deleteTableSelect,
          disabled: !this.$permission('reqm-center-require-del')
        },
        {
          name: '批量编辑',

          fn: this.editAll,
          disabled: !this.$permission('reqm-center-require-edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('reqm_center_issue_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('reqm_center_issue_export')
        }

      ]
    }
  },
  mounted() {

  },
  methods: {
    getInitTableData() {
      this.$refs.component.getInitTableData(this.formData)
    }

  }
}
</script>
<style lang='scss' scoped>

</style>
