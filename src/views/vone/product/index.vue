<template>
  <page-wrapper>

    <!-- 卡片视图 -->
    <div style="height: calc(100vh - 30px)">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="product-card"
            :model="formData"
            v-model:default-fileds="defaultFileds"
            show-basic
            v-model:extra="extraData"
            @getTableData="getProductList"
          />
        </template>
        <template slot="actions">
          <!-- <el-button @click="jumpToBoard">看板</el-button> -->
          <el-button type="primary" :disabled="!$permission('product_newAdd')" icon="iconfont el-icon-tips-plus-circle" @click="addProduct">新增</el-button>

        </template>
        <template slot="fliter">
          <vone-search-filter
            v-model:extra="extraData"
            :model="formData"
            v-model:default-fileds="defaultFileds"
            @getTableData="getProductList"
          />
        </template>

      </vone-search-wrapper>
      <vone-cards ref="product-card" v-loading="loading" :data="tableData" :row-count="4" :height="cardHeight" @updateData="getProductList">

        <template slot-scope="{ row }">
          <div :class="[ !$permission('product_count') ?'disabledLinkCls':'linkCls']" @click="editById(row)">
            <vone-card :actions-num="3" :actions="rowActions">
              <template slot="title">
                <el-row type="flex" justify="space-between">
                  <div class="title-box">
                    <div class="title" :title="row.name">{{ row.name }}</div>
                    <!-- <span>{{ row.key }}</span> -->
                    <div v-if="productSetsName(row)" :title="productSetsName(row)" class="productname">
                      {{ productSetsName(row) }}
                    </div>
                  </div>
                </el-row>
              </template>
              <template v-slot:icon>
                <img v-if="row.icon" slot="icon" :src="row.icon" class="iconImg">
                <img v-else slot="icon" src="@/assets/iconsteps/icon-product.png" class="iconImg">
              </template>

              <div slot="desc">
                <span>
                  <vone-user-avatar
                    :avatar-path="getUserInfo(row) ? getUserInfo(row).avatarPath : ''"
                    :name="getUserInfo(row)
                      ? getUserInfo(row).name :''"
                  />
                </span>

              </div>

              <el-row style="font-size: 12px;color: #909399;">
                <vone-toolitip :content="row.code" :label="'标识 '" />

                <vone-toolitip :content="row.description" :multi-line="true" :label="'描述 '" />
              </el-row>

            </vone-card>

          </div>
        </template>
      </vone-cards>
    </div>

    <edit-dialog v-if="projectParam.visible" v-bind="projectParam" v-model="projectParam.visible" @success="getProductList" />
    <!-- 权限分配抽屉 -->
    <division v-if="divisionDrawerParam.visible" v-bind="divisionDrawerParam" v-model="divisionDrawerParam.visible" @success="getProductList" />
  </page-wrapper>
</template>
<script>
import { apiProductData, operationProduct } from '@/api/vone/product'
import EditDialog from './edit-dialog.vue'
import division from './division.vue'
import setDataMixin from '@/mixin/set-data'
export default {
  components: {
    EditDialog,
    division
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '产品名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入产品名称'
        },
        {
          key: 'leadingBy',
          name: '负责人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择负责人'
          // multiple: false
        },
        {
          key: 'productSetId',
          name: '产品集',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择产品集',
          valueType: 'id'
        }
      ],
      formData: {},
      divisionDrawerParam: { visible: false },
      loading: false,
      projectParam: {
        visible: false
      },
      tableData: {},
      rowActions: [
        {
          disabled: !this.$permission('product_update'),
          text: '编辑基本信息',
          type: 'text',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editBaseInfo(row)
        },
        {
          disabled: !this.$permission('product_power'),
          text: '权限分配',
          type: 'text',
          icon: 'iconfont el-icon-application-user-permission',
          onClick: ({ row }) => this.resDivision(row)
        },
        {
          disabled: !this.$permission('product_del_list'),
          text: '删除',
          type: 'text',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.deleteById(row)
        }
      ]
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
    productSetsName() {
      return function(row) {
        const nameArr = row?.echoMap?.productSets?.map(item => item.name)
        return nameArr?.join('、') || ''
      }
    },
    cardHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(100vh - 140px - ${height})`
    }
  },
  mounted() {
  },
  methods: {
    // 权限分配
    resDivision(row) {
      this.divisionDrawerParam = { visible: true, id: row.id }
    },
    // 编辑基本信息
    async editBaseInfo(row) {
      this.projectParam = {
        visible: true,
        title: '编辑产品',
        id: row.id
      }
    },
    async getProductList() {
      this.loading = true
      let params = {}
      const tableAttr = this.$refs['product-card'].exportTableQueryData()
      params = {
        ...tableAttr,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await apiProductData(
        params
      )
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },
    jumpToBoard() {
      this.$router.push('/product/dataBoard')
    },
    // 新增
    addProduct() {
      this.projectParam = { visible: true, title: '新增产品' }
    },
    // 修改
    editById(row) {
      if (!this.$permission('product_count')) return
      this.$router.push({
        name: 'product_count',
        params: {
          productCode: row.code,
          productId: row.id
        }
      })
    },
    // 删除
    async deleteById(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      })
        .then(async(actions) => {
          const res = await operationProduct(
            [row.id], 'delete'
          )
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.getProductList()
          this.$message.success('删除成功')
        })
        .catch(() => { })
    }

  }
}
</script>

<style lang="scss" scoped>
.cardText {
  padding-top: 5px;
  color: #909399;
  font-size: 12px;
}
.iconImg {
  width: 32px;
  height: 32px;
}
.userText {
  height: 35px;
  line-height: 35px;
}
.cards-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.linkCls {
  cursor: pointer;
}
.disabledLinkCls {
  cursor: not-allowed;
}
.title-box {
  display: flex;
  align-items: center;
  .title {
    flex: 1;
  }
  .productname {
    background: var(--main-hover-page-color);
    padding: 0 6px;
    border-radius: 2px;
    color: var(--main-theme-color);
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    max-width: 84px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
