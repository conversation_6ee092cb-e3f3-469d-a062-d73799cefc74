<template>

  <vone-echarts-card :title="'缺陷趋势'" :width="'100%'" header-box>
    <span slot="headerBox" class="filterBox">
      <el-date-picker
        v-model="data.times"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        @change="getClone"
      />
    </span>
    <vone-echarts :options="options" @chartClick="chartClick" />
    <div class="statisticsNum">

      <header>最近{{ days }}天的问题  </header>
      <div>
        <a @click="chartClick(trendsData,'created')">
          <span class="c2" />新建的缺陷 <span class="t2">{{ trendsData.alltodo }}</span>
        </a>
        <a @click="chartClick(trendsData,'done')">
          <span class="c1" />修复的缺陷<span class="t1">{{ trendsData.alldone }}</span>
        </a>
        <a @click="chartClick(trendsData,'doing')">
          <span class="c3" />当前的缺陷<span class="t1">{{ trendsData.alldoing }}</span>
        </a>

      </div>

    </div>
    <!-- <vone-empty v-if="!trendsData" /> -->

    <TrendsDetailDialog v-if="detailParams.visible" v-bind="detailParams" v-model:visible="detailParams.visible" />

  </vone-echarts-card>

  <!-- 缺陷趋势 -->

</template>

<script>

import dayjs from 'dayjs'
import { queryViewByTime } from '@/api/vone/product/index'
import TrendsDetailDialog from '@/views/vone/project/overview/components/trends-dialog.vue'
import _ from 'lodash'

export default {
  components: {
    TrendsDetailDialog
  },
  props: {
    keys: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      detailParams: { visible: false },
      data: {
        times: [],
        productId: this.$route.params.productId
      },
      trendsData: {
        days: ''
      },
      options: {},
      days: null
    }
  },
  watch: {

  },
  mounted() {
    const day = dayjs().format('YYYY-MM-DD')
    const monthAgo = dayjs().add(-30, 'day').startOf('day').format('YYYY-MM-DD')

    this.data.times = [monthAgo, day]

    this.getProjectOptions()
  },
  methods: {

    async getClone() {
      this.days = null
      this.trendsData = {}

      await this.getProjectOptions()
    },
    async getProjectOptions() {
      this.days = dayjs(this.data.times[1]).diff(this.data.times[0], 'day')
      const { data, isSuccess, msg } = await queryViewByTime({
        productId: this.$route.params.productId,
        scopeTime: {
          start: this.data.times ? this.data.times[0] : ' ',
          end: this.data.times ? this.data?.times[1] : ' '
        }

      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.trendsData = data

      this.trendsData.alltodo = _.sum(data?.add)
      this.trendsData.alldone = _.sum(data?.close)
      this.trendsData.alldoing = data?.residue[data.residue.length - 1]

      this.options = {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          }
        },
        grid: [{
          left: '16px',
          right: '70px',
          bottom: '55%',
          containLabel: true
        },
        {
          top: '50%',
          left: '16px',
          right: '70px',
          bottom: '25%',
          containLabel: true
        }
        ],
        xAxis: [
          {
            data: this.trendsData.date,
            boundaryGap: 0,
            axisLabel: {
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              },
              rotate: -18 // 设置日期显示样式（倾斜度）

            }
          },
          {
            data: this.trendsData.date,
            gridIndex: 1,
            splitLine: {
              show: false // 去掉网格线
            },
            boundaryGap: 0,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              },
              rotate: -18 // 设置日期显示样式（倾斜度）
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            'axisTick': { // y轴刻度线
              'show': false
            },
            splitNumber: 1,
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          },
          {
            splitNumber: 1,
            type: 'value',
            'axisTick': { // y轴刻度线
              'show': false
            },
            gridIndex: 1,
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          }
        ],
        series: [
          {
            showSymbol: false,
            name: '新增的缺陷',
            type: 'line',
            data: this.trendsData.add,
            itemStyle: {
				              normal: {
				                color: '#58CC94', // 改变折线点的颜色
				                lineStyle: {
				                  color: '#58CC94' // 改变折线颜色
				                }
				              }
            }
            // markLine: {
            //   symbol: ['none', 'none'],
            //   data: version,
            //   lineStyle: {
            //     normal: {
            //       color: '#B2B6BF',
            //       width: 1
            //       // 这儿设置安全基线颜色
            //     }
            //   },
            //   label: {
            //     show: true,
            //     position: 'end',
            //     formatter(params) {
            //       return params.data.name + '：' + params.data.version
            //     },
            //     color: '#fff',
            //     height: 18,
            //     padding: [4, 4, 4, 4],
            //     borderWidth: 2,
            //     backgroundColor: 'rgba(50, 50, 50, 0.65)',
            //     borderRadius: 2,
            //     fontWeight: 400,
            //     fontSize: 14,
            //     fontFamily: 'PingFang SC'
            //   }
            // },
            // silent: true // 鼠标悬停事件, true悬停不会出现实线
            // symbol: 'none' // 去掉箭头

          },
          {
            showSymbol: false,
            name: '修复的缺陷',
            type: 'line',
            data: this.trendsData.close,
            itemStyle: {
				              normal: {
				                color: '#FA6E69', // 改变折线点的颜色
				                lineStyle: {
				                  color: '#FA6E69' // 改变折线颜色
				                }
				              }
            }
          },
          {
            showSymbol: false,
            xAxisIndex: 1,
            yAxisIndex: 1,
            name: '当前的缺陷',
            type: 'line',
            data: this.trendsData.residue,
            itemStyle: {
              normal: {
                color: '#5792FF', // 改变折线点的颜色
                ineStyle: {
                  color: '#5792FF' // 改变折线颜色
                }
              }
            }
          }

        ]
      }
    },
    chartClick(params, val) {
      console.log(params, val, 'params, val')
      const types = [
        {
          name: '新建的缺陷',
          code: 'created'
        },
        {
          name: '当前的缺陷',
          code: 'doing'
        },
        {
          name: '修复的缺陷',
          code: 'done'
        }
      ]
      const dates = this.trendsData.date
      this.$set(params, 'start', dates[0])
      this.$set(params, 'end', dates[dates.length - 1])
      this.$set(params, 'classify', 'BUG')
      this.$set(params, 'activeTab', val)
      // this.$set(params, 'name', val)
      this.detailParams = { visible: true, form: params, types: types }
    }
  }
}
</script>

<style lang="scss" scoped>

.filterBox {
  float: right;
  padding-right: 10px;
  // position: absolute;
  // top: 9px;
  // right: 16px;
  :deep(.el-cascader__tags) {
    flex-wrap: nowrap;
  }
  :deep(.el-tag--info) {
    max-width: 85px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .filterSelect {
    width: 240px;
    :deep() {
      .el-input__inner {
        background: var(--hover-bg-color);
        border-color: var(--hover-bg-color);
      }
      .el-select__tags {
        width: 160px;
        .el-tag {
          max-width: 150px;
        }
      }
    }
  }
}
.statisticsNum{
	position: absolute;
	top:80%;
	left: 16px;
  right:16px;
	bottom:10px;
	background: #F7F8FA;
	border-radius: 4px;
	padding:16px;
	header{
		font-weight: 500;
		line-height: 22px;
    font-size: 14px;
    color:#1D2129;
	}
	div{
		font-size: 14px;
    line-height: 22px;
    margin-top:12px;
    display: flex;
    justify-content: space-between;
  .c1{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #FA6E69;
    margin-right:12px;
	}
	.c2{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #58CC94;
    margin-right:12px;
	}
	.c3{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #5792FF;
    margin-right:12px;
	}
	.t1{
    margin-left:4px;
    color:#6B7385;
    font-weight: 500;
	}
	}
}
</style>
