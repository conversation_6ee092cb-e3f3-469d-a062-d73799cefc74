<template>
  <div class="drawerBox">
    <vone-drawer title="新增意向" v-model="visible" :before-close="onClose" :modal="false" size="65%" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="intentionForm" :model="intentionForm" label-position="right" label-width="100px" :rules="rules">
        <el-row class="basicForm">
          <el-col :span="24">
            <el-form-item label="意向名称：" prop="name" style="margin: 16px 16px 10px 10px">
              <el-input v-model="intentionForm.name" placeholder="请输入意向名称" :disabled="infoDisabled" style="width: 85%" />
            </el-form-item>
            <el-tabs v-model="tabActive" @tab-click="handleClick">
              <el-tab-pane label="基本信息" name="basic">

                <el-row>
                  <el-col :span="12">
                    <el-form-item label="意向分类" prop="typeCode">
                      <el-select v-model="intentionForm.typeCode" style="width:100%" clearable>
                        <el-option v-for="item in typeCodeList" :key="item.key" :label="item.name" :value="item.code" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="意向来源" prop="sourceCode">
                      <el-select v-model="intentionForm.sourceCode" style="width:100%" clearable>
                        <el-option v-for="item in sourceList" :key="item.key" :label="item.name" :value="item.code" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="优先级" prop="priorityCode">
                      <vone-icon-select v-model="intentionForm.priorityCode" :data="prioritList" filterable style="width:100%" clearable>
                        <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                          <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                          {{ item.name }}
                        </el-option>
                      </vone-icon-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="归属产品" prop="productId">
                      <el-select v-model="intentionForm.productId" clearable filterable style="width:100%">
                        <el-option v-for="item in productIdList" :key="item.key" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="提出人" prop="putBy">
                      <vone-remote-user v-model="intentionForm.putBy" />

                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="期望完成" prop="expectedTime">
                      <el-date-picker v-model="intentionForm.expectedTime" prefix-icon="iconfont el-icon-application-run-history" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" default-time="18:00:00" placeholder="选择期望完成时间" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="意向描述：" prop="description">
                  <el-input
                    v-model="intentionForm.description"
                    type="textarea"
                    style="95%"
                    :rows="3"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>

                <!-- <intentionForm ref="intentionForm" :space-info="spaceInfo" :info-disabled="infoDisabled" @save="saveInfo" /> -->
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="footer">

        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
        <el-button @click="onClose">取消</el-button>
      </div>
    </vone-drawer>
  </div>
</template>
<script>

import { apiAlmIdeaAddOrEdit } from '@/api/vone/reqmcenter/idea'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import { getAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { getUser } from '@/utils/auth'

// import intentionForm from './intentionForm.vue'
export default {
  components: {
    // intentionForm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    spaceInfo: {
      type: Object,
      default: () => { }
    }
  },

  data() {
    return {
      intentionForm: {
        name: this.title
      },
      rules: {
        name: [{ required: true, message: '请输入意向名称' }],
        typeCode: [{ required: true, message: '请选择意向分类' }],
        sourceCode: [{ required: true, message: '请选择意向来源' }],
        priorityCode: [{ required: true, message: '请选择优先级' }]
      },
      tabActive: 'basic',
      saveLoading: false,
      // intentionForm: {},
      sourceList: [], // 意向来源
      productIdList: [], // 归属产品
      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [] // 意向分类
    }
  },

  mounted() {
    this.getIssueType() // 意向分类
    this.productList() // 归属产品
    this.getStateList() // 状态
    this.getPrioritList() // 优先级
    this.getSourceList() // 来源
    this.$set(this.intentionForm, 'putBy', getUser().id)
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    handleClick(tab) {
    },
    // 保存
    async saveInfo() {
      await this.$refs.intentionForm.validate()

      try {
        this.saveLoading = true
        const projectDocuments = {
          documentId: this.$route.params.docId,
          spaceId: this.$route.params.spaceId
        }
        const params = {
          ...this.intentionForm,
          ...this.$refs.intentionForm.intentionForm,
          projectDocuments: [{ ...projectDocuments }]
        }
        const res = await apiAlmIdeaAddOrEdit(params)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success('创建成功')
        this.$confirm('是否进入意向配置页面?', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'success'
        }).then(() => {
          this.$router.push({
            path: '/reqmcenter/idea'
          })
        }).catch(() => {
          this.onClose()
        })
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 查询意向分类
    async getIssueType() {
      const res = await getAlmGetTypeNoPage(
        'IDEA'
      )
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.$set(this.intentionForm, 'typeCode', res.data.length ? res.data[0].code : '')
    },
    // 查询意向来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'IDEA'
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.productIdList = res.data
      if (this.spaceInfo.typeId == '3') {
        this.$set(this.intentionForm, 'productId', this.spaceInfo.bizId)
      }
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.$set(this.intentionForm, 'priorityCode', res.data[0].code)
    }

  }
}
</script>
<style lang="scss" scoped>
:deep(.el-date-editor.el-input),
.el-date-editor.el-input__inner {
  width: 100%;
}
:deep(.el-form-item) {
  height: 40px;
}

:deep(.el-form-item__label) {
  text-align: right;

}
.drawerBox {
  .basicForm {
    overflow-y: auto;
    // height: calc(100vh - 50px);
    padding-bottom: 50px;
    :deep(.el-tabs__content) {
      padding: 6px 15px 6px 10px;
      // overflow: auto;
       height: calc(100vh - 300px);
    }
    :deep(.el-tabs__nav-scroll) {
      padding-left: 28px;
    }
  }
}
</style>
