<template>
  <page-wrapper>
    <header class="header">
      <span>工时统计</span>
      <div class="header-right">
        <el-date-picker
          v-model="date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="dateChange"
        />
      </div>
    </header>
    <main class="main">
      <el-row class="main-row" :gutter="16">
        <el-col :span="16">
          <vone-echarts-card title="项目工时分布" height="100%">
            <vone-echarts v-show="isShow.projectOption" :options="projectOption" :height="'300px'" />
            <vone-empty v-if="!isShow.projectOption" />
          </vone-echarts-card>
        </el-col>
        <el-col :span="8">
          <vone-echarts-card title="工作项类型分布" height="100%">
            <vone-echarts v-show="isShow.progressoption" :options="progressoption" :height="'300px'" />
            <vone-empty v-if="!isShow.progressoption" />
          </vone-echarts-card>
        </el-col>
      </el-row>
      <el-row class="main-row">
        <el-col :span="24">
          <vone-echarts-card title="项目成员填报情况" height="100%" header-box>
            <el-select slot="headerBox" v-model="projectId" class="select-style" filterable placeholder="请选择" @change="projectChange">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <vone-echarts v-show="isShow.memberOptio" :options="memberOptio" :height="'300px'" />
            <vone-empty v-if="!isShow.memberOptio" />
          </vone-echarts-card>
        </el-col>
      </el-row>
    </main>
  </page-wrapper>
</template>

<script>
import { getStatisticsOfFilledBy, getStatisticsOfProject, getStatisticsOfType, getGroup } from '@/api/vone/manhour/index'
import dayjs from 'dayjs'

export default {
  components: {
  },
  data() {
    return {
      value: 'month',
      data: ['1'],
      date: [dayjs().add(-30, 'day').startOf('day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      projectOption: null,
      progressoption: null,
      memberOptio: null,
      isShow: {
        projectOption: false,
        progressoption: false,
        memberOptio: false
      },
      dateForm: {
        startTime: dayjs().add(-30, 'day').startOf('day').format('YYYY-MM-DD'),
        endTime: dayjs().format('YYYY-MM-DD')
      },
      options: [],
      projectId: ''
    }
  },
  mounted() {
    this.getStatisticsOfProjectFn()
    this.getStatisticsOfFilledByFn()
    this.getStatisticsOfTypeFn()
    this.getGroupWorkingHoursInfoFn()
  },
  methods: {
    projectChange() {
      this.dateForm.projectId = this.projectId
      this.getStatisticsOfFilledByFn()
    },
    async getGroupWorkingHoursInfoFn() {
      const form = {
        current: 1,
        extra: {},
        model: { groupBy: 'project' },
        order: 'descending',
        size: 999,
        sort: 'bizId'
      }
      const res = await getGroup(form)
      if (res.data.records.length > 0) {
        res.data.records = res.data.records.filter(e => {
          if (e.bizId && e.data) {
            e.id = e.data.id
            e.name = e.data.name
          }
          return e.bizId && e.data
        })
        this.options = res.data.records
        this.projectId = this.options[0]?.id
      }
      this.getStatisticsOfFilledByFn()
    },
    dateChange() {
      this.dateForm.startTime = this.date[0]
      this.dateForm.endTime = this.date[1]
      this.getStatisticsOfFilledByFn()
      this.getStatisticsOfProjectFn()
      this.getStatisticsOfTypeFn()
    },
    // 项目成员填报情况
    async getStatisticsOfFilledByFn() {
      this.dateForm.projectId = this.projectId
      const res = await getStatisticsOfFilledBy(this.dateForm)
      this.isShow.memberOptio = res.data.name.length > 0
      this.memberOptio = {
        color: ['#5792FF'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '0',
          right: '0',
          top: '16px',
          bottom: '0',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#EAECF0'
            }
          },
          axisLabel: {
            show: true,
            rotate: 30,
            formatter: function(value) {
              var res = value
              if (res.length > 5) {
                res = res.substring(0, 4) + '...'
              }
              return res
            },
            color: '#B2B6BF'
          },
          data: res.data?.name
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dotted',
              color: '#EAECF0'
            }
          }
        },
        series: [
          {
            data: res.data?.data,
            type: 'bar'
          }
        ]
      }
    },
    // 项目工时分布
    async getStatisticsOfProjectFn() {
      const res = await getStatisticsOfProject(this.dateForm)
      res.data.data.forEach(e => {
        e.type = 'bar'
        e.stack = 'Ad'
      })
      this.isShow.projectOption = res.data.name.length > 0
      this.projectOption = {
        color: ['#5D7092', '#58CC94', '#FFA67D'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          left: 'center',
          bottom: 0,
          textStyle: {
            color: '#838A99',
            fontSize: 12
          }
        },
        grid: {
          left: '0',
          right: '0',
          top: '16px',
          bottom: '12%',
          containLabel: true
        },
        xAxis: [
          {
            axisLine: {
              lineStyle: {
                color: '#EAECF0'
              }
            },
            axisLabel: {
              show: true,
              // interval: 0,
              rotate: 30,
              overflow: 'truncate',
              // align: 'left',
              color: '#B2B6BF',
              formatter: function(value) {
                var res = value
                if (res.length > 5) {
                  res = res.substring(0, 4) + '...'
                }
                return res
              }
            },
            type: 'category',
            data: res.data.name
          }
        ],
        yAxis: [
          {
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dotted',
                color: '#EAECF0'
              }
            },
            type: 'value'
          }
        ],
        series: res.data.data
      }
    },
    // 类型分布
    async getStatisticsOfTypeFn() {
      const res = await getStatisticsOfType(this.dateForm)
      res.data?.forEach(e => {
        e.name = e.key
      })
      this.isShow.progressoption = res.data.length > 0
      this.progressoption = {
        color: ['#FF9B94', '#9063FF', '#5792FF'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          left: 'center',
          bottom: 0,
          textStyle: {
            color: '#838A99',
            fontSize: 12
          }
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: res.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{time|{c}}',
              rich: {
                time: {
                  fontSize: 12,
                  color: '#999'
                }
              }
            }
          }
        ]
      }
    }
  }
}
</script>
<style lang='scss' scoped>
@use "@/styles/variables.scss";

.header{
  height: 56px;
  margin: -16px -16px 0;
  padding: 0 16px;
  line-height: 56px;
  color: var(--main-font-color);
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #F2F3F5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .header-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-right__select {
      width: 64px;
      margin-left: 8px;
    }
  }
}
.main {
  padding: 16px 0 10px 0;
  height: calc(100vh - #{$hasHeader} - 68px);
  .main-row {
    height: calc(50% - 8px);
    margin-bottom: 16px;
    :deep(.el-col) {
      height: 100%;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.form-item--width {
  width: 200px;
}
:deep(.headerBox) {
  background-color: transparent;
}
.select-style {
  float: right;
  width: 220px;
  padding-right: 16px;
  :deep(.el-input__inner) {
    padding-right: 30px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
