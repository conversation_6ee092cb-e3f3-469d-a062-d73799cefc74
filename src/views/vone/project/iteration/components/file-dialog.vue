<template>
  <div>
    <el-dialog
      title="配置归档"
      width="520px"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-form
        ref="iterationForm"
        :model="iterationForm"
      >

        <el-form-item label="是否开启自动归档" prop="state">
          <el-radio-group v-model="iterationForm.state" @change="stateChange">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="iterationForm.state=='1'" class="dayConf" prop="day" label=" " :rules="iterationForm.state=='1'?iterationRules.day:[{required:false}]">
          <span class="conffile">迭代完成后
            <el-select v-model="iterationForm.day" placeholder="" :disabled="iterationForm.state=='0'">
              <el-option
                v-for="item in options"
                :key="item.key"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
            自动归档
          </span>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button
          :loading="saveLoading"
          type="primary"
          @click="saveProjectConf"
        >保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getProjectConf, updateProjectConf } from '@/api/vone/project/iteration'

// import storage from 'store'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      options: [
        { key: 1, value: '7', name: '7天' },
        { key: 2, value: '15', name: '15天' },
        { key: 3, value: '30', name: '一个月' },
        { key: 4, value: '90', name: '一季度' }
      ],

      iterationForm: {
        projectId: this.$route.params.id

      },
      iterationRules: {
        status: [{ required: true, message: '' }],
        day: [{ required: true, message: '请选择周期' }]

      },
      saveLoading: false,
      userList: [],
      disabledStart: false,
      disabledEnd: false
    }
  },
  mounted() {
    this.getProjectConf()
  },

  methods: {
    stateChange(val) {
      if (val == '0') {
        this.$set(this.iterationForm, 'day', '')
      }
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.iterationForm.resetFields()
    },
    async getProjectConf() {
      const res = await getProjectConf(this.iterationForm.projectId)
      this.data = res.data.map((v) => ({
        ...v,
        key: v.key,
        value: v.value
      }))

      var metadata = {}
      this.data.map((item) => {
        metadata[item.key] = item.value
      })
      this.iterationForm = metadata
    },

    async saveProjectConf() {
      this.$refs.iterationForm.validate(async(valid) => {
        if (!valid) return
        // 数据处理
        for (var k in this.iterationForm) {
          this.data.forEach(v => {
            if (k == v.key) {
              v.value = this.iterationForm[k]
            }
          })
        }
        const res = await updateProjectConf(this.data)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('操作成功')
        this.onClose()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.conffile{
	:deep(.el-select) {
   width:130px;
	}
}
.dayConf{
	:deep(.el-form-item__label) {
		padding:0;
	}
}
</style>
