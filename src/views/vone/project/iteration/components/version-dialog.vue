<template>
  <div>
    <el-dialog
      title="关联版本"
      width="520px"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-form
        ref="versionForm"
        :model="versionForm"
        :rules="rule"
      >
        <el-form-item label="产品" prop="productId">
          <el-select v-model="versionForm.productId" filterable placeholder="请选择产品" @change="checkProduct">
            <el-option v-for="item in productList" :key="item.id" :value="item.id" :label="item.name">
              {{ item.name }}
              <el-tag v-if="item.isMain" type="success" style="float:right">主</el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="productVersionId">
          <el-select v-model="versionForm.productVersionId" filterable clearable placeholder="请选择版本">
            <el-option v-for="item in planList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取消</el-button>&nbsp;
        <el-button
          :loading="saveLoading"
          type="primary"
          @click="saveVersion"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { apiProjectInfo } from '@/api/vone/project/index'
import { getProductVersionList } from '@/api/vone/product'
import { saveVersionProjectPlan } from '@/api/vone/project/iteration'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    productVersionId: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      versionForm: {
        productId: '',
        productVersionId: ''
      },
      rule: {
        productId: [{ required: true, message: '请选择产品' }]
      },
      productList: [],
      planList: [],
      saveLoading: false
    }
  },
  mounted() {
    this.getProjectInfo()
  },
  methods: {
    // 查询项目详情
    async getProjectInfo() {
      this.listLoading = true
      const res = await apiProjectInfo(this.$route.params.id)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      if (res.data && res.data.hostProductId) {
        this.productList = [
          {
            id: res.data.echoMap.hostProduct.id,
            name: res.data.echoMap.hostProduct.name,
            isMain: true
          }
        ]
      }
      if (res.data.echoMap.assistProduct) {
        this.productList.push(...res.data.echoMap.assistProduct)
      }
      if (this.productList && this.productList.length > 0) {
        this.versionForm.productId = this.productList[0].id
        await this.getProductVersion(this.productList[0].id)
      }
    },
    // 查询项目所属的产品的版本信息
    async getProductVersion(val) {
      const { data, isSuccess, msg } = await getProductVersionList({
        productId: val
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }

      this.planList = data
    },
    checkProduct(val) {
      this.getProductVersion(val)
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    async saveVersion() {
      try {
        await this.$refs.versionForm.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }
      const params = {
        productVersionId: this.versionForm.productVersionId ? this.versionForm.productVersionId : this.productVersionId,
        relation: this.versionForm.productVersionId == '' ? 'cancel' : 'relation',
        projectPlanIds: [this.id]
      }
      this.saveLoading = true
      saveVersionProjectPlan(params).then(res => {
        if (res.isSuccess) {
          this.saveLoading = false
          this.$message.success('保存成功')
          this.$emit('update:visible', false)
        } else {
          this.saveLoading = false
          this.$message.warning(res.msg)
        }
      })
    }

  }
}
</script>
