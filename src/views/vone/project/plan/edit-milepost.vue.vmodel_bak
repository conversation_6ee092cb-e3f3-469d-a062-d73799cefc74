<template>
  <div>
    <el-dialog title="编辑里程碑" width="60%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="iterationForm" v-loading="formLoading" label-position="top" :model="iterationForm" :rules="iterationRules">
        <el-row class="basicHeader" :gutter="24">
          <el-col :span="24">
            <el-input v-model="iterationForm.name" placeholder="请填写" />
          </el-col>
          <el-col :span="8">
            <div class="fixedItem">
              <span v-if="echoMap && echoMap.leadingBy">
                <vone-user-avatar :avatar-path="echoMap.leadingBy.avatarPath" :avatar-type="true" :show-name="false" height="42px" width="42px" />
              </span>
              <span v-else>
                <i class="iconfont el-icon-icon-light-avatar" style="font-size:42px" />
              </span>
              <el-form-item label="处理人" prop="leadingBy">
                <vone-remote-user v-model="iterationForm.leadingBy" :no-name="false" :default-data="pUserList" @updateSelectedRow="getSelectUser" />
              </el-form-item>

            </div>
          </el-col>
          <el-col :span="8">
            <div class="fixedItem">
              <i class="iconfont el-icon-icon-fill-zhuangtai" :style="{color:iterationForm.stateCode =='1'? '#adb0b8':iterationForm.stateCode=='2'?'#64befa':'#3cb540'}" />
              <el-form-item label="状态" prop="stateCode">
                <el-select v-model="iterationForm.stateCode" placeholder="请选择">
                  <el-option v-for="i in stateOption" :key="i.id" :label="i.name" :value="i.code">
                    <i style="display:inline-block;width:10px;height:10px;border-radius:50%;" :style="{'color':i.color,border:`2px solid ${i.color}`}" />
                    {{ i.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="fixedItem">
              <svg-icon icon-class="vone-date" />
              <el-form-item label="完成日期" prop="planEtime">
                <el-date-picker v-model="iterationForm.planEtime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择日期" />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="iterationForm.description" type="textarea" :rows="2" placeholder="请输入描述" maxlength="500" show-word-limit />
        </el-form-item>
      </el-form>
      <div class="deliver">
        <span>交付物</span>
        <el-button icon="el-icon-plus" type="text" @click="addDeliverables">交付物目标</el-button>
      </div>
      <el-table ref="deliverablesTable" :data="deliverablesList">
        <el-table-column width="150" show-overflow-tooltip prop="name" label="目标名称">
          <template slot-scope="scope">
            <span v-if="!scope.row.isShowName" class="name">{{ scope.row.name }}</span>
            <el-input v-if="scope.row.isShowName" v-model="scope.row.name" v-focus placeholder="请填写" @blur="blurFn(scope.row,scope.$index,'isShowName')" @change="editChange(scope.row,scope.$index)" />
            <el-button
              v-if="!scope.row.isShowName"
              class="buttons"
              icon="iconfont el-icon-application-edit"
              type="text"
              size="small"
              @click="editFn(scope.row, scope.$index, 'isShowName')"
            />
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型">
          <template slot-scope="scope">
            <span v-if="scope.row.type == 'FILE'">文件</span>
            <span v-if="scope.row.type == 'LINK'">链接</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip width="150" prop="fileName" label="交付物">
          <template slot-scope="scope">
            <span v-if="!scope.row.isShowLink" class="name file-name" @click="fileClick(scope.row)">{{ scope.row.type == 'FILE'? scope.row.fileName : scope.row.link }}</span>
            <el-input v-if="scope.row.isShowLink" v-model="scope.row.link" v-focus placeholder="请填写" @blur="blurFn(scope.row,scope.$index,'isShowLink')" @change="editChange(scope.row,scope.$index,'isShowLink')" />
            <el-button
              v-if="scope.row.type == 'LINK' && !scope.row.isShowLink"
              class="buttons"
              icon="iconfont el-icon-application-edit"
              type="text"
              size="small"
              @click="editFn(scope.row, scope.$index, 'isShowLink')"
            />
            <vone-upload v-if="scope.row.type == 'FILE' && !scope.row.isShowLink" ref="uploadFile" :limit="1" :multiple="false" :show-file-list="false" class="buttons" type="text" file-title="" :files-data="[]" @onSuccess="fileSuccess($event,scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小">
          <template slot-scope="scope">
            {{ scope.row.fileSize? scope.row.fileSize +'kb' : '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="deliverer" label="交付者">
          <template slot-scope="scope">
            <vone-user-avatar v-if="scope.row.echoMap.qaLeadingBy" :avatar-path="scope.row.echoMap.qaLeadingBy.avatarPath" :name="scope.row.echoMap.qaLeadingBy.name" width="20px" height="20px" />
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              icon="iconfont el-icon-application-delete"
              type="text"
              size="small"
              @click="removeDel(scope.row.id)"
            />
          </template>
        </el-table-column>
      </el-table>

      <el-form v-if="isShow" ref="addDeliverablesForm" :model="addDeliverablesForm">
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="14">
            <el-form-item
              prop="name"
              :rules="[
                { required: true, message: '请输入目标名称', trigger: 'blur'}
              ]"
            >
              <el-input v-model="addDeliverablesForm.name" placeholder="请输入目标名称" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item
              prop="type"
              :rules="[
                { required: true, message: '请选择类型', trigger: 'change'}
              ]"
            >
              <el-select v-model="addDeliverablesForm.type" placeholder="请选择">
                <el-option label="文件" value="FILE" />
                <el-option label="链接" value="LINK" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button @click="resetForm">取 消</el-button>&nbsp;
            <el-button :loading="saveLoading" type="primary" @click="onSubmit">保存</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button :loading="saveLoading" type="primary" @click="sureAdd">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { download, catchErr } from '@/utils'
import storage from 'store'
import { apiBaseFileLoadById } from '@/api/vone/base/file'
import { apiProjectUserNoPage } from '@/api/vone/project/index'

import { apiAlmProjectPlanAdd, apiAlmPlanInfo } from '@/api/vone/project/iteration'
import { getDeliverablesList, addDeliverables, deleteDeliverables, editDeliverables } from '@/api/vone/project/deliverables'

export default {
  directives: {
    focus: {
      inserted: function(el) {
        el.querySelector('input').focus()
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    hasParent: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: undefined
    },
    node: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formLoading: false,
      iterationForm: {
        projectId: this.$route.params.id,
        type: 'MILESTONE',
        stateCode: '1',
        sort: '1'
      },
      iterationRules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' }],
        planEtime: [{ required: true, message: '请选择完成日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        stateCode: [{ required: true, message: '请选择里程碑状态' }],
        sort: [{ required: true, message: '请选择添加在哪个阶段后' }]
      },
      saveLoading: false,
      disabledStart: false,
      disabledEnd: false,
      tableLoading: false,
      deliverablesList: [],
      addDeliverablesForm: {},
      isShow: false,
      rowId: '',
      stateOption: [
        {
          name: '未开始',
          code: '1',
          color: '#adb0b8'
        }, {
          name: '进行中',
          code: '2',
          color: '#64befa'
        }, {
          name: '已完成',
          code: '3',
          color: '#3cb540'
        }
      ],
      pUserList: [],
      echoMap: {
        leadingBy: null
      }
    }
  },
  mounted() {
    this.getProjectUser()
    this.getDeliverablesList()
    if (this.id) {
      // 里程碑详情
      this.getPlanInfo()
    }
  },
  methods: {
    getSelectUser(user) {
      this.echoMap.leadingBy = user[0] || null
    },
    editFn(row, index, type) {
      row[type] = true
      this.$set(this.deliverablesList, index, row)
    },
    blurFn(row, index, type) {
      row[type] = false
      this.$set(this.deliverablesList, index, row)
    },
    async editChange(row, index, type) {
      row[type] = false
      // this.$set(this.deliverablesList, index, row)
      this.editMethod(row, type)
    },
    async editMethod(e, type) {
      if (type != 'isShowName') {
        const userInfo = storage.get('user')
        e.deliverer = userInfo.id
      }
      const res = await editDeliverables(e)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.getDeliverablesList()
      }
    },
    async fileSuccess(e, row) {
      if (e.length > 0) {
        row.fileName = e[0].name
        row.fileId = e[0].id
        row.fileSize = e[0].size
      }
      this.editMethod(row)
    },
    async fileClick(row) {
      if (row.type == 'LINK') {
        const link = row.link
        if (link && (row.type.substr(0, 7).toLowerCase() == 'http://' || row.type.substr(0, 8).toLowerCase() == 'https://')) {
          window.open(row.link, '_blank')
        } else {
          window.open('http://' + row.link, '_blank')
        }
        row.type.substr(0, 7).toLowerCase()
      } else if (row.type == 'FILE') {
        try {
          download(row.fileName, await apiBaseFileLoadById([row.fileId]
          ))
          this.tableLoading = false
        } catch (e) {
          this.tableLoading = false
          return
        }
      }
    },
    async removeDel(e) {
      try {
        await this.$confirm(`删除后不可恢复，确定要删除吗`, '删除', {
          type: 'warning',
          closeOnClickModal: false,
          customClass: 'delConfirm'
        })
        const res = await deleteDeliverables([e])
        if (res.isSuccess) {
          this.$message.success('删除成功')
          this.getDeliverablesList()
        }
      } catch {
        return
      }
    },
    async onSubmit() {
      await this.$refs.addDeliverablesForm.validate()
      try {
        const userInfo = storage.get('user')
        this.$set(this.addDeliverablesForm, 'deliverer', userInfo.id)

        this.addDeliverablesForm.planId = this.id
        const res = await addDeliverables(this.addDeliverablesForm)
        if (res.isSuccess) {
          this.$message.success('添加成功')
          this.isShow = false
          this.getDeliverablesList()
        }
      } catch {
        return
      }
    },
    resetForm() {
      this.isShow = false
      this.$refs.addDeliverablesForm.resetFields()
    },
    addDeliverables() {
      this.isShow = true
    },
    // 查询交付物列表
    async getDeliverablesList() {
      const res = await getDeliverablesList({
        planId: this.id
      })
      if (!res.isSuccess) {
        return
      }
      this.deliverablesList = res.data
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.iterationForm.resetFields()
    },
    async sureAdd() {
      this.saveLoading = true
      try {
        await this.$refs.iterationForm.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }
      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...this.iterationForm
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('保存成功')
      this.onClose()
      this.$emit('success')
    },
    async getPlanInfo() {
      this.formLoading = true
      const res = await apiAlmPlanInfo(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
    },
    async getProjectUser() {
      const [res, err] = await catchErr(apiProjectUserNoPage({
        projectId: this.$route.params.id
      }))
      if (err) return
      if (!res.isSuccess) {
        return
      }

      this.pUserList = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.basicHeader {
  background-color: var(--node-cildren-bg-color);
  min-height: 100px;
  padding: 18px;
  border-bottom: 1px solid var(--disabled-bg-color);
  margin: -20px -24px 0 !important;
  .el-col-8 {
    & :hover {
      background: var(--col-hover-bg);
    }

    :deep(.el-select .el-input.is-focus .el-input__inner) {
      background-color: var(--col-hover-bg);
    }

    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 42px;
        margin-right: 10px;
      }
    }
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      & :focus {
        background: var(--main-bg-color);
      }
    }
    :deep(.el-input__suffix) {
      display: none;
    }
    :deep(.el-form-item__label) {

      margin-left: 10px;
    }
    :deep(.el-input--small) {
      font-size: 14px;
    }

    :deep(.el-input.is-disabled .el-input__inner) {
      color: #000 !important;
      font-size: 14px;
    }
    :deep(.el-date-editor.el-input) {
      width: 100%;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }

  .el-col-24 {
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      font-weight: bold;
      font-size: 16px;
    }

    :deep(.el-input__inner:focus) {
      background: var(--col-hover-bg);
    }
    & :hover {
      background: var(--col-hover-bg);
    }
  }
}
.deliver {
  padding: 13px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-weight: 500;
    font-size: 14px;
  }
}
.el-table {
  .name {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 42px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 32px;
  }
  .buttons {
    min-width: 32px;
    display: none;
    float: right;
  }
  .file-name {
    color: #3E7BFA;
    cursor:pointer;
  }
  :deep(.upload) {
    width: 42px;
    text-align: center;
    .el-button {
      min-width: 32px;
    }
  }
}
:deep(.hover-row) {
  .buttons {
    display: block;
  }
}
:deep(th.el-table__cell.is-leaf) {
	height: 48px;
	background-color: var(--bottom-bg-color);
}
.el-select-dropdown__item {
  display: flex;
  align-items: center;
  gap:0 8px;
  padding: 0 10px;
}
</style>
