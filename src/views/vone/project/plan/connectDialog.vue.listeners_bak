<template>
  <!-- vone-custom-table -->
  <el-dialog title="关联工作项" top="5vh" width="800px" v-model:visible="visible" :before-close="onClose" class="connectDialog" :close-on-click-modal="false" append-to-body v-on="$listeners">
    <el-table
      ref="connectItemTable"
      v-loading="loading"
      table-key="connectItemTable"
      :table-options="tableOptions"
      row-key="bizId"
      :table-data="tableData"
      height="420px"
      :show-column="false"
      @getTableData="getWorkItemlist"
      @selection-change="onSelectionChange"
    >
      <vone-search-dynamic
        slot="search"
        :project-id="$route.params.id"
        v-model:model="formData"
        label-position="top"
        @getTableData="getWorkItemlist"
      >
        <el-row :gutter="20">
          <el-col v-for="item in filterList" :key="item.key" :span="12">
            <el-form-item :label="item.name" :prop="item.key">
              <!-- 人员组件 -->
              <vone-icon-select v-if="item.type == 'user'" v-model="formData[item.key]" select-type="user" clearable filterable multiple :data="pUserList" style="width:100%">
                <el-option v-for="item in pUserList" :key="item.id" :label="item.name" :value="item.id">
                  <vone-user-avatar v-if="item.avatarPath" :avatar-path="item.avatarPath" :avatar-type="item.avatarType" :show-name="false" height="22px" width="22px" />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
              <!-- 输入框 -->
              <el-input v-else-if="item.type == 'input'" v-model="formData[item.key]" :placeholder="item.placeholder" clearable />

              <!-- 下拉多选框 -->
              <el-select v-else-if="item.type == 'select'" v-model="formData[item.key]" :placeholder="item.placeholder" multiple clearable filterable>
                <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'planIds' ? i.id : i.code" />
              </el-select>

            </el-form-item>
          </el-col>
        </el-row>
      </vone-search-dynamic>
      <template slot="actions">
        已选工作项 <b class="count">{{ selected.length }}</b> 个
      </template>
      <el-table-column type="selection" width="55" reserve-selection />
      <el-table-column label="名称" prop="name" show-overflow-tooltip>
        <template v-slot="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="stateCode" width="100px">
        <template v-slot="{ row }">
          <span v-if="row.echoMap&&row.echoMap.stateCode" class="tagCustom" :style="{color:row.echoMap.stateCode.color,border:'1px solid currentColor'}">{{ row.echoMap.stateCode.name }}</span>
          <span v-else class="status-text">{{ row.stateCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="优先级" prop="priotory" width="100px">
        <template v-slot="{ row }">
          <span v-if="row.echoMap&&row.echoMap.priorityCode"><i :class="['iconfont',row.echoMap.priorityCode.icon]" :style="{color:row.echoMap.priorityCode.color}" />{{ row.echoMap.priorityCode.name }}</span>
          <span v-else>{{ row.priorityCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划完成时间" width="110px">
        <template v-slot="{ row }">
          <span>{{ row.planEtime |format }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" prop="handleBy" width="100px">
        <template v-slot="{ row }">
          <span v-if="row.handleBy && row.echoMap && row.echoMap.handleBy">
            <vone-user-avatar :avatar-path="row.echoMap.handleBy.avatarPath" :name="row.echoMap.handleBy.name" />
          </span>
        </template>
      </el-table-column>
      <el-table-column label="负责人" prop="leadingBy" width="100px">
        <template v-slot="{ row }">
          <span v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy">
            <vone-user-avatar :avatar-path="row.echoMap.leadingBy.avatarPath" :name="row.echoMap.leadingBy.name" />
          </span>

        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" style="text-align:right">
      <el-button @click="onClose">取消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiProjectUserNoPage } from '@/api/vone/project'
import { catchErr } from '@/utils'
import { apiAlmPlanLinkedSprint, getWorkItemsByPage } from '@/api/vone/project/iteration'
import dayjs from 'dayjs'

export default {
  filters: {
    format(value) {
      return value ? dayjs(value).format('YYYY-MM-DD') : ''
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 关联的数据
    infoData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      formData: {
        name: '',
        handleBys: []
      },
      pUserList: [],
      filterList: [
        {
          name: '名称',
          key: 'name',
          type: 'input',
          placeholder: '请输入名称'
        },
        {
          name: '处理人',
          key: 'handleBys',
          type: 'user'
        }
      ],
      tableOptions: {},
      tableData: { records: [] },
      selected: [] // 选中数据

    }
  },
  async mounted() {
    this.getProjectUser()
    // this.getWorkItemlist()
  },
  methods: {
    // 查询项目下人员
    async getProjectUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id
      })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.pUserList = res.data
    },
    // 查询未关联的工作项
    async getWorkItemlist() {
      const tableAttr = this.$refs['connectItemTable']?.exportTableQueryData()
      const params = {
        ...tableAttr,
        extra: {},
        model: {
          ...this.formData,
          projectId: this.$route.params.id,
          planId: -1
        }
      }
      this.loading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(getWorkItemsByPage(params))
      this.loading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    onSelectionChange(selection) {
      this.selected = selection
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    async confirm() {
      if (this.selected.length === 0) {
        this.$message.warning('请选择关联的工作项')
        return
      }
      const selectedList = this.selected.map(item => {
        return {
          issueId: item.bizId,
          planId: this.infoData.id,
          typeClassify: item.classify.code,
          typeCode: item.typeCode
        }
      })

      const [{ isSuccess, msg }, err] = await catchErr(apiAlmPlanLinkedSprint(selectedList))
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('关联成功')
      this.$emit('success')
      this.onClose()
    }

  }
}
</script>
<style lang="scss" scoped>
.count {
  font-weight: 600;
  color: var(--main-font-color);
}

 .connectDialog :deep(.el-dialog .el-dialog__body) {
  padding: 16px;
}
</style>

