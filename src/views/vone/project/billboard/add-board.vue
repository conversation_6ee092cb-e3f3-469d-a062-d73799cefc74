<template>
  <div>
    <el-dialog title="新增看板" v-model="kanbanAddDialogShow" :close-on-click-modal="false" :before-close="closeKanbanAdd">
      <el-form ref="kanbanAddForm" :model="kanbanAddForm" :rules="kanbanAddFormRules" @submitValid="saveAddKanban">
        <el-form-item label="看板名称" prop="name">
          <el-input v-model="kanbanAddForm.name" />
        </el-form-item>
        <el-form-item label="事项类型" prop="issueType">
          <el-select v-model="kanbanAddForm.issueType" multiple>
            <el-option
              v-for="(type) in issueTypeList"
              :key="type.issueTypeId"
              :label="type.issueTypeName"
              :value="type.issueTypeId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="kanbanAddDialogShow = false">取消</el-button>
        <el-button
          type="primary"
          :loading="kanbanAddSaveLoading"
          @click="$refs.kanbanAddForm.submit()"
        >确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'

const validateName = (rule, value, callback) => {
  const val = value && value.trim() || ''
  if (!val) {
    callback('请输入看板名称')
  } else {
    callback()
  }
}
const kanbanAddFormRules = {
  name: [{ required: true, validator: validateName }],
  issueType: [{ required: true, message: '请选择事项类型', type: 'array' }]
}

export default {
  data() {
    return {
      kanbanAddForm: {},
      kanbanAddFormRules,
      kanbanAddDialogShow: false,
      kanbanAddSaveLoading: false,
      issueTypeList: [],
      projectKey: 'A'
    }
  },
  computed: {
    ...mapState({
      user: state => state.user.user
    })
  },
  watch: {
  },
  mounted() {
    this.loadIssueTypeList()
  },
  methods: {
    async saveAddKanban() {
      // const issueTypes = this.issueTypeList.filter(type => {
      //   return this.kanbanAddForm.issueType.indexOf(type.issueTypeId) !== -1
      // })
      // const formData = {
      //   name: this.kanbanAddForm.name,
      //   projectKey: 'A',
      //   issueTypeList: issueTypes.map(type => {
      //     return {
      //       issueType: type.issueType,
      //       issueTypeId: type.issueTypeId
      //     }
      //   })
      // }
      // this.kanbanAddSaveLoading = true
      // const { data, success, message } = await apiAlmPostProjectCreateKanban(formData)
      // this.kanbanAddSaveLoading = false
      // if (!success) {
      //   this.$message.warning(message)
      //   return
      // }
      // this.jumpKanbanEdit(data.id)
    },
    closeKanbanAdd() {
      this.$refs.kanbanAddForm.resetFields()
      this.kanbanAddDialogShow = false
    },
    jumpKanbanEdit(id) {
      this.$router.push({
        name: 'project_kanban_edit',
        params: { id, projectKey: 'A' }
      })
    },
    openAddKanban() {
      this.$refs.lane.full(false)
      this.kanbanAddDialogShow = true
      this.$nextTick(() => {
        this.$refs.kanbanAddForm.$refs.form.clearValidate()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";
</style>

