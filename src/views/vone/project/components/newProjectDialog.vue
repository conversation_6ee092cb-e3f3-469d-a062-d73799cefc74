<template>

  <el-dialog v-if="visible" :title="title" width="1000px" v-model="visible" :before-close="onClose" class="project-dialog" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <el-form ref="projectForm" v-loading="pageLoading" class="project-form" :model="projectForm" label-position="top" :rules="projectFormRules">
      <div class="left-form">
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目模式" prop="typeCode">
              <el-radio-group v-model="projectForm.typeCode">
                <el-radio v-for="(item, index) in projectTypeList" :key="index" :label="item.code" :disabled="id || fastType ? true :false">{{
                  item.name
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目标识" prop="code">
              <el-input v-model="projectForm.code" placeholder="请输入项目标识" :disabled="id ?true :false" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="classify">
              <el-select v-model="projectForm.classify" placeholder="请选择项目类型" filterable @change="changeClassify">
                <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.code" :title="item.name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <!-- 描述 -->
            <el-form-item label="项目描述" prop="description">
              <el-input v-model="projectForm.description" type="textarea" placeholder="请输入项目描述" :rows="8" maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="right-form">
        <!-- 组织结构 -->
        <el-form-item label="组织机构" prop="orgId">
          <vone-tree-select v-model="projectForm.orgId" search-nested :tree-data="orgDatas" placeholder="请选择机构" :disabled="id ? true :false" />
        </el-form-item>
        <!-- 项目负责人 -->
        <el-form-item label="项目经理" prop="leadingBy">
          <vone-remote-user v-model="projectForm.leadingBy" />

        </el-form-item>
        <!-- 产品 -->
        <el-alert
          v-if="isChange"
          title="切换产品后,项目与产品的关联关系会修改,请谨慎操作"
          type="warning"
          show-icon
          @close="isChange = false"
        />
        <el-form-item label="关联项目" prop="projectIds">
          <el-select v-model="projectForm.projectIds" placeholder="请选择关联项目" filterable multiple clearable>
            <el-option v-for="item in allProjectList" :key="item.value" :label="item.name" :value="item.id" />

          </el-select>
        </el-form-item>
        <el-form-item label="主办产品" prop="hostProductId" style="display: none;">
          <el-select
            v-model="projectForm.hostProductId"
            placeholder="请选择产品"
            style="width: 100%"
            filterable
            clearable
            @focus="setOptionWidth"
            @change="changeProduct"
          >
            <el-option v-for="item in productList" :key="item.value" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
          </el-select>
        </el-form-item>
        <el-form-item :label="projectForm.classify == 'DELIVERY' ? '交付产品' : '研发产品'" prop="assistProductIds">
          <el-select
            v-model="projectForm.assistProductIds"
            placeholder="请选择"
            style="width: 100%"
            filterable
            clearable
            multiple
            @focus="setOptionWidth"
          >
            <el-option v-for="item in productList" :key="item.value" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
          </el-select>
        </el-form-item>
        <!-- 交付产品集 -->
        <!-- <el-form-item v-if="projectForm.classify == 'DELIVERY'" label="产品集" prop="projectProductsets" filterable>
          <el-select v-model="projectForm.projectProductsets" placeholder="请选择产品集" style="width: 100%" filterable clearable multiple @focus="setOptionWidth">
            <el-option v-for="item in productSetData" :key="item.id" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="归属项目集" prop="programId">
          <el-select v-model="projectForm.programId" placeholder="请选择归属项目集" style="width:100%" clearable @focus="setOptionWidth">
            <el-option v-for="item in programDatas" :key="item.id" :label="item.name" :value="item.id" :style="{width:selectOptionWidth}" />
          </el-select>
        </el-form-item> -->
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveProject">确定</el-button>
    </div>
  </el-dialog>

</template>

<script>
import { orgList } from '@/api/vone/base/org'
import { getAllProjectType, apiProjectInfo, apiProjectEdit, productListByCondition, apiAlmProjectNoPage, apiProjectSave } from '@/api/vone/project/index'
import { ProgramListByCondition } from '@/api/vone/ssp/index'
import { gainTreeList } from '@/utils'
import storage from 'store'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { getqueryList } from '@/api/vone/product/productfit'
import { cloneDeep } from 'lodash'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    fastType: {
      type: String,
      default: undefined // 根据模板创建---判断是敏捷还是瀑布
    }
  },
  data() {
    return {
      allProjectList: [], // 交付类项目可以选的关联项目
      originProduct: '',
      selectOptionWidth: '',
      projectForm: {
        orgId: ''
      },
      projectFormRules: {
        typeCode: [{ required: true, message: '请选择项目类型' }],
        name: [{ required: true, message: '请输入项目名称', trigger: 'blur' },
          {
            pattern: '^([^ ]){1,50}$',
            message: '请输入不超过50个除空格外的字符'
          }],
        code: [
          { required: true, message: '请输入项目标识', trigger: 'blur' },
          {
            pattern: '^([a-zA-Z0-9-]){1,30}$',
            message: '请输入不超过30个字母、数字或横线(-)组成的标识'
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾'
          }
        ],
        orgId: [{ required: true, message: '请选择组织机构' }],
        leadingBy: [{ required: true, message: '请选择项目经理' }],
        classify: [
          { required: true, message: '请选择项目类型' }
        ]
      },
      projectTypeList: [],
      programDatas: [], // 项目集
      productList: [],
      productSetData: [],
      orgDatas: [], // 机构
      saveLoading: false,
      pageLoading: false,
      isChange: false,
      typeList: [],
      allUser: [],
      orginProjectInfo: null
    }
  },
  mounted() {
    if (!this.id) {
      const userInfo = storage.get('user')
      this.$set(this.projectForm, 'leadingBy', userInfo.id)
    }
    // this.getProductSet()
    this.getOrgList() // 查询机构
    this.getAllProjectType() // 查询项目类型
    this.getAllProductList() // 查询产品
    this.getProgramList() // 查询项目集
    this.getTypeList() // 查询项目类型,交付类/研发类
    this.getAllUser()
    if (this.id) {
      this.getProjectInfo()
    }
  },
  methods: {
    changeClassify(val) {
      this.$set(this.projectForm, 'assistProductIds', [])
      this.$set(this.projectForm, 'projectProductsets', [])
      this.$set(this.projectForm, 'projectIds', [])
      this.getAllProjectList(val)
    },
    // 获取产品集
    getProductSet() {
      getqueryList().then(res => {
        if (res.isSuccess) {
          this.productSetData = res.data
        }
      })
    },
    // 项目类型
    async getTypeList() {
      const res = await apiBaseDictNoPage({ type: 'PROJECT_CLASSIFY' })
      if (!res.isSuccess) return
      this.typeList = res.data
      this.$set(this.projectForm, 'classify', res.data?.[0]?.code)
      res.data?.[0]?.code && this.getAllProjectList(res.data?.[0]?.code)
    },
    // 关联项目
    async getAllProjectList(val) {
      const res = await apiAlmProjectNoPage({ classify: val == 'DEVELOP' ? 'DELIVERY' : 'DEVELOP' })
      if (!res.isSuccess) return
      this.allProjectList = res.data
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeProduct(val) {
      if (!this.title == '编辑项目' || !this.originProduct || val === this.originProduct) return
      this.isChange = true
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.projectForm.resetFields()
    },
    // 回显
    async getProjectInfo() {
      this.pageLoading = true
      const res = await apiProjectInfo(this.id)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      this.changeClassify(res.data.classify)
      res.data.projectProductsets = res.data?.projectProductsets.map(item => item.productsetId)
      this.orginProjectInfo = cloneDeep(res.data)
      this.projectForm = res.data
      this.$set(this.projectForm, 'projectIds', res.data?.projects?.map(r => r.id))
      this.$set(this.projectForm, 'projectStage', res.data?.projectStage?.code)
      this.originProduct = res.data.hostProductId
    },
    async getProgramList() {
      this.pageLoading = true
      const res = await ProgramListByCondition()
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      this.programDatas = res.data
    },
    // 查询所有机构
    getOrgList() {
      this.pageLoading = true
      orgList().then(res => {
        const orgTree = gainTreeList(res.data)
        this.orgDatas = orgTree
      })
      this.pageLoading = false
      const userInfo = storage.get('user')
      this.$set(this.projectForm, 'orgId', userInfo.orgId)
    },
    getAllUser() {
      apiBaseAllUserNoPage().then(res => {
        if (res.isSuccess) {
          this.allUser = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 查询项目类型
    async getAllProjectType() {
      this.pageLoading = true
      const res = await getAllProjectType()
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.projectTypeList = res.data
      this.$set(this.projectForm, 'typeCode', res.data[0].code)
      if (this.fastType) {
        this.$set(this.projectForm, 'typeCode', this.fastType)
      }
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await productListByCondition()
      if (!res.isSuccess) {
        return
      }
      this.productList = res.data
    },
    // 保存
    async saveProject() {
      await this.$refs.projectForm.validate()
      const paramsData = cloneDeep(this.projectForm)
      paramsData.projectProductsets = this.projectForm.projectProductsets?.map(item => { return { productsetId: item } })
      if (this.id) {
        this.editSave(paramsData)
      } else {
        let initiation = false
        this.projectTypeList.forEach(item => {
          if (item.code == this.projectForm.typeCode) {
            initiation = item.initiation
          }
        })
        if (initiation) { // 走流程
          this.hold(paramsData) // 只保存
        } else {
          this.createSave(paramsData)
        }
      }
    },
    // 不走流程新建
    async createSave(params) {
      this.saveLoading = true
      const res = await apiProjectSave(
        params
      ).catch(res => {
        this.saveLoading = false
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.onClose()
      this.$emit('success', params.classify)
    },
    // 走流程新建
    async hold(params) {
      this.saveLoading = true
      const res = await apiProjectSave(
        params
      ).catch(res => {
        this.saveLoading = false
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }

      this.onClose()
      this.$emit('success', params.classify)
      const h = this.$createElement
      await this.$confirm('', {
        message: h('div', null, [
          h('i', { class: 'iconfont el-icon-tips-check-circle-fill', style: 'color:#00BF80;font-size: 16px;vertical-align: bottom' }),
          h('span', { style: 'margin-left:5px;font-weight:600;vertical-align:top;color:#00BF80 ; font-size:16px' }, '创建项目成功'),
          h('br', undefined, undefined),
          h('span', { style: 'display:inline-block;margin:10px 0 0 20px;' }, '项目审批中,请到任务中心 '),
          h('span', {
            style: 'color: #3E7BFA;display:inline-block;margin:10px 0 0 10px;cursor:pointer',
            on: {
              click: () => {
                this.toTaskCenter()
              }
            }}, '查看审批详情')
        ]),
        showClose: false, // 是否显示右上角的x
        confirmButtonText: '我知道了',
        showCancelButton: false
      })
    },
    // 编辑
    async editSave(params) {
      try {
        this.saveLoading = true
        const res = await apiProjectEdit(
          params
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('修改项目信息成功')
        this.onClose()
        if (this.orginProjectInfo?.classify != params.classify) {
          this.$emit('success')
        } else {
          this.$emit('success', params.classify)
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
    toTaskCenter() {
      this.$msgbox.close()
      this.$router.push({ name: 'task_center' })
    }
  }
}
</script>
<style lang="scss" scoped>
.project-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}
.project-form {
  display: flex;
  .left-form {
    flex: 1;
    padding: 16px;
  }
  .right-form {
    width: 350px;
    padding: 16px;
    border-left: 1px solid var(--solid-border-color);
  }
}
</style>
