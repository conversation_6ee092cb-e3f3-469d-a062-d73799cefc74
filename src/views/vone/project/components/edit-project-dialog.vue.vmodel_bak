<template>
  <el-dialog v-if="visible" width="60%" v-model:visible="visible" :before-close="onClose" class="dialogBox" :close-on-click-modal="false" :modal-append-to-body="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <span slot="title" class="vone-border-tabs">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="编辑项目" name="editProject" />
        <el-tab-pane v-if="getlprocId(row)" label="立项流程" name="beginFlow" />
        <el-tab-pane v-if="getlprocId(row)" label="结项流程" name="endFlow" />

      </el-tabs>
    </span>
    <template v-if="activeName == 'editProject'" v-loading="pageLoading">

      <el-form ref="projectForm" :model="projectForm" label-position="top" :rules="projectFormRules">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item label="项目模式" prop="typeCode">
              <el-radio-group v-model="projectForm.typeCode">
                <el-radio v-for="(item, index) in projectTypeList" :key="index" :label="item.code" :disabled="id || fastType ? true :false">{{
                  item.name
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-row :gutter="50">
              <el-col :span="24">
                <el-form-item label="项目名称" prop="name">
                  <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目标识" prop="code">
                  <el-input v-model="projectForm.code" placeholder="请输入项目标识" :disabled="id ?true :false" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目类型" prop="classify">
                  <el-select v-model="projectForm.classify" placeholder="请选择项目类型" filterable :disabled="id ?true :false">
                    <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.code" :title="item.name" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 描述 -->
            <el-form-item label="项目描述" prop="description">
              <el-input v-model="projectForm.description" type="textarea" placeholder="请输入项目描述" :rows="8" maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="8" class="projectAddBorder">
            <!-- 组织结构 -->
            <el-form-item label="组织机构" prop="orgId">
              <vone-tree-select v-model="projectForm.orgId" search-nested :tree-data="orgDatas" placeholder="请选择机构" :disabled="id ? true :false" />
            </el-form-item>
            <!-- 项目负责人 -->
            <el-form-item label="项目经理" prop="leadingBy">
              <vone-remote-user v-model="projectForm.leadingBy" />
            </el-form-item>

            <el-form-item v-if="projectForm.classify" label="关联项目" prop="projectIds">
              <el-select v-model="projectForm.projectIds" placeholder="请选择关联项目" filterable multiple>
                <el-option v-for="item in allProjectList" :key="item.value" :label="item.name" :value="item.id" :title="item.name" />
              </el-select>
            </el-form-item>
            <!-- 产品 -->
            <el-form-item label="主办产品" prop="hostProductId" filterable>
              <el-select v-model="projectForm.hostProductId" placeholder="请选择产品" style="width: 100%" filterable clearable @change="changeProduct" @focus="setOptionWidth">
                <el-option v-for="item in productList" :key="item.value" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
              </el-select>
            </el-form-item>
            <el-form-item label="辅办产品" prop="assistProductIds" filterable>
              <el-select v-model="projectForm.assistProductIds" placeholder="请选择产品" style="width: 100%" filterable clearable multiple @focus="setOptionWidth">
                <el-option v-for="item in productList.filter(v=>v.id!==projectForm.hostProductId)" :key="item.value" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目集" prop="programId">
              <el-select v-model="projectForm.programId" placeholder="请选择项目集" style="width:100%" clearable>
                <el-option v-for="item in programDatas" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </template>

    <span v-if="activeName == 'editProject'" slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveProject">确定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import { orgList } from '@/api/vone/base/org'
import { apiProjectSave, getAllProjectType, apiProjectInfo, apiProjectEdit, productListByCondition, apiAlmProjectNoPage } from '@/api/vone/project/index'
import { gainTreeList } from '@/utils'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import storage from 'store'
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    fastType: {
      type: String,
      default: undefined // 根据模板创建---判断是敏捷还是瀑布
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      originProduct: '',
      selectOptionWidth: '',
      projectForm: {
        // typeName: 'AGILE'
        orgId: ''
      },
      projectFormRules: {
        typeCode: [{ required: true, message: '请选择项目类型' }],
        name: [{ required: true, message: '请输入项目名称', trigger: 'blur' },
          {
            pattern: '^([^ ]){1,50}$',
            message: '请输入不超过50个除空格外的字符'
          }],
        code: [
          { required: true, message: '请输入项目标识', trigger: 'blur' },
          {
            pattern: '^([a-zA-Z0-9-]){1,30}$',
            message: '请输入不超过30个字母、数字或横线(-)组成的标识'
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾'
          }
        ],
        orgId: [{ required: true, message: '请选择组织机构' }],
        leadingBy: [{ required: true, message: '请选择项目经理' }],
        classify: [
          { required: true, message: '请选择项目类型' }
        ]
      },
      projectTypeList: [],
      programDatas: [], // 项目集
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.projectForm.startTime) {
            return (
              time.getTime() <
              new Date(this.projectForm.startTime).getTime() ||
              time.getTime() >
              new Date(this.projectForm.endTime).getTime()
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          if (this.projectForm.endTime) {
            return (
              time.getTime() <
              new Date(this.projectForm.startTime).getTime() ||
              time.getTime() >
              new Date(this.projectForm.endTime).getTime() ||
              time.getTime() < new Date(this.projectForm.startTime).getTime()
            )
          }
          return (
            time.getTime() < new Date(this.projectForm.startTime).getTime()
          )
        }
      },
      productList: [],

      orgDatas: [], // 机构
      saveLoading: false,
      pageLoading: false,
      activeName: 'editProject',
      tabList: [
        {
          label: '立项流程',
          name: 'beginFlow',
          active: false
        },
        {
          label: '结项流程',
          name: 'endFlow',
          active: false
        }
      ],
      typeList: [],
      allProjectList: []
    }
  },
  computed: {
    getlprocId() {
      return function(row) {
        return row.echoMap && row.echoMap.projectProcess && row.echoMap.projectProcess.lprocId
      }
    }
  },
  watch: {
    'projectForm.classify': {
      handler(value) {
        this.getAllProjectList(value)
      }

    }

  },
  mounted() {
    if (!this.id) {
      const userInfo = storage.get('user')
      this.$set(this.projectForm, 'leadingBy', userInfo.id)
    }

    this.getOrgList() // 查询机构
    this.getAllProjectType() // 查询项目类型
    this.getAllProductList() // 查询产品

    this.getTypeList()
    if (this.id) {
      this.getProjectInfo()
    }
  },
  methods: {
    changeClassify(val) {
      this.getAllProjectList(val)
    },
    // 项目类型
    async getTypeList() {
      const res = await apiBaseDictNoPage(
        {
          type: 'PROJECT_CLASSIFY'
        }
      )
      if (!res.isSuccess) {
        return
      }
      this.typeList = res.data
    },
    // 关联项目
    async getAllProjectList(val) {
      this.pageLoading = true
      const res = await apiAlmProjectNoPage(
        {
          classify: val == 'DEVELOP' ? 'DELIVERY' : 'DEVELOP'
        }
      )
      if (!res.isSuccess) {
        return
      }
      this.allProjectList = res.data
      this.pageLoading = false
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeProduct(val) {
      if (!this.title == '编辑项目' || !this.originProduct || val === this.originProduct) {
        return
      }

      this.$confirm('切换产品后，当前项目关联产品的关联关系会同时修改，确定要切换产品吗?', '切换产品', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const res = await apiProjectEdit(
          this.projectForm
        )

        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('修改项目信息成功')
        this.onClose()
        this.$emit('success')
      }).catch(() => {
      })
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.projectForm.resetFields()
    },
    // 回显
    async getProjectInfo() {
      this.pageLoading = true
      const res = await apiProjectInfo(this.id)
        .catch(res => {
          this.pageLoading = false
        })

      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }

      this.$set(res.data, 'projectIds', res.data?.projects?.map(r => r.id))

      this.projectForm = res.data
      this.originProduct = res.data.hostProductId

      if (!res.data.classify) {
        return
      }
      this.getAllProjectList(res.data.classify)
      this.pageLoading = false
    },
    changeStart(v) {
      if (this.projectForm.endTime && v > this.projectForm.endTime) {
        this.$message.warning('开始时间不能大于结束时间')
      }
    },
    // 查询所有机构
    getOrgList() {
      this.pageLoading = true
      orgList().then(res => {
        const orgTree = gainTreeList(res.data)
        this.orgDatas = orgTree
      })
      this.pageLoading = false
      const userInfo = storage.get('user')
      this.$set(this.projectForm, 'orgId', userInfo.orgId)
    },
    // 查询项目类型
    async getAllProjectType() {
      this.pageLoading = true
      const res = await getAllProjectType()
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.projectTypeList = res.data
      this.$set(this.projectForm, 'typeCode', res.data[0].code)
      if (this.fastType) {
        this.$set(this.projectForm, 'typeCode', this.fastType)
      }
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await productListByCondition()
      if (!res.isSuccess) {
        return
      }
      this.productList = res.data
    },
    // 保存
    async saveProject(id) {
      await this.$refs.projectForm.validate()
      if (this.id) {
        this.editSave()
      } else {
        try {
          this.saveLoading = true
          const res = await apiProjectSave(
            this.projectForm
          )
          this.saveLoading = false
          if (!res.isSuccess) {
            this.$message.error(res.msg)
            return
          }
          this.$message.success('保存成功')
          this.onClose()
          this.$emit('success')
        } catch (e) {
          this.saveLoading = false
        }
      }
    },
    // 编辑
    async editSave() {
      try {
        this.saveLoading = true
        const res = await apiProjectEdit(
          this.projectForm
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('修改项目信息成功')
        this.onClose()
        this.$emit('success')
      } catch (e) {
        this.saveLoading = false
      }
    },
    handleClick(tab) {
      this.tabList.forEach((v) => {
        if (v.name == tab.name) {
          v.active = true
        } else {
          v.active = false
        }
      })
    }
  }

}
</script>

<style lang='scss' scoped>
:deep() {
  .el-dialog .el-dialog__header {
	border-bottom: none;
  height: 45px;
  }

}

.vone-border-tabs {
	:deep(	.el-tabs--card>.el-tabs__header .el-tabs__nav) {
		border: none;
	}
  :deep(.el-tabs--card>.el-tabs__header) {
    margin: 0px -16px 16px;
  }
	:deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child) {
		border-left: 1px solid #EAECF0;
		margin-left: 16px
	}
	:deep(	.el-tabs__item) {
		margin: 0px 2px;
		border: 1px solid #EAECF0;
		border-radius: 2px 2px 0px 0px;
		height: 32px;
		line-height: 32px;
		// padding:0px 12px
	}
	:deep(.el-tabs__item:not(.is-active)) {
		background: #F2F3F5;
		color: #838A99;
	}
	:deep(.is-active) {
		color: #1964FF
	}
}
// :deep(.el-col-16) {
//   border-right: 1px solid var(--solid-border-color);
// }

</style>
