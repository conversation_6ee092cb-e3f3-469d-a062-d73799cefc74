<template>
  <page-wrapper>
    <div>
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="project-report-table"
            v-model:model="formData"
            :table-ref="$refs['project-report-table']"
            :project-id="$route.params.id"
            show-basic
            v-model:extra="extraData"
            v-model:default-fileds="defaultFileds"
            @getTableData="getPlanReports"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="交付物目标" prop="name">
                  <el-input v-model.trim="formData.name" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-select v-model="formData.type" clearable style="width:100%">
                    <el-option label="链接" value="LINK" />
                    <el-option label="文件" value="FILE" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="formData.type === 'FILE'" :span="12">
                <el-form-item label="文件名称" prop="fileName">
                  <el-input v-model.trim="formData.fileName" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col v-if="formData.type === 'LINK'" :span="12">
                <el-form-item label="链接名称" prop="link">
                  <el-input v-model.trim="formData.link" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
          </vone-search-dynamic>
        </template>
        <template slot="fliter">
          <vone-search-filter
            v-model:extra="extraData"
            v-model:model="formData"
            v-model:default-fileds="defaultFileds"
            @getTableData="getPlanReports"
          />
        </template>
      </vone-search-wrapper>
      <main :style="{height:tableHeight}">
        <vxe-table
          ref="project-report-table"
          class="vone-vxe-table"
          border
          height="auto"
          resizable
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth:'120px' }"
          row-id="id"
        >
          <vxe-column title="交付物名称" field="name">
            <template #default="{ row }">
              {{ row.name }}
            </template>
          </vxe-column>
          <vxe-column title="类型" field="type" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 'FILE'">文件</span>
              <span v-if="scope.row.type == 'LINK'">链接</span>
            </template>
          </vxe-column>
          <vxe-column title="交付物" field="fileName">
            <template slot-scope="scope">
              <a v-if="showOffice(scope.row)" @click="getOffice(scope.row)">
                {{ scope.row.fileName }}
              </a>
              <a v-else @click="fileClick(scope.row)">
                {{ scope.row.type == 'FILE'? scope.row.fileName : scope.row.link }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="文件大小" field="fileSize" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 'FILE'">{{ scope.row.fileSize | fileSize }}</span>
              <span v-if="scope.row.type == 'LINK'">--</span>
            </template>
          </vxe-column>
          <vxe-column title="交付时间" field="deliveryTime" width="150">
            <template slot-scope="scope">
              {{ scope.row.deliveryTime | formatVal }}
            </template>
          </vxe-column>
          <vxe-column title="交付者" field="deliverer" width="150">
            <template slot-scope="scope">
              <!-- <vone-user-avatar v-if="scope.row.echoMap.deliverer" :avatar-path="scope.row.echoMap.deliverer.avatarPath" :name="scope.row.echoMap.deliverer.name" width="20px" height="20px" /> -->
              <vone-user-avatar v-if="scope.row.echoMap.deliverer" :avatar-path="scope.row.echoMap.deliverer.avatarPath" :name="scope.row.echoMap.deliverer.name" width="20px" height="20px" />
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column title="所属里程碑" field="id" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.echoMap&&scope.row.echoMap.planId" class="name file-name" @click="gomilestone">{{ scope.row.echoMap.planId.name }}</span>
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" align="left" width="100">
            <template #default="{ row }">
              <template>
                <el-tooltip v-if="row.type == 'FILE'" class="item" content="下载" placement="top">
                  <el-button type="text" icon="iconfont el-icon-edit-download" @click="fileClick(row)" />
                </el-tooltip>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </main>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getPlanReports" />
    </div>
    <v-only-office ref="office" v-model:visible="visible" :option="option" />
  </page-wrapper>
</template>

<script>
import VOnlyOffice from '@/components/OnlyOffice'
import dayjs from 'dayjs'
import { getDeliverablesListPage } from '@/api/vone/project/deliverables'
import { apiBaseFileLoadById } from '@/api/vone/base/file'
import { download, fileFormatSize } from '@/utils'
import _ from 'lodash'

export default {
  components: {
    VOnlyOffice
  },
  filters: {
    formatVal(val) {
      if (!val) return '--'
      return dayjs(val).format('YYYY-MM-DD')
    },
    fileSize(val) {
      if (!val) return '--'
      return fileFormatSize(val)
    }
  },
  data() {
    return {
      formData: {
        projectId: this.$route.params.id
      },
      pageLoading: false,
      tableData: { records: [], total: 0 },
      tableOptions: {
      },
      // 参考vabOnlyOffice组件参数配置
      option: {
        url: '',
        isEdit: '',
        fileType: '',
        title: '',
        lang: '',
        isPrint: '',
        user: { id: null, name: '' }
      },
      visible: false,
      typeList: [],
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '交付物目标',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入'
        },
        {
          key: 'type',
          name: '类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择',
          optionList: [
            {
              code: 'LINK',
              name: '链接',
              id: 'LINK'
            },
            {
              code: 'FILE',
              name: '文件',
              id: 'FILE'
            }
          ]
        },
        {
          key: 'fileName',
          name: '文件名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入'
        },
        {
          key: 'link',
          name: '链接名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入'
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      const height = this.extraData?.height + 'px' || '0px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    // this.getPlanReports()
  },
  methods: {
    gomilestone() {
      this.$router.push({
        name: 'project_milestone',
        projectTypeCode: this.$route.params.projectTypeCode,
        id: this.$route.params.id
      })
    },
    async fileClick(row) {
      if (row.type == 'LINK') {
        const link = row.link

        if (link && (_.startsWith(link, 'http') || _.startsWith(link, 'https'))) {
          window.open(row.link, '_blank')
        } else {
          window.open('http://' + row.link, '_blank')
        }
      } else if (row.type == 'FILE') {
        try {
          download(row.fileName, await apiBaseFileLoadById([row.fileId]))
          this.tableLoading = false
        } catch (e) {
          this.tableLoading = false
          return
        }
      }
    },
    // 查询交付物列表
    async getPlanReports() {
      try {
        this.pageLoading = true
        let params = {}

        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
        this.$set(this.formData, 'projectId', this.$route.params.id)
        params = {
          ...tableAttr,
          extra: {
            ...this.extraData
          },
          model: { ...this.formData }
        }

        const res = await getDeliverablesListPage(params)

        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.tableData = res.data
      } catch (e) {
        this.pageLoading = false
      }
    },
    showOffice(row) {
      const type = row.fileName && row.fileName.split('.')[row.fileName.split('.').length - 1]
      if (this.$refs.office.getFileType(type) !== '') {
        return true
      }
    },
    async getOffice(row) {
      const type = row.fileName && row.fileName.split('.')[row.fileName.split('.').length - 1]
      if (this.$refs.office.getFileType(type) !== '') {
        this.visible = true
        await this.$nextTick(() => {
          this.option = {
            id: row.fileId,
            title: row.fileName,
            fileType: type
          }
        })
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.file-name {
  width: 100%;
  color: #3E7BFA;
  cursor:pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.table-operation-view) {
  height: 40px;
  line-height: 40px;
  .table-search {
    min-height: 24px;
    line-height: 24px;
  }
}
</style>
