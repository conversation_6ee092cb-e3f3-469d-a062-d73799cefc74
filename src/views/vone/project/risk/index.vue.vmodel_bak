<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="risk_table"
          v-model:model="formData"
          show-basic
          :show-column-sort="true"
          v-model:extra="extraData"
          :table-ref="$refs['risk_table']"
          :hide-columns="tableOptions.hideColumns"
          v-model:default-fileds="defaultFileds"
          @getTableData="getInitTableData"
          @showPopovers="showPopovers"
          @onTypeChange="onTypeChange"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <span v-if="createSimple">
            <simpleAddIssue
              v-if="createSimple"
              :type-code="'RISK'"
              :biz-type="'RISK_FILE_UPLOAD'"
              @success="getInitTableData"
              @cancel="createSimple = false"
            />
          </span>

          <div>
            <el-button-group class="ml-16">
              <el-tooltip content="快速新增" placement="top">
                <el-button
                  :disabled="!$permission('project_risk_add')"
                  class="subBtton"
                  :icon="`iconfont  ${
                    createSimple ? 'el-icon-direction-double-left' : 'el-icon-direction-double-down'
                  }`"
                  type="primary"
                  @click.stop="createSimple = !createSimple"
                />
              </el-tooltip>
              <el-button
                icon="iconfont el-icon-tips-plus-circle"
                type="primary"
                :disabled="!$permission('project_risk_add')"
                @click.stop="addRiskData"
                >新增</el-button
              >
            </el-button-group>
            <el-dropdown trigger="click" @command="e => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more" /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in actions"
                  :key="index"
                  :icon="item.icon"
                  :command="item.fn"
                  :disabled="item.disabled"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <main style="height: calc(100vh - 185rem)">
      <vxe-table
        ref="risk_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @resizable-change="({ column }) => resizableChangeEvent(column, 'risk_table')"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          min-width="480"
          class-name="custom-title-style"
          show-overflow="ellipsis"
          fixed="left"
        >
          <template #default="{ row }">
            <el-tooltip
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{ color: `${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}` }"
                />
                <span class="custom-title-style-text">{{ row.code + ' ' + row.name }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' 0',
                right: '10px',
                display: copyRow && copyRow.id == row.id ? 'block' : ''
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="e => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown" class="custom-title-copy-dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" command="title">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" command="code">
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="status" width="110">
          <template #default="{ row, rowIndex }">
            <risktatus
              v-if="row"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('project_risk_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column title="提出人" field="putBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.putBy"
                :project-id="projectId"
                class="remoteuser"
                :default-data="[row.echoMap.putBy]"
                :disabled="!$permission('project_risk_edit')"
                @change="workitemChange(row, $event, 'putBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                :project-id="projectId"
                class="remoteuser"
                :default-data="[row.echoMap.handleBy]"
                :disabled="!$permission('project_risk_edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="leadingBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :project-id="projectId"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('project_risk_edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="风险等级" field="priorityCode" width="120">
          <template #default="{ row }">
            <vone-icon-select
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('project_risk_priority_update')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column title="开始时间" field="startTime" width="120">
          <template #default="{ row }">
            <span v-if="row.startTime">
              {{ dayjs(row.startTime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.startTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="关闭时间" field="endTime" width="120">
          <template #default="{ row }">
            <span v-if="row.endTime">
              {{ dayjs(row.endTime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.endTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="center" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('project_risk_edit')"
                icon="iconfont el-icon-application-edit"
                @click="editRiskData(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('project_risk_del')"
                icon="iconfont el-icon-application-delete"
                @click="rowDelete(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e(row)">
              <el-button type="text" icon="iconfont el-icon-application-more" class="operation-dropdown" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="iconfont el-icon-edit-character-b" :command="() => titleCopy(row, 'code')">
                  <span>复制编号</span>
                </el-dropdown-item>
                <el-dropdown-item
                  icon="iconfont el-icon-application-copy-content"
                  :command="() => titleCopy(row, 'title')"
                >
                  <span>复制标题</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('project_risk_add')"
                  icon="iconfont el-icon-icon-fuzhi"
                  :command="() => workItemCopy(row)"
                >
                  <span>复制工作项</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('project_risk_edit')"
                  icon="iconfont el-icon-application-type"
                  :command="() => typeCodeChangeFn(row)"
                >
                  <span>变更类型</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />

    <!-- 新增风险 -->
    <vone-custom-add
      v-if="riskAddParam.visible"
      :key="riskAddParam.key"
      v-model:visible="riskAddParam.visible"
      v-bind="riskAddParam"
      :type-code="'RISK'"
      :title="'新增风险'"
      @success="getInitTableData"
    />

    <!-- 编辑风险 -->
    <vone-custom-edit
      v-if="riskParam.visible"
      :key="riskParam.key"
      v-model:visible="riskParam.visible"
      v-bind="riskParam"
      :title="riskParam.title"
      :type-code="'RISK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 风险详情 -->
    <vone-custom-info
      v-if="riskInfoParam.visible"
      :key="riskInfoParam.key"
      v-model:visible="riskInfoParam.visible"
      v-bind="riskInfoParam"
      :type-code="'RISK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 导入用户 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model:visible="importParam.visible"
      @success="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model:visible="editAllParam.visible"
      :type-code="'RISK'"
      @success="getInitTableData"
    />
    <!-- 变更工作项类型 -->
    <type-code-change
      v-if="typeCodeChangeParam.visible"
      v-bind="typeCodeChangeParam"
      v-model:visible="typeCodeChangeParam.visible"
      @success="getInitTableData"
    />
  </page-wrapper>
</template>

<script>
import risktatus from '@/views/vone/project/common/change-status/index.vue'

import { getProjectRiskList, delRiskData } from '@/api/vone/project/risk'

import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'

import simpleAddIssue from '@/views/vone/project/issue/function/simple-add-issue.vue'
import { apiAlmRiskInfo } from '@/api/vone/project/risk'

import { catchErr } from '@/utils'
// apiDelProgramPage
import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'

import editAll from '../common/edit-all'

import typeCodeChange from '@/components/CustomEdit/components/type-code-change'
import { editById, getWorkItemState } from '@/api/vone/project/index'

import { queryFieldList } from '@/api/common'
import { apiAlmSourceNoPage, requirementListByCondition } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    risktatus,
    simpleAddIssue,
    typeCodeChange,
    editAll
  },
  mixins: [setDataMixin],
  props: {},
  data() {
    return {
      extraData: {},
      defaultFileds: [],
      formData: {
        projectId: [this.$route.params.id]
      },
      createSimple: false,
      tableData: {},
      tableLoading: false,
      riskParam: {
        visible: false
      },
      tableOptions: {
        hideColumns: [
          'files',
          'code',
          'description',
          'delay',
          'estimatePoint',
          'planStime',
          'projectId',
          'ideaId',
          'sourceCode',
          'typeCode',
          'putBy',
          'leadingBy'
        ] // 默认隐藏列
      },
      actions: [
        {
          name: '批量删除',

          fn: this.deleteAll,
          disabled: !this.$permission('project_risk_del')
        },
        {
          name: '批量编辑',

          fn: this.editAll,
          disabled: !this.$permission('project_risk_edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('project_risk_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('project_risk_export')
        }
      ],
      selecteTableData: [],
      typeCodeList: [],
      prioritList: [],
      stateCodeList: [],
      importParam: { visible: false }, // 用户导入

      exportLoading: false,
      riskAddParam: { visible: false },
      riskInfoParam: { visible: false }, // 详情
      editAllParam: { visible: false }, // 批量编辑
      tableList: [], // 用于编辑时切换上一个下一个
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '变更记录',
          name: 'activityRecord'
        }
      ],
      leftTabs: [
        // {
        //   label: '用户需求',
        //   name: 'RiskToIdea'
        // },
        {
          label: '需求',
          name: 'RiskToIssue'
        },
        {
          label: '关联任务',
          name: 'RiskToTask'
        },
        {
          label: '关联缺陷',
          name: 'RiskToBug'
        }
      ],
      headerList: [], // 动态列头
      columnsList: [],
      filterList: [], // 筛选条件
      total: undefined,
      exportParam: { visible: false }, // 导出
      showTag: false,
      typeCodeChangeParam: {
        visible: false
      },
      copyRow: null,
      projectId: this.$route.params.id
    }
  },
  created() {
    console.log('6666')

    this.getQueryFieldList()
    const params = this.$route.params
    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }

    // 领导工作台跳转过来
    const { showDialog, queryId } = this.$route.query
    if (showDialog && queryId) {
      this.showInfo({ id: queryId })
    }
  },
  mounted() {
    this.getPrioritList()
  },
  methods: {
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    typeCodeChangeFn(e) {
      this.typeCodeChangeParam = {
        visible: true,
        dataId: e.id,
        typeClassfiy: 'RISK'
      }
    },
    workItemCopy(e) {
      this.createSimple = false
      this.riskAddParam = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: e.typeCode,
        title: '复制风险',
        id: e.id
      }
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('risk', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    showPopovers() {
      this.showTag = true
      this.getIssueType()
      this.getAllStatus()
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '项目-风险',
        url: `/api/alm/alm/risk/downloadImportTemplate`,
        importUrl: `/api/alm/alm/risk/import?projectId=${this.$route.params.id}`
      }
    },

    // 导出
    async exportFlie() {
      try {
        this.exportLoading = true
        download(`项目风险信息.xls`, await apiBaseFileLoad('/api/alm/alm/risk/export', this.formData))

        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
        return
      }
    },
    // 更新任务状态
    async editRowStatus(row, index) {
      const [res, err] = await catchErr(apiAlmRiskInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(res.data, 'tag', res.data.echoMap.tagId ? res.data.echoMap.tagId.map(r => r.name) : [])
      }
      this.tableData.records.splice(index, 1, res.data)
    },

    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this
      const message = type == 'code' ? row.code : row.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    addRiskData() {
      this.riskAddParam = {
        visible: true,
        title: '新增风险',
        key: Date.now(),
        infoDisabled: false
      }
    },
    editRiskData(val) {
      this.riskParam = {
        visible: true,
        title: '编辑风险',
        id: val.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: val.typeCode,
        stateCode: val.stateCode
      }
    },
    showInfo(val) {
      this.riskInfoParam = {
        visible: true,
        title: '风险详情',
        id: val.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: val.typeCode,
        stateCode: val.stateCode
      }
    },
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs.risk_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs.risk_table.getCheckboxRecords()
    },

    // 查询表单数据
    async getInitTableData() {
      this.tableLoading = true

      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      if (this.$route.params.id) {
        params.model.projectId = [this.$route.params.id]
      }
      if (this.formData.createTime && this.formData.createTime.length > 0) {
        params.model.createTime = {
          start: this.formData.createTime[0],
          end: this.formData.createTime[1]
        }
      }
      const res = await getProjectRiskList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      res.data.records.forEach(element => {
        element.tag =
          element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
            ? element.echoMap.tagId.map(r => r.name)
            : []
      })

      this.tableData = res.data
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
    },

    async rowDelete(data) {
      await this.$confirm(`确定删除【${data.name}】吗`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
      const res = await delRiskData([data.id])

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量删除
    async deleteAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除当前数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        this.tableLoading = true
        const selectId = this.selecteTableData.map(r => r.id)
        const res = await delRiskData(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getInitTableData()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 批量编辑
    editAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selecteTableData }
    },
    async getQueryFieldList(typeCodes) {
      const fixedField = ['name', 'handleBy', 'stateCode', 'tagId', 'createTime', 'typeCode']
      const form = {
        projectId: this.$route.params.id,
        typeClassify: 'RISK',
        typeCodes: typeCodes || []
      }
      const res = await queryFieldList(form)
      if (!res.isSuccess) {
        return
      }
      const vId = ['productId']
      const filter = res.data.filter(r => r.isSearch && r.key != 'projectId')
      filter.forEach(element => {
        element.isBasicFilter = !fixedField.includes(element.key)
        element.multiple = element.type.code != 'ICON'

        element.valueType = vId.includes(element.key) ? 'id' : null
      })
      this.defaultFileds = filter
      console.log(
        this.defaultFileds.map(r => r.name),
        '--'
      )
      this.getOptions()
    },
    getOptions() {
      this.getPrioritList()
      this.getIssueType()
      this.getAllStatus()
      this.getDaley()
      this.getsourceCode()
      this.productList()
      this.getplanId()
    },
    getDaley() {
      const delay = [
        { name: '是', code: true },
        { name: '否', code: false }
      ]
      this.setData(this.defaultFileds, 'delay', delay)
    },
    // 查询来源
    async getsourceCode() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'RISK'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'sourceCode', res.data)
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productId', res.data)
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'RISK'
      })
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },
    // 查询需求类型
    async getIssueType() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, 'RISK')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },
    // 迭代计划
    async getplanId() {
      // if (this.maps['planId'].length > 0) return
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'planId', res.data)
    },
    // 查项目下需求
    async getRequirementList() {
      // if (this.maps['requirementId']?.length > 0) return
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'requirementId', res.data)
    },
    onTypeChange(item) {
      this.getQueryFieldList(item)
    }
  }
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
