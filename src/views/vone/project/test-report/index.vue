<!-- 测试报告table -->
<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="actions">
        <el-button
          type="primary"
          icon="iconfont el-icon-tips-plus-circle"
          :disabled="!$permission('project_testm_new_report')"
          @click="openAddDialog"
        >生成报告</el-button>
      </template>
    </vone-search-wrapper>
    <main style="height:calc(100vh - 205rem);">
      <vxe-table
        ref="project-report-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="报告名称" field="testReportName">
          <template #default="{ row }">
            <span>{{ row.testReportName }}</span>
            <span v-if="row.createTime">_{{ row.createTime | formatVal }}</span>
          </template>
        </vxe-column>
        <vxe-column width="250" title="测试计划" field="plan">
          <template #default="{ row }">
            <span v-if="row.planId && row.echoMap">
              <span>{{ row.echoMap.name }} </span>
            </span>
            <span v-else> -- </span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="170" sortable />
        <vxe-column title="创建人" field="createdBy" width="160">
          <template #default="{ row }">
            <template v-if="row.createdBy && row.echoMap && row.echoMap.createBy">
              <vone-user-avatar :avatar-path="row.echoMap.createBy.avatarPath" :name="row.echoMap.createBy.name" />
            </template>
            <span v-else> -- </span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <el-tooltip class="item" content="查看" placement="top">
              <el-button type="text" :disabled="!$permission('project_testm_report_detail')" icon="iconfont el-icon-edit-visible" @click="visiteDetail(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button type="text" :disabled="!$permission('project_testm_report_del')" icon="iconfont el-icon-application-delete" @click="delRow(row)" />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getPlanReports" />

    <el-dialog title="新增报告" v-model="addVisible" width="30%" :close-on-click-modal="false">
      <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules">
        <el-form-item label="测试计划" prop="planId">
          <el-select v-model="form.planId" clearable filterable>
            <el-option v-for="item in planIdList" :key="item.key" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="报告名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 报告详情 -->
    <reportDetail v-model="detailVisible" :report-data="detailData" @delSuccess="getPlanReports" />

  </page-wrapper>
</template>

<script>
import * as dayjs from 'dayjs'
import storage from 'store'

import { projectGetPageTestReport } from '@/api/vone/project/test'
import { getProjectPlans } from '@/api/vone/testmanage/case'
import { createReport, deleteReport } from '@/api/vone/testmanage/case'

import reportDetail from './reportDetail.vue'

export default {
  components: {
    reportDetail
  },
  filters: {
    formatVal(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD')
    }
  },
  data() {
    return {
      formData: {
        projectId: this.$route.params.id
      },
      pageLoading: false,
      formLoading: false,
      planIdList: [], // 测试计划
      tableData: { records: [], total: 0 },
      addVisible: false, // 新增测试报告
      detailVisible: false, // 报告详情
      detailData: {}, // 查看报告数据
      form: {
        name: ''
      },
      rules: {
        planId: [{ required: true, message: '请选择测试计划', trigger: 'change' }],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      }

    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    }
  },
  mounted() {
    this.getPlanReports()
  },
  methods: {
    // 查询测试计划
    async getTestPlan() {
      this.formLoading = true
      const res = await getProjectPlans({
        projectId: this.$route.params.id
      })
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.planIdList = res.data
    },
    // 查询报告列表
    async getPlanReports() {
      try {
        this.pageLoading = true

        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
        const params = {
          ...tableAttr,
          extra: {},
          model: { ...this.formData }
        }

        const res = await projectGetPageTestReport(params)

        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.tableData = res.data
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 查看报告
    visiteDetail(row) {
      this.detailVisible = true
      this.detailData = row
    },
    // 删除报告
    async delRow(row) {
      try {
        await this.$confirm('确认删除当前项？', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        })
        const res = await deleteReport([row.id])
        if (res.isSuccess) {
          this.$message.success('删除成功')
          this.getPlanReports()
        } else {
          this.$message.error(res.msg)
        }
      } catch (e) {
        if (e === 'cancel') return
      }
    },
    openAddDialog() {
      this.getTestPlan()
      this.addVisible = true
    },
    // 确认新增测试报告
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }

      const params = {
        createdBy: this.loginUser.id,
        testReportName: this.form.name,
        planId: this.form.planId,
        projectId: this.$route.params.id
      }
      const res = await createReport(params)
      if (res.isSuccess) {
        this.$message.success('创建成功')
        this.addVisible = false
        this.getPlanReports()
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.operation-icon-main {
  display: flex;
  align-items: center;
  .el-button {
    padding: 0px;
    height: unset;
    line-height: unset;
    min-width: unset;
    font-size: 16px;
  }
}

</style>

