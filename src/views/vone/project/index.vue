<template>
  <div>
    <div class="pageContentNoH" style="margin-bottom: 10px">
      <div class="title-header">根据模板新建</div>
      <el-row type="flex">
        <div v-for="item in templeteList" :key="item.id" style="margin-right: 16px">
          <a @click="fastAdd(item.code)">
            <div :class="item.code" class="cardBox" :style="{ width: item.code == 'DEVOPS' ? '180px' : '160px' }">
              <span class="flexCard">
                <svg class="icon" aria-hidden="true">
                  <use :xlink:href="'#' + `${item.icon}`" />
                </svg>
                <div class="itemName">{{ item.name }}</div>
              </span>
              <div class="itemBtn">创建</div>
            </div>
          </a>
        </div>
      </el-row>
    </div>
    <div class="search-layout">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="project-table"
            v-model:model="formData"
            v-model:extra="extraData"
            v-model:default-fileds="defaultFileds"
            show-basic
            :default-filter-collection="true"
            @getTableData="getProjectData"
          />
        </template>
        <template slot="actions">
          <el-button
            icon="iconfont el-icon-tips-plus-circle"
            type="primary"
            :disabled="!$permission('project_import_pm')"
            @click="addBatchUserToProject"
            >一键添加项目经理</el-button
          >
          <el-button
            icon="iconfont el-icon-tips-plus-circle"
            type="primary"
            :disabled="!$permission('alm:projectInfo:add')"
            @click="addNewProject"
            >新增</el-button
          >
        </template>
        <template slot="fliter">
          <vone-search-filter
            v-model:extra="extraData"
            v-model:model="formData"
            v-model:default-fileds="defaultFileds"
            :style="{ padding: Object.keys(formData).length > 0 ? '10px 16px' : '0 0' }"
            @getTableData="getProjectData"
          />
        </template>
      </vone-search-wrapper>
    </div>
    <div class="project-layout">
      <div
        v-for="card in projectLayout"
        :key="card.key"
        class="delivery-layout common-css"
        :style="{ height: cardHeight('200px') }"
      >
        <div class="title">{{ card.label }}</div>
        <div
          v-loading="card.key == 'DELIVERY' ? DELIVERYLoading : DEVELOPLoading"
          class="project-cards"
          :style="{ height: cardHeight('268px') }"
        >
          <vone-cards :ref="card.ref" :data="card.tableData" :row-count="2" @updateData="getProjectData(card.key)">
            <template slot-scope="{ row }">
              <a @click="itemProject(row.code, row.typeCode, row.id, row.name, row)">
                <vone-card :title="row.name" :actions="rowActions" :class="row.top ? 'vCard' : ''" :actions-num="4">
                  <template slot="icon">
                    <svg v-if="row.typeCode == 'AGILE'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon-minjie1" />
                    </svg>
                    <svg v-else-if="row.typeCode == 'WALL'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon-pubu1" />
                    </svg>
                    <svg v-else-if="row.typeCode == 'TEST'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-ceshixiangmu" />
                    </svg>
                    <svg v-else-if="row.typeCode == 'DEVOPS'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-project-devops" />
                    </svg>
                    <svg v-else-if="row.typeCode == 'CAR'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-project-car-model" />
                    </svg>
                    <svg v-else-if="row.typeCode == 'PLATFORM'" class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-project-platform" />
                    </svg>
                  </template>

                  <template slot="title">
                    <el-row type="flex" justify="space-between">
                      <div class="title-box">
                        <el-tooltip v-if="row.isShow" class="item" :content="row.name" placement="top">
                          <div class="title">{{ row.name }}</div>
                        </el-tooltip>
                        <div v-else class="title" @mouseenter="e => isShowToltip(e, row)" @mouseout="hideTip(row)">
                          {{ row.name }}
                        </div>
                        <span>{{ row.key }}</span>
                      </div>
                      <div @click.stop="focus(row)">
                        <i
                          v-if="row.collection"
                          style="color: #ffc642; font-size: 18px"
                          class="iconfont el-icon-icon-shoucang-on"
                        />
                        <i v-else class="el-icon-star-off" style="font-size: 18px" />
                      </div>
                    </el-row>
                  </template>
                  <template v-slot:tagboxs>
                    <el-tag v-if="row.echoMap.projectProcess && row.echoMap.projectProcess.status" type="success">{{
                      row.echoMap.projectProcess.status
                    }}</el-tag>
                  </template>
                  <div slot="desc">
                    <vone-user-avatar
                      :avatar-path="getUserInfo(row) ? getUserInfo(row).avatarPath : ''"
                      :name="getUserInfo(row) ? getUserInfo(row).name : ''"
                    />
                  </div>
                  <el-row class="descbox" flex>
                    <vone-toolitip :content="row.code" :label="'标识'" />
                    <!-- <vone-toolitip :content="row.echoMap && row.echoMap.hostProduct ? row.echoMap.hostProduct.name : ''" :label="row.classify == 'DELIVERY' ? '交付产品' : '研发产品'" /> -->

                    <vone-toolitip :content="row.description" :label="'描述'" />
                  </el-row>
                </vone-card>
              </a>
            </template>
          </vone-cards>
        </div>
      </div>
    </div>
    <!-- 新增项目对话框 -->
    <newProjectDialog
      v-if="projectParam.visible"
      v-bind="projectParam"
      v-model="projectParam.visible"
      @success="getProjectData"
    />
    <el-dialog title="添加项目经理" :modal="false" v-model="dialogVisible" append-to-body width="40%">
      <el-form ref="form" :rules="rules" :model="form" label-position="top">
        <el-col :span="12">
          <el-form-item label="项目经理" prop="userId">
            <vone-remote-user v-model="form.userId" />
          </el-form-item>
        </el-col>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import { cloneDeep } from 'lodash'

import {
  getAllProject,
  apiProjectDel,
  apiProjectAuth,
  addProjectAndStartFlow,
  getAllProjectType,
  closeProject,
  collectProject,
  topProject,
  batchUserToProject
} from '@/api/vone/project/index'
import { getPermission, setPermission, getRouter } from '@/utils/auth'
import newProjectDialog from './components/newProjectDialog.vue'
// import myTask from './my-task/index'
import storage from 'store'
import { textRange } from '@/utils'
export default {
  components: {
    newProjectDialog
    // myTask
  },
  filters: {
    format(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD')
    }
  },
  data() {
    return {
      dialogLoading: false,
      dialogVisible: false,
      form: {
        userId: ''
      },
      rules: {
        userId: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        },
        {
          key: 'collection',
          name: '我收藏的',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择',
          optionList: [
            {
              code: true,
              name: '是',
              id: '1'
            },
            {
              code: false,
              name: '否',
              id: '2'
            }
          ]
        }
      ],
      rowActions: [
        // {
        //   type: 'text',
        //   text: '结项申请',
        //   hidden: ({ row }) => row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
        //   onClick: ({ row }) => this.endProject(row),
        //   icon: 'iconfont el-icon-application-project-end'
        // },
        {
          type: 'text',
          text: '修改',
          hidden: ({ row }) => row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editProject(row),
          disabled: !this.$permission('project_edit')
        },
        {
          type: 'text',
          text: '置顶',
          hidden: ({ row }) => row.top,
          onClick: ({ row }) => this.setProjectTop(row),
          icon: 'iconfont el-icon-application-top-back'
        },
        {
          type: 'text',
          text: '取消置顶',
          hidden: ({ row }) => !row.top,
          onClick: ({ row }) => this.setProjectTop(row),
          icon: 'iconfont el-icon-application-untop-back'
        },
        {
          type: 'text',
          text: '删除',
          hidden: ({ row }) => row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.projectDelete(row),
          disabled: !this.$permission('project_del')
        }
      ],
      templeteList: [],
      tableData: {},
      projectParam: { visible: false },
      formData: {}, // 筛选条件
      extraData: {}, // 筛选条件
      DELIVERYLoading: false,
      DEVELOPLoading: false,
      projectLayout: [
        {
          label: '交付类项目',
          key: 'DELIVERY',
          ref: 'deliveryCard',
          tableData: {}
        },
        {
          label: '研发类项目',
          key: 'DEVELOP',
          ref: 'developCard',
          tableData: {}
        }
      ]
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
    cardHeight() {
      return function (cardPx) {
        const height = (this.extraData?.height || 0) + 'px'
        return `calc(100vh - ${cardPx} - ${height})`
      }
    }
  },
  watch: {
    $route: function () {
      this.getProjectData()
    }
  },
  mounted() {
    this.getTemplate()
  },
  methods: {
    isShowToltip(e, node) {
      const bool = textRange(e.target)
      this.$set(node, 'isShow', bool)
    },
    hideTip(node) {
      this.$set(node, 'isShow', false)
    },
    // 获取项目模版
    async getTemplate() {
      const res = await getAllProjectType()
      if (!res.isSuccess) {
        return
      }
      this.templeteList = res.data
    },
    // 查询项目列表数据
    getProjectData(type) {
      if (type != 'DELIVERY' && type != 'DEVELOP') {
        this.getProjectList()
        setTimeout(() => {
          this.getDevelopProjectList()
        }, 100)
      } else {
        type == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
      }
    },
    // 查询项目列表-交付类
    async getProjectList() {
      this.DELIVERYLoading = true
      let params = {}
      const pageObj = this.$refs?.deliveryCard[0]?.exportTableQueryData() || {}
      params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData, classify: 'DELIVERY' }
      }
      const { isSuccess, data, msg } = await getAllProject(params)
      this.DELIVERYLoading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      data.records.forEach(element => {
        element.pStage = element.projectStage?.code || 'RUNNING'
      })
      this.projectLayout[0].tableData = data
      this.tableData = data
    },
    async getDevelopProjectList() {
      this.DEVELOPLoading = true
      let params = {}
      const pageObj = this.$refs?.developCard[0]?.exportTableQueryData() || {}
      params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData, classify: 'DEVELOP' }
      }
      const { isSuccess, data, msg } = await getAllProject(params)
      this.DEVELOPLoading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      data.records.forEach(element => {
        element.pStage = element.projectStage?.code || 'RUNNING'
      })
      this.projectLayout[1].tableData = data
      this.tableData = data
    },
    async projectDelete(item) {
      try {
        await this.$confirm(`确定删除【${item.name}】吗`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        const { isSuccess, msg } = await apiProjectDel([item.id])
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }
        this.$message.success('删除项目成功')
        item.classify == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
      } catch (error) {
        //
      }
    },
    async itemProject(code, typeCode, id, name, row) {
      const arry = ['APPROVAL', 'CLOSED']
      if (row.projectStage && arry.indexOf(row.projectStage.code) > -1) {
        return this.$message.warning(` 【${row.projectStage.desc}】流程审批中... `)
      }
      const { data, isSuccess, msg } = await apiProjectAuth(id)
      const jumpRouter = cloneDeep(data)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      if (!isSuccess) {
        return this.$message('获取用户权限失败，请重新登录')
      }
      if (!data.length) {
        this.$message.warning('当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权')
        return
      }
      // -------------------------------------------------------------------------------
      // 处理按钮权限
      const hasPermissionList = [] // 接口返回的用来接收新的按钮权限数组
      var findPermission = function (V) {
        V.forEach(item => {
          // 把传入的数组循环遍历
          if (item.meta.isButton === true) {
            hasPermissionList.push(item.meta.code) // item.meta.isButton 为true 把id添加到新数组
          }
          if (item.children) {
            findPermission(item.children) // 递归调用自身
          }
        })
      }
      findPermission(data) // 调用函数

      const permission = getPermission() // 从登录接口获取的权限按钮数据

      const allPermision = [...new Set([...permission, ...hasPermissionList])] // 去重

      setPermission(allPermision)

      // -------------------------------------------------------------------------------

      const routerMenu = cloneDeep(getRouter())
      const projectSettingMenu = cloneDeep(
        routerMenu.find(ele => ele.name == 'project').children.find(ele => ele.name == 'project_view')
      )

      routerMenu.map(item => {
        if (item.name == 'project') {
          item.children = []
          item.children.push(projectSettingMenu)
          item.children = [...item.children, ...data]
        }
      })

      this.$store.commit('user/set_router', routerMenu)

      // 保存路由信息
      this.$store.dispatch('project/itemProject', typeCode)

      const firstMenuName = jumpRouter[0]?.children[0]?.name || jumpRouter[0]?.name
      this.$router.push({
        name: firstMenuName,
        params: { projectKey: code, projectTypeCode: typeCode, id: id, name: name }
      })
    },
    addNewProject() {
      this.projectParam = { visible: true, title: '新增项目' }
    },
    async addBatchUserToProject() {
      this.dialogVisible = true
    },
    async submit() {
      try {
        await this.$refs.form.validate()
        this.dialogLoading = true
        const opt = 'add'
        const userIds = [this.form.userId]
        const { isSuccess, data, msg } = await batchUserToProject(opt, userIds)
        this.dialogLoading = false
        if (isSuccess) {
          this.$message.success('添加成功')
          this.$emit('msg')
          this.dialogVisible = false
        } else {
          this.$message.error(msg)
        }
      } catch (error) {
        // 表单验证失败时的处理
        this.dialogLoading = false
        console.error('Form validation error:', error)
      }
    },
    fastAdd(val) {
      if (!this.$permission('alm:projectInfo:add')) {
        this.$message.warning('当前登录用户【项目角色】创建项目权限不足')
        return
      }
      this.projectParam = { visible: true, title: '新增项目', fastType: val }
    },
    editProject(data) {
      this.projectParam = { visible: true, title: '编辑项目', id: data.id, row: data }
    },
    async endProject(data) {
      try {
        await this.$confirm(`是否确定结项？`, '提示').then(async () => {
          if (data.echoMap && data.echoMap.typeCode && data.echoMap.typeCode.initiation) {
            // 结项流程
            const userInfo = storage.get('user')
            const params = {
              processDefinitionKey: data.echoMap.typeCode.closureProcess,
              startAssignee: userInfo.id,
              variables: {
                assignee: userInfo.id,
                closureProject: {
                  assistProductIds: [],
                  typeName: data.echoMap.typeCode.name,
                  ...data
                },
                formData: {
                  componentUrl: 'vone/project/components/project-flow-info',
                  projectName: data.name
                },
                title: data.name || ''
              }
            }
            const res = await addProjectAndStartFlow(params)
            if (!res.isSuccess) {
              this.$message.error(res.msg)
              return
            }
            this.$message.success('结项申请成功')
            data.classify == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
          } else {
            // 非流程结项
            closeProject({
              id: data.id
            }).then(res => {
              if (res.isSuccess) {
                this.$message.success('结项申请成功')
                data.classify == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        })
      } catch (error) {
        //
      }
    },
    async focus(row) {
      const res = await collectProject(row.id, !row.collection)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('操作成功')
      row.classify == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
    },
    async setProjectTop(row) {
      const res = await topProject(row.id, !row.top)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('操作成功')
      row.classify == 'DELIVERY' ? this.getProjectList() : this.getDevelopProjectList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-layout {
  background: #ffffff;
  :deep(.toolbar) {
    margin: 0;
  }
}
.pageContentNoH {
  .title-header {
    border-left: 4px solid var(--main-theme-color);
    padding-left: 10px;
    color: var(--font-main-color);
    font-weight: 500;
    margin-bottom: 12px;
    line-height: 18px;
  }
  padding: 16px;
}
.mainheight {
  height: calc(100vh - 198px);
  position: relative;
}
.project-layout {
  width: 100%;
  display: flex;
  gap: 0px 10px;
  .common-css {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    .title {
      height: 24px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 500;
      color: var(--font-main-color);
      margin-bottom: 12px;
    }
    .project-cards {
      overflow-y: auto;
      :deep(.pagination) {
        position: static;
      }
    }
  }
  .delivery-layout {
    width: 50%;
  }
  .develop-layout {
    width: 50%;
  }
}
.cardBox {
  width: 160px;
  height: 48px;
  position: relative;
  // box-shadow: var(--main-bg-shadow);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
  border-radius: 4px;
  .flexCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &:hover .itemBtn {
    display: block;
  }
  &:hover::before {
    content: '';
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    svg {
      position: relative;
      z-index: 0;
    }
  }

  .icon {
    font-size: 24px;
    margin-right: 8px;
  }
  .itemName {
    font-size: 16px;
    font-weight: 500;
    color: var(--font-main-color);
  }
  .itemBtn {
    float: right;
    z-index: 999;
    display: none;
    color: #fff;
    background-color: var(--main-theme-color);
    padding: 5px;
    font-size: 12px;
    border-radius: 5px;
  }
}

.AGILE {
  background: linear-gradient(to right, #e3f0ff, #91bbff);
  background-size: 100% 100%;
}

.WALL {
  background: linear-gradient(to right, #fcf5e4, #ffca7a);
  background-size: 100% 100%;
}

.TEST {
  background: linear-gradient(to right, #e6f7ff, #6bc1ff);
  background-size: 100% 100%;
}
.DEVOPS {
  background: linear-gradient(to right, #e3fcf8, #4fd6cd);
  background-size: 100% 100%;
}
.CAR {
  background: linear-gradient(to right, #e6f2ff, #6ba6ff);
  background-size: 100% 100%;
}
.PLATFORM {
  background: linear-gradient(to right, #f5f0ff, #b59eff);
  background-size: 100% 100%;
  // &:hover{
  //   background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0,0.5)), linear-gradient(to right, #F5F0FF, #B59EFF);
  // }
}
.userText {
  height: 35px;
  line-height: 35px;
}
.descbox {
  font-size: 14px;
  color: var(--font-second-color);
}
.vCard {
  position: relative;
  overflow: hidden;
}
.vCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-bottom: 20px solid transparent;
  border-left: 20px solid #09bdbd; /* 根据需要设置颜色 */
}
.flexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: -10px;
  line-height: 36px;
  a {
    padding-left: 20px;
  }
}
</style>
