<template>
  <vone-echarts-card title="缺陷趋势">
    <!-- 项目下缺陷趋势 -->
    <vone-echarts v-if="trendsData" :options="options" />
    <vone-empty v-else style="height:300px;" />

    <div class="statisticsNum">

      <header>最近7天的问题 1 </header>
      <div>
        <a class="wrap" @click="chartClick(trendsData,'created')">
          <span class="c2" />新增的缺陷 <span class="t2">{{ trendsData.alltodo }}</span>
        </a>
        <a class="wrap" @click="chartClick(trendsData,'done')">
          <span class="c1" />修复的缺陷<span class="t1">{{ trendsData.alldone }}</span>
        </a>
        <a class="wrap" @click="chartClick(trendsData,'doing')">
          <span class="c3" />当前的缺陷<span class="t1">{{ trendsData.alldoing }}</span>
        </a>

      </div>

    </div>
    <TrendsDetailDialog v-if="detailParams.visible" v-bind="detailParams" v-model:visible="detailParams.visible" />
  </vone-echarts-card>
</template>

<script>

import TrendsDetailDialog from '../components/trends-dialog.vue'
import { queryViewByTime } from '@/api/vone/product/index'
import _ from 'lodash'
export default {
  components: {
    TrendsDetailDialog
  },
  data() {
    return {
      trendsData: {
      },
      options: {},
      detailParams: { visible: false }
    }
  },
  mounted() {
    this.getProjectOptions()
  },
  methods: {
    async getProjectOptions() {
      const { data, isSuccess, msg } = await queryViewByTime({
        projectId: this.$route.params.id
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.trendsData = data

      this.trendsData.alltodo = _.sum(data?.add)
      this.trendsData.alldone = _.sum(data?.close)
      this.trendsData.alldoing = data?.residue[data.residue.length - 1]
      this.options = {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          }
        },
        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          borderRadius: 5,
          data: [
            {
              name: '剩余',
              icon: 'rect'
            },
            {
              name: '修复',
              icon: 'rect'
            },
            {
              name: '新增',
              icon: 'rect'
            }
          ],
          textStyle: { // 图例文字的样式
            color: '#8A8F99'
          }
        },
        grid: [{
          left: '16px',
          right: '70px',
          bottom: '55%',
          containLabel: true
        },
        {
          top: '50%',
          left: '16px',
          right: '70px',
          bottom: '25%',
          containLabel: true
        }
        ],
        xAxis: [
          {
            data: this.trendsData.date,
            boundaryGap: 0,
            axisLabel: {
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              },
              rotate: -18 // 设置日期显示样式（倾斜度）

            }
          },
          {
            data: this.trendsData.date,
            gridIndex: 1,
            splitLine: {
              show: false // 去掉网格线
            },
            boundaryGap: 0,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              },
              rotate: -18 // 设置日期显示样式（倾斜度）
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            'axisTick': { // y轴刻度线
              'show': false
            },
            splitNumber: 1,
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          },
          {
            splitNumber: 1,
            type: 'value',
            'axisTick': { // y轴刻度线
              'show': false
            },
            gridIndex: 1,
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          }
        ],
        series: [
          {
            showSymbol: false,
            name: '新增的缺陷',
            type: 'line',
            data: this.trendsData.add,
            itemStyle: {
              normal: {
                color: '#58CC94', // 改变折线点的颜色
                lineStyle: {
                  color: '#58CC94' // 改变折线颜色
                }
              }
            }
            // markLine: {
            //   symbol: ['none', 'none'],
            //   data: version,
            //   lineStyle: {
            //     normal: {
            //       color: '#B2B6BF',
            //       width: 1
            //       // 这儿设置安全基线颜色
            //     }
            //   },
            //   label: {
            //     show: true,
            //     position: 'end',
            //     formatter(params) {
            //       return params.data.name + '：' + params.data.version
            //     },
            //     color: '#fff',
            //     height: 18,
            //     padding: [4, 4, 4, 4],
            //     borderWidth: 2,
            //     backgroundColor: 'rgba(50, 50, 50, 0.65)',
            //     borderRadius: 2,
            //     fontWeight: 400,
            //     fontSize: 14,
            //     fontFamily: 'PingFang SC'
            //   }
            // },
            // silent: true // 鼠标悬停事件, true悬停不会出现实线
            // symbol: 'none' // 去掉箭头

          },
          {
            showSymbol: false,
            name: '修复的缺陷',
            type: 'line',
            data: this.trendsData.close,
            itemStyle: {
              normal: {
                color: '#FA6E69', // 改变折线点的颜色
                lineStyle: {
                  color: '#FA6E69' // 改变折线颜色
                }
              }
            }
          },
          {
            showSymbol: false,
            xAxisIndex: 1,
            yAxisIndex: 1,
            name: '当前的缺陷',
            type: 'line',
            data: this.trendsData.residue,
            itemStyle: {
              normal: {
                color: '#5792FF', // 改变折线点的颜色
                ineStyle: {
                  color: '#5792FF' // 改变折线颜色
                }
              }
            }
          }

        ]
      }
    },
    chartClick(params, val) {
      const types = [
        {
          name: '新增的缺陷',
          code: 'created'
        },
        {
          name: '当前的缺陷',
          code: 'doing'
        },
        {
          name: '修复的缺陷',
          code: 'done'
        }
      ]

      const dates = this.trendsData?.date
      this.$set(params, 'start', dates[0])
      this.$set(params, 'end', dates[dates.length - 1])
      this.$set(params, 'activeTab', val)
      this.detailParams = { visible: true, form: params, types: types }
    }

  }

}
</script>
<style lang="scss" scoped>

.statisticsNum{
	position: absolute;
	top:75%;
	left: 16px;
  right:16px;
	bottom:10px;
	background: #F7F8FA;
	border-radius: 4px;
	padding: 10px 16px;
	header{
		font-weight: 500;
		line-height: 22px;
    font-size: 14px;
    color:#1D2129;
	}
	div{
		font-size: 14px;
    line-height: 22px;
    margin-top:12px;
    display: flex;
    justify-content: space-between;
    .wrap {
      display: flex;
      align-items: center;
    }
  .c1{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #FA6E69;
    margin-right:12px;
	}
	.c2{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #58CC94;
    margin-right:12px;
	}
	.c3{
		display: inline-block;
		width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #5792FF;
    margin-right:12px;
	}
	.t1{
    margin-left:4px;
    color:#6B7385;
    font-weight: 500;
	}
	}
}
</style>
