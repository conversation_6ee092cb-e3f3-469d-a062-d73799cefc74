<template>

  <vone-echarts-card :title="title">
    <vone-echarts v-if="data && data.length>0" :options="options" :height="'300px'" @chartClick="chartClick" />
    <vone-empty v-else />
    <ProjectDetailDialog v-if="detailParams.visible" v-bind="detailParams" v-model:visible="detailParams.visible" />
  </vone-echarts-card>

</template>

<script>
import ProjectDetailDialog from '../components/detail-dialog.vue'
export default {
  components: {
    ProjectDetailDialog
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    statusFun: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      detailParams: { visible: false },
      data: [],
      backgroundColor: '#2c343c',
      options: {
        dataZoom: {
          // type: 'inside',
          show: false,
          realtime: true,
          height: 20, // dataZoom的尺寸
          bottom: 10,	// 滚动体距离底部的距离
          // start: 0, // 滑动条宽度开始标度
          // end: 30, // 滑动条宽度结束标度
          maxValueSpan: 20,
          minValueSpan: 5,
          startValue: 0,
          endValue: 20,
          borderColor: 'none',
          backgroundColor: '#f2f3f5',
          dataBackground: {
            lineStyle: {
              color: '#3E7BFA'
            },
            areaStyle: {
              color: '#e0e6f6',
              opacity: 1
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          }
        },
        legend: {
          type: 'scroll',
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          textStyle: { // 图例文字的样式
            color: '#8A8F99'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLabel: {
              show: true,
              interval: 0,
              rotate: 25,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5' // 更改坐标轴颜色
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            'axisTick': { // y轴刻度线
              'show': false
            },
            axisLine: {
              show: false // 不显示坐标轴轴线
            },
            splitLine: {
            // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99' // 更改坐标轴文字颜色
              }
            }
          }

        ],
        series: []
      }
    }
  },
  watch: {
    statusFun: function(val) {
      if (val) {
        val.data?.forEach(item => {
          item.type = 'bar'
          item.stack = 'Ad'
          item.emphasis = {
            focus: 'series'
          }
          item.itemStyle = {
            color: item.color
          }
        })
        this.data = val.data
        if (this.data?.length > 0 && this.data[0].data.length >= 20) {
          this.options.dataZoom.show = true
        } else {
          this.options.dataZoom.show = false
        }
        this.options.series = val.data
        this.options.xAxis[0].data = val.name
      }
    }
  },
  methods: {
    chartClick(params) {
      const stateMap = this.statusFun.data.reduce((r, v) => (r[v.name] = v.code) && r, {})

      // console.log(this.statusFun, 'this.statusFun')

      this.$set(params, 'classify', 'TASK')
      this.$set(params, 'stateCode', stateMap[params.seriesName])
      this.detailParams = { visible: true, form: params }
    }
  }
}
</script>
