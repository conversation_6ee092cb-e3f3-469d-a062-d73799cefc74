<template>
  <vone-echarts-card :title="title">
    <vone-echarts v-if="data.length>0" v-loading="show" :options="options" @chartClick="chartClick" />
    <vone-empty v-else />
    <ProjectDetailDialog v-if="detailParams.visible" v-bind="detailParams" v-model="detailParams.visible" />
  </vone-echarts-card>

</template>

<script>
import ProjectDetailDialog from '../components/detail-dialog.vue'

export default {
  components: {
    ProjectDetailDialog
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: null
    },
    name: {
      type: String,
      default: ''
    },
    taskFun: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      detailParams: { visible: false },
      show: false,
      data: [],
      backgroundColor: '#2c343c',
      options: {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#fff',
          borderWidth: '1',
          borderColor: '#EBEEF5',
          textStyle: {
            color: '#8A8F99'
          }
        },
        legend: {
          orient: 'vertical',
          left: '70%',
          bottom: '5%',
          textStyle: {
            color: '#8A8F99'
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle'
        },
        series: [
          {
            name: '任务概览',
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['50%', '75%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'center',
              formatter: '{num|' + 0 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#8A8F99'
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#8A8F99',
                  lineHeight: 30
                }
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    taskFun(val) {
      if (val.data) {
        val.data.forEach(item => {
          item.itemStyle = {
            color: item.color
          }
        })
      }
      this.options.series[0].data = val.data
      this.options.series[0].label.formatter = '{num|' + val.count + '}' + '\n\r' + '{name|总数}'
      this.data = val.data
    }
  },
  methods: {
    getinfo() {
      this.show = true
      if (this.taskFun) {
        this.show = false
        this.options = {
          tooltip: {
            trigger: 'item',
            backgroundColor: '#fff',
            borderWidth: '1',
            borderColor: '#EBEEF5',
            textStyle: {
              color: '#8A8F99'
            }
          },
          legend: {
            orient: 'vertical',
            left: '70%',
            bottom: '5%',
            textStyle: {
              color: '#898E99'
            }
          },
          series: [
            {
              name: '',
              type: 'pie',
              center: ['30%', '50%'],
              radius: ['50%', '75%'],
              avoidLabelOverlap: false,
              itemStyle: {
                // borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 0
              },
              label: {
                show: true,
                position: 'center',
                formatter: '{num|' + this.taskFun.count + '}' + '\n\r' + '{name|总数}',
                rich: {
                  num: {
                    fontSize: 18,
                    color: '#8A8F99'
                  },
                  name: {
                    fontFamily: '微软雅黑',
                    fontSize: 14,
                    color: '#8A8F99',
                    lineHeight: 30
                  }
                }

              },

              data: this.taskFun.data
            }
          ]
        }
      }
    },
    chartClick(params) {
      this.$set(params, 'classify', 'TASK')
      this.detailParams = { visible: true, form: params }
    }
  }
}
</script>

<style lang="scss" scoped>
.clearfix{
  height: 30px;
  line-height: 30px;
}
</style>
