<template>
  <vone-echarts-card :title="title">
    <vone-echarts v-if="data.length>0" :options="options" @chartClick="chartClick" />
    <vone-empty v-else />

    <ProjectDetailDialog v-if="detailParams.visible" v-bind="detailParams" v-model:visible="detailParams.visible" />
  </vone-echarts-card>

</template>

<script>
import ProjectDetailDialog from '../components/detail-dialog.vue'

export default {
  components: {
    ProjectDetailDialog
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: null
    },
    name: {
      type: String,
      default: ''
    },
    requireFun: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      detailParams: { visible: false },
      data: [],
      backgroundColor: '#2c343c',
      options: {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#fff',
          borderWidth: '1',
          borderColor: '#EBEEF5',
          textStyle: {
            color: '#8A8F99'
          }
        },
        legend: {
          orient: 'vertical',
          left: '70%',
          bottom: '5%',
          textStyle: {
            color: '#8A8F99'
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle'
        },
        series: [
          {
            name: '任务概览',
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['50%', '75%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'center',
              formatter: '{num|' + 0 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#8A8F99'
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#8A8F99',
                  lineHeight: 30
                }
              }
            }

          }
        ]
      }
    }
  },
  watch: {
    requireFun(val) {
      if (val.data) {
        val.data.forEach(item => {
          item.itemStyle = {
            color: item.color
          }
        })
      }
      this.options.series[0].data = val.data
      this.options.series[0].label.formatter = '{num|' + val.count + '}' + '\n\r' + '{name|总数}'
      this.data = val.data
    }
  },
  methods: {
    chartClick(params) {
      this.$set(params, 'classify', 'ISSUE')
      this.detailParams = { visible: true, form: params }
    }
  }
}
</script>

<style lang="scss" scoped>
.clearfix{
  height: 30px;
  line-height: 30px;
}
</style>
