<template>
  <el-dialog title="数据详情" v-model:visible="visible" width="80%" :close-on-click-modal="false" :before-close="onClose">

    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane v-for="item in types " :key="item.code" :label="item.name" :name="item.code" />
    </el-tabs>

    <vxe-table
      ref="trendsDetail"
      class="vone-vxe-table"
      border
      auto-resize
      max-height="500px"
      :loading="loading"
      :empty-render="{ name: 'empty' }"
      :data="tableData.records"
      :column-config="{ minWidth:'150px' }"
      show-overflow="tooltip"
      row-id="id"
    >
      <vxe-column title="编号" field="code" width="150" fixed="left">
        <template #default="{ row }">
          <a @click="jumpTask(row)">{{ row.code }}</a>
        </template>
      </vxe-column>
      <vxe-column title="标题" field="name" min-width="150" fixed="left">
        <template #default="{ row }">
          <a @click="jumpTask(row)">
            <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
              <i :class="`iconfont ${row.echoMap.typeCode.icon}`" :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}" />
            </span>
            {{ row.name }}</a>
        </template>
      </vxe-column>
      <vxe-column title="工作项类型" field="classify" width="100">
        <template #default="{ row }">
          {{ typeNameObj(row).classifyType }}
        </template>
      </vxe-column>
      <vxe-column title="类型" field="type" width="150">
        <template #default="{ row }">
          {{ typeNameObj(row).typeName }}
        </template>
      </vxe-column>
      <vxe-column title="负责人" field="leadingBy" width="100">
        <template #default="{ row }">
          <vone-user-avatar :avatar-path="userInfo(row).avatarPath" :avatar-type="userInfo(row).avatarType" :name="userInfo(row).name" />
        </template>
      </vxe-column>
      <vxe-column title="状态" field="stateCode" width="120">
        <template #default="{ row }">
          <span v-if="row.stateCode && row.echoMap && row.echoMap.stateCode" :style="{ border:`1px solid ${ row.echoMap.stateCode.color}`,color:`${row.echoMap.stateCode.color}`}" class="tagCustom">  {{ stateName(row) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="projectId" show-overflow="tooltip" title="所属项目" width="150">
        <template #default="{ row }">
          {{ projectName(row) }}
        </template>
      </vxe-column>
    </vxe-table>

    <div slot="footer" style="height:25px">
      <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />
    </div>
  </el-dialog>
</template>

<script>
import { projectQueryView } from '@/api/vone/project/overview'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    },
    types: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: {},
      activeName: 'created'

    }
  },
  computed: {

    typeNameObj() {
      return function(row) {
        const classifyType = row.echoMap?.typeCode?.classify?.desc || ''
        const typeName = row.echoMap?.typeCode?.name || ''
        return { classifyType, typeName }
      }
    },
    userInfo() {
      return function(row) {
        const avatarPath = row.echoMap?.leadingBy?.avatarPath || ''
        const avatarType = row.echoMap?.leadingBy?.avatarType || ''
        const name = row.echoMap?.leadingBy?.name || ''
        return { avatarPath, avatarType, name }
      }
    },
    stateName() {
      return function(row) {
        return row.echoMap?.stateCode?.name || ''
      }
    },
    projectName() {
      return function(row) {
        return row.echoMap?.projectId?.name || ''
      }
    }
  },
  mounted() {
    if (this.form.activeTab) {
      this.activeName = this.form.activeTab || this.types[0].code
    }
    this.getInitTableData()
  },
  methods: {
    handleClick() {
      this.getInitTableData()
    },

    async getInitTableData() {
      this.tableData = []
      this.loading = true

      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs?.searchForm?.sortObj

      // console.log(this.form, 'formformform')
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {},
        model: {
          projectId: this.$route.params.id,
          location: this.activeName,
          scopeTime: {
            end: this.form.end,
            start: this.form.start
          },
          productId: this.$route.params.productId,
          currentTime: this.$route.params.id ? this.form.name : null
        }
      }

      this.getBug(params)
    },
    async getBug(params) {
      const { data, isSuccess, msg } = await projectQueryView(params)
      this.loading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.tableData = data
    },

    async jumpTask(row) {
      const workItemType = row.echoMap?.typeCode?.classify?.code
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = workItemType == 'ISSUE' ? '/project/issue' : workItemType == 'TASK' ? '/project/task' : workItemType == 'BUG' ? '/project/defect' : '/project/issue'
      const newpage = await this.$router.resolve({ path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`, query: {
        showDialog: true,
        queryId: row?.id,
        rowTypeCode: row?.typeCode,
        stateCode: row?.stateCode,
        projectId: projectId
      }})
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    onClose() {
      this.$emit('update:visible', false)
      this.tableData = {}
    }
  }

}
</script>

<style>

</style>
