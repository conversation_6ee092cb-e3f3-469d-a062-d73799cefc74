<!-- 测试计划 -->
<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span class="hearder-title">
          新增计划
        </span>
        <div class="search">
          <el-tooltip class="item" effect="dark" content="新增计划" placement="top">
            <i
              :disabled="!$permission('project_test_plan_create')"
              style="margin-left: 8px"
              class="iconfont el-icon-tips-plus"
              @click="sendId({
                name: '新增计划',
                nameEn: 'default',
                status: '点击创建新的测试计划',
                stateId: '0'
              })"
            />
          </el-tooltip>
        </div>
      </div>
      <div class="cardContent">
        <vone-empty v-if="planList.length == 0" />
        <div v-else>
          <el-card v-for="item in planList" :key="item.id" shadow="hover" :class="['left-small-card', planData.id === item.id ? 'is-active' : '']" @click.native="sendId(item)">
            <div class="small-card-title">
              <div class="small-card-title-left">
                <span>{{ item.name }}</span>
              </div>
              <div @click.stop>
                <el-dropdown trigger="click">
                  <i class="iconfont el-icon-application-more" style="cursor: pointer" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="iconfont el-icon-application-edit" :disabled="!$permission('project_test_plan_edit')" @click.native="showTestPlan(item)">编辑</el-dropdown-item>
                    <el-dropdown-item icon="iconfont el-icon-application-delete" :disabled="!$permission('project_test_plan_del')" @click.native="removeDEL(item)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div class="small-card-desc">
              {{
                item.type === 'regression'? '回归测试':item.type === 'smoke'?'冒烟测试':'系统测试'
              }}
              <el-dropdown style="float: right" trigger="click">
                <a class="el-dropdown-link dropItem">
                  <div class="stateBlock" :style="{color:`${item.stateColor}`,background:`${item.backGroundColor}`}">
                    {{ stateMap[item.stateId] && stateMap[item.stateId].name || '' }}
                  </div>
                  <div
                    class="stateBlockIcon"
                    :style="{
                      color:`${item.stateColor}`,background:`${item.backGroundColor}`}"
                  >
                    <i class="iconfont el-icon-direction-down" />
                  </div>
                </a>

                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :disabled="item.stateId == 0" @click.native="changeStatus(item, 0)">未开始</el-dropdown-item>
                  <el-dropdown-item :disabled="item.stateId == 1" @click.native="changeStatus(item, 1)">进行中</el-dropdown-item>
                  <el-dropdown-item :disabled="item.stateId == 2" @click.native="changeStatus(item, 2)">已完成</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="small-card-desc">
              {{ item.planStime | format }} -
              {{ item.planEtime | format }}
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <div class="rightSection">
      <div v-if="!isLarge" class="rightTopSection">
        <!-- 右侧用例执行进度 -->
        <div style="margin:-16px">
          <process :progress-data="progressData" :pro-cases-len="libCaseList.length" :plan-data="planData" />
        </div>
      </div>
      <div :class="isLarge? 'rightHightSection' : 'rightBottomSection'">

        <testCase :library-id="libraryId" :tree-data="treeData" :table-data="tableData" :plan-data="planData" @success="refresh" @changeLarge="changeLarge" />
      </div>
      <!-- 新增编辑计划抽屉 -->
      <planDrawer v-model:visible="planVisibile" :plan-type="planType" :library-id="libraryId" :tree-data="treeData" :case-list="tableData.records" :lib-cases="libCaseList" :plan-data="planData" :project-info="projectInfo" @success="getTestPlanList" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import storage from 'store'
import planDrawer from './plan/components/plan-drawer.vue'
import process from './plan/components/progress.vue'
import testCase from './plan/test-case/index.vue'

import { list2Tree } from '@/utils/list2Tree'
import { deleteTestPlan, editTestPlan, getAllConnectByLibraryId, getPlanCases, getProjectPlans, getTestPlanStatus } from '@/api/vone/testmanage/case'
import { getCaseTree, getLibraryIdByProjectId } from '@/api/vone/testmanage'
import { apiProjectInfo } from '@/api/vone/project'
const stateList = [
  {
    state: 0,
    name: '未开始',
    color: '#8C8C8C',
    backGroundColor: '#F5F5F5'
  },
  {
    state: 1,
    name: '进行中',
    color: '#0BABE5',
    backGroundColor: '#E6FCFF'
  },
  {
    state: 2,
    name: '已完成',
    color: '#00BF80',
    backGroundColor: '#E1FAED'
  }
]
export default {
  components: {
    process,
    planDrawer,
    testCase
  },
  filters: {
    format(val) {
      if (!val) return ''
      return dayjs(val).format('MM月DD日')
    }
  },
  data() {
    return {
      tabActive: 'testCase',
      types: [
        { type: 'testCase', name: '用例' }
      ],
      userMap: {},
      tableData: {
        records: [],
        total: 0
      },
      libraryId: '',
      libCaseList: [], // 项目下所有用例
      treeData: [],
      listLoading: false,
      planList: [{
        name: '新增计划',
        nameEn: 'default',
        status: '点击创建新的测试计划',
        stateId: '0'
      }], // 计划列表
      planData: {}, // 选择的计划数据
      projectInfo: {}, // 当前项目

      planVisibile: false, // 新增计划弹窗
      planType: 'edit',
      progressData: {}, // 计划进度数据
      stateList,
      stateMap: {},
      isLarge: false
    }
  },
  watch: {
    loginUser() {
      return storage.get('user')
    }
  },
  created() {
    this.getLibraryList()
    this.getTestPlanList()
    this.getProjectInfo()
  },
  mounted() {
    this.stateMap = this.stateList.reduce((r, v) => (r[v.state] = v) && r, {})
    console.log(this.stateMap)
  },
  methods: {
    //  查询当前项目下用例库
    async getLibraryList() {
      const { id } = this.$route.params
      const params = {
        projectId: id
      }

      const res = await getLibraryIdByProjectId(params)
      if (res.isSuccess) {
        this.libraryId = res.data
        this.getTreeData()
        this.getProjectCases()
      }
    },
    // 查询当前项目信息
    async getProjectInfo() {
      const { data, isSuccess } = await apiProjectInfo(
        this.$route.params.id
      )
      if (isSuccess) {
        this.projectInfo = data || {}
      }
    },
    // 查询项目下所有用例
    async getProjectCases() {
      const res = await getAllConnectByLibraryId(this.libraryId)
      if (res.isSuccess) {
        this.libCaseList = res.data || []
      }
    },
    // 查询树节点数据
    async getTreeData() {
      const res = await getCaseTree(this.libraryId)
      if (res.isSuccess) {
        this.treeData = list2Tree(res.data, { parentKey: 'parentId' })
      }
    },
    // 查询计划用例状态
    async getPlanCaseStatus() {
      const params = {
        planId: this.planData.id
      }
      const res = await getTestPlanStatus(params)
      if (res.isSuccess) {
        this.progressData = res.data
        const { blockingNum, skipNum, smokeNum, systemNum, undoNum } = res.data
        this.progressData.totalSum = Number(blockingNum) + Number(skipNum) + Number(smokeNum) + Number(systemNum) + Number(undoNum)
      }
    },
    // 查询测试计划
    async getTestPlanList() {
      const { id } = this.$route.params
      const params = {
        projectId: id
      }
      const res = await getProjectPlans(params)
      if (res.isSuccess) {
        this.planList = res.data

        if (this.$route.query.planId) {
          this.planData = {
            id: this.$route.query.planId
          }
        } else {
          this.planData = res.data[0] || {}
        }

        if (this.planData.id) {
          this.getPlanCaseList(this.planData.id)
          // 查询用例状态
          this.getPlanCaseStatus()
        }
        this.planList.forEach(element => {
          element.stateColor = this.stateMap[element.stateId]?.color || '#ccc'
          element.backGroundColor = this.stateMap[element.stateId]?.backGroundColor || '#fff'
        })
      }
    },
    // 查人员
    async getUserList(userId, row) {
      if (this.userMap[userId]) {
        this.$set(row, 'userData', this.userMap[userId])
        return
      }
      const res = await this.$store.dispatch('user/getUserData', userId)
      if (!res.isSuccess) {
        return
      }
      this.userMap[userId] = res.data
      this.$set(row, 'userData', res.data)
    },
    // 查询计划下用例
    async getPlanCaseList(id) {
      const params = {
        planId: id
      }
      const res = await getPlanCases(params)
      if (res.isSuccess) {
        this.tableData.records = res.data
        this.tableData.total = res.data.length
        Array.isArray(this.tableData.records) && this.tableData.records.forEach(ele => {
          ele.execBy && this.getUserList(ele.execBy, ele)
        })
      } else {
        this.tableData.records = []
      }
    },
    // 编辑计划
    showTestPlan(item) {
      this.planData = item
      this.planVisibile = true
      this.planType = 'edit'
    },
    // 删除计划
    async removeDEL(item) {
      await this.$confirm('是否确认删除？', '删除', {
        type: 'warning',
        customClass: 'delConfirm'
      })
      const res = await deleteTestPlan([item.id])
      if (res.isSuccess) {
        this.$message.success('删除成功')
        this.getTestPlanList()
      }
    },

    // 查询计划下用例
    sendId(val) {
      if (!this.$permission('project_test_plan_create')) return
      if (val.nameEn === 'default') {
        this.planVisibile = true
        this.planType = 'add'
        return
      }
      this.planData = val
      // 刷新状态
      this.refresh()
    },
    // 刷新用例数据
    refresh() {
      this.getPlanCaseList(this.planData.id)
      // 查询用例状态
      this.getPlanCaseStatus()
    },
    // 修改计划状态
    async changeStatus(item, key) {
      this.$set(item, 'stateId', key)
      const params = {
        ...item,
        stateId: key
      }
      const res = await editTestPlan(params)
      if (res.isSuccess) {
        this.sendId(item)
      }
    },
    changeLarge(type) {
      this.isLarge = type
    }
  }
}
</script>
<style lang='scss' scoped>
.dropItem {
  display: flex;
  align-items: center;
  font-size: 12px;
  padding: 4px 6px;
  border-radius: 4px;
}
.nostart {
  color: var(--main-theme-color,#3e7bfa);
  border: 1px solid var(--main-theme-color,#3e7bfa);
}
.progress {
  color: #f7cd55;
  border: 1px solid #f7cd55;
}
.finish {
  color: #6fc38a;
  border: 1px solid #6fc38a;
}

:deep(.el-tabs__content) {
  display: none;
}
:deep(.relateCard) {
  position: relative;
  .el-card__body {
    padding: 12px 16px;
  }
}

.scale {
  position: absolute;
  right: 15px;
  top: 10px;
  z-index: 10;
  cursor: pointer;
}

.leftSection {
	width: 240px;
  height: calc(100vh - 48px - 20px);
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		.hearder-title {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
    .header-total {
      font-size: 12px;
      color: #777F8E;
    }
		.search {
			display: inline-block;
			.iconfont {
				cursor: pointer;
				color: var(--font-second-color);
			}
			.iconfont:hover {
				color: var(--main-theme-color);
			}
			.iconfont.active {
				color: var(--main-theme-color);
			}
		}
  }
  .cardContent {
		padding:16px 16px 0;
    height: calc(100vh - 48px - 48px - 50px);
		overflow-y: overlay;
    .left-small-card {
      margin-bottom: 16px;
      cursor: pointer;
      height: unset;
    }
  }
}
.stateBlock {
  text-align: center;
  font-weight: 500;
  border-radius: 2px 0 0 2px;
  padding:0px 6px
}
.stateBlockIcon {
  text-align: center;
  font-weight: 500;
  border-radius: 2px 0 0 2px;
  padding:0px 4px;
  border-left: 1px solid var(--main-bg-color);
  i {
    font-size: 12px;
  }
}
.dropItem {
  display: flex;
  font-size: 12px;
  line-height: 18px;
}
.rightSection {
  background: none;
  padding: 0;
  box-shadow: none
}
.rightTopSection {
	border-radius: 4px;
	padding: 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	color: var(--font-main-color);
	.title {
		font-weight: 500;
		line-height: 22px;
		display: flex;
		.svg-icon {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}
	}
	.detail {
		margin-top: 8px;
		line-height: 22px;
		span {
			color: var(--font-second-color);
			margin-right: 56px;
			display:inline-flex
		}
		p {
			display: inline-block;
			margin: 0px;
			margin-left:12px;
			color: var(--font-main-color);
		}
    :deep(.avatar span) {
      margin-left: 4px;
    }
	}
}
.rightBottomSection {
  position: relative;
	border-radius: 4px;
	padding: 16px 16px 10px 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	margin-top: 10px;
	height: calc(100vh - 260px);
}
.rightHightSection {
   position: relative;
	border-radius: 4px;
	padding: 16px 16px 10px 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	height: calc(100vh - 70px);
}
</style>
