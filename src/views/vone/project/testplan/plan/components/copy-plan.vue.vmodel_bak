<!-- 测试计划 -->
<template>
  <div>
    <el-drawer title="复用测试计划" :visible="visible" direction="rtl" size="70%" :show-close="false" :before-close="onClose" :wrapper-closable="false">

      <el-row type="flex" class="ctx">
        <el-col class="leftForm">
          <el-form ref="taskFormLeft" :model="taskForm" :rules="taskRules" label-position="top">
            <el-form-item label="标题" prop="name">
              <el-input v-model="taskForm.name" placeholder="输入计划标题" />
            </el-form-item>
            <el-form-item label="测试类型">
              <el-radio-group v-model="taskForm.type">
                <el-radio label="system">系统测试</el-radio>
                <el-radio label="smoke">冒烟测试</el-radio>
                <el-radio label="regression">回归测试</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <vone-editor ref="editor" v-model="taskForm.description" @input.native="eventDisposalRangeChange(taskForm.description)" />
            </el-form-item>
            <el-form-item v-if="planType==='copy'&&taskForm.libraryId">
              <el-radio-group v-model="taskForm.addCase" class="pt-radio">
                <el-radio label="1" class="radio-label">包含全部用例</el-radio>
                <div>覆盖本用例库全部可用用例，如果用例库有新增的用例，会自动加入到本计划中。
                </div>
                <el-radio label="2" class="radio-label">手动圈选用例</el-radio>
                <div>手动从用例库中圈选用例，如果用例库有新创建的用例，不会被同步到本计划。</div>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="taskForm.addCase == 2">
              <el-card class="box-card">
                <div class="text-item">
                  <span>{{ taskForm.projectCaseList.length }}</span>
                  {{ '条用例已选' }}
                  <a class="selectButton" @click="checkScope">圈选范围<i class="el-icon-right" /></a>
                </div>
              </el-card>
            </el-form-item>
            <el-form-item v-if="planType=='filter'">
              <el-card class="box-card">
                <div class="text-item">
                  <span>{{ treeData.length }}</span>
                  {{ '条用例已选' }}
                </div>
              </el-card>
            </el-form-item>
          </el-form>
        </el-col>
        <div class="ctx-right">
          <el-form ref="taskForm" :model="taskForm" :rules="taskRules" label-position="left">
            <el-form-item label="维护人" prop="leadingBy">
              <vone-remote-user v-model="taskForm.leadingBy" />
            </el-form-item>
            <el-form-item label="开始时间" prop="planStime">
              <el-date-picker v-model="taskForm.planStime" type="datetime" placeholder="开始时间" format="MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsStart" default-time="['9:00:00']" style="width:100%;" />
            </el-form-item>
            <el-form-item label="结束时间" prop="planEtime">
              <el-date-picker v-model="taskForm.planEtime" type="datetime" placeholder="结束时间" format="MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsEnd" default-time="['18:00:00']" style="width:100%;" />
            </el-form-item>
            <!-- <el-form-item label="产品/场景用例库" prop="libraryId">
              <el-select v-model="taskForm.libraryId" placeholder="请选择" style="width: 100%" disabled filterable @change="getLibrary">
                <el-option v-for="item in productList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="测试环境" prop="envId">
              <el-select v-model="taskForm.envId" filterable clearable placeholder="请选择" style="width: 100%">
                <el-option v-for="item in envList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-row>
      <footer class="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </footer>
    </el-drawer>
    <relateCase v-model:visible="addCaseVisible" :library-id="taskForm.libraryId" :case-list="taskForm.projectCaseList" @success="saveProCases" />
  </div>
</template>

<script>
import { saveProductPlan } from '@/api/vone/testplan'
import { apiBaseDictPage } from '@/api/vone/base/dict'

import relateCase from '../../../../test/plan-manager/components/relate-case.vue'
import { getAllConnect, getproductCaseLibrary } from '@/api/vone/testplan'
import { copyCurrentPlan } from '@/api/vone/testmanage'

export default {
  components: { relateCase },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    planType: {
      type: String,
      default: 'copy'
    },
    // 测试计划数据
    planData: {
      type: Object,
      default: () => { }
    },
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      addCaseVisible: false,
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.taskForm.planEtime) {
            return (
              time.getTime() < Date.now() - 8.64e7 ||
              time.getTime() > new Date(this.taskForm.planEtime).getTime()
            )
          }
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          return (
            time.getTime() < Date.now() - 8.64e7 ||
            time.getTime() < new Date(this.taskForm.planStime).getTime()
          )
        }
      },
      saveLoading: false,
      taskForm: {
        addCase: '1',
        projectCaseList: [], // 已关联用例
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        systemId: '',
        envId: ''
      },
      taskRules: {
        name: [
          {
            required: true,
            message: '请输入标题'
          },
          {
            pattern: '^[^ ]+$',
            message: '不能输入空格'
          }
        ],
        leadingBy: [{ required: true, message: '请选择维护人', trigger: 'change' }],
        // libraryId: [{ required: true, message: '请选择产品/场景用例库', trigger: 'change' }],
        planStime: [{ required: true, message: '请选择计划开始时间', trigger: 'blur' }],
        planEtime: [{ required: true, message: '请选择计划结束时间', trigger: 'blur' }]
      },
      envList: [], // 环境列表
      productList: [] // 产品列表
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getEnvList()
        // this.getAllProductList()
        this.taskForm = {
          ...this.planData,
          name: ''
        }
        this.taskForm.projectCaseList = this.planData?.echoMap?.testProductCase || []
      }
    }
  },
  methods: {
    getLibrary() {
      this.taskForm.projectCaseList = []
    },
    eventDisposalRangeChange(value) { },
    // 选择用例
    checkScope() {
      this.addCaseVisible = true
    },
    // 查询环境列表
    async getEnvList() {
      const params = {
        current: 1,
        size: 9999,
        extra: {},
        model: { type: 'ENVIRONMENT' }
      }
      const res = await apiBaseDictPage(params)
      if (res.isSuccess) {
        this.envList = res.data.records
      }
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await getproductCaseLibrary()
      if (res.isSuccess) {
        this.productList = res.data
      }
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.taskFormLeft.resetFields()
      this.$refs.taskForm.resetFields()
      this.taskForm = {
        projectCaseList: [],
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        envId: ''
      }
    },
    // 保存选择用例
    saveProCases({ caseIds, treeIds }) {
      this.taskForm.projectCaseList = caseIds
    },
    // 保存
    async saveInfo() {
      this.$refs['taskFormLeft'].validate((valid) => {
        if (valid) {
          this.$refs['taskForm'].validate((valids) => {
            if (valids) {
              // 默认关联全部用例
              this.getConnectCase()
            }
          })
        } else {
          return false
        }
      })
    },
    // 获取关联的用例
    async getConnectCase() {
      if (this.planType === 'filter') {
        this.excuteCaseSave()
      } else {
        let projectList = []
        if (this.taskForm.addCase == 1) {
        // 查询关联的所有用例
          const res = await getAllConnect(this.taskForm.libraryId)
          if (res.isSuccess) {
            projectList = res.data
          }
        } else {
          projectList = this.taskForm.projectCaseList.map(v => v.testcaseId || v.id)
        }
        this.addSave(projectList)
      }
    },
    // 新建保存
    async addSave(projectList) {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()
        const params = {
          treeId: this.planData.id,
          delay: this.taskForm.delay,
          description: this.taskForm.description,
          planKey: this.taskForm.planKey,
          leadingBy: this.taskForm.leadingBy,
          name: this.taskForm.name,
          planEtime: this.taskForm.planEtime,
          planStime: this.taskForm.planStime,
          rateProgress: 0,
          stateId: this.taskForm.stateId,
          type: this.taskForm.type,
          libraryId: this.taskForm.libraryId,
          caseId: projectList,
          envId: this.taskForm.envId
        }
        this.saveLoading = true
        const res = await saveProductPlan(params)
        if (res.isSuccess) {
          this.$message.success('新建成功')
          this.$emit('success')
          this.onClose()
        }
      } catch (error) {
        this.saveLoading = false
        return
      }
    },
    // 执行用例页编辑保存
    async excuteCaseSave() {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()
        delete this.taskForm.projectCaseList
        const params = {
          ...this.taskForm,
          treeId: this.planData.treeId,
          caseId: this.treeData.map(v => v.testcaseId)
        }
        this.saveLoading = true
        const res = await copyCurrentPlan(params)
        this.saveLoading = false
        if (res.isSuccess) {
          this.$message.success('复制成功')
          this.$emit('success')
          this.onClose()
          const { projectKey, projectTypeCode, id } = this.$route.params
          window.requestAnimationFrame(() => {
            this.$router.push({
              path: `/project/testplan/${projectKey}/${projectTypeCode}/${id}`
            })
          })
        } else {
          this.$message.error(res.msg)
        }
      } catch (err) {
        this.saveLoading = false
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.header {
  display: flex;
  align-items: center;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #202d40;
  }
}
.footer {
  margin-top: 12px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 16px 20px;
  text-align: right;
  background-color: var(--main-bg-color,#fff);
  border-top: 1px solid var(--disabled-bg-color,#ebeef5);
}
:deep() {
  .w-e-text-container {
    min-height: 240px;
  }
  .el-drawer__body {
    padding: 0px;
  }
}
.ctx {
  height: calc(100% - 58px);
  > div {
    overflow: auto;
  }
  .leftForm {
    padding: 12px 20px;
  }
}
.ctx-right {
  border-left: 1px solid var(--disabled-bg-color,#ebeef5);
  width: 400px;
  padding: 12px 20px;
  background-color: #fafafa;
}
.box-card {
  border: 1px solid var(--disabled-bg-color,#ebeef5);
  span {
    font-weight: bold;
    padding-right: 4px;
  }
  .selectButton {
    padding-left: 20px;
    font-weight: bold;
    color: var(--main-theme-color);
    cursor: pointer;
    i {
      font-size: 16px;
    }
  }
}
.basicBox {
  cursor: pointer;
  position: relative;
  .basicIcon {
    float: left;
    color: #2dbcff;
    font-size: 36px;
    margin-top: 8px;
  }
  .basicRight {
    float: left;
    margin-left: 10px;
    .basicText {
      font-size: 12px;
      color: #ccc;
      margin-top: -12px;
    }
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 10px;
    left: 0;
    height: 40px;
    z-index: 99;
    cursor: pointer;
  }
  .basic-time {
    position: relative;
    .basic-date {
      position: absolute;
      top: 4px;
      left: 0;
      height: 40px;
      z-index: 99;
      cursor: pointer;
    }
  }
}
.tab {
  :deep(.el-input__suffix-inner) {
    display: none;
  }
}
:deep(.el-dialog__body) {
  .el-form-item {
    margin-bottom: 10px;
  }
}
.empty-senior {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  i {
    margin-right: 10px;
  }
}
:deep(.w-e-toolbar .w-e-menu) {
  width: 25px;
}
:deep(.w-e-text-container) {
  height: 240px;
}

.custom-theme-dark {
  .ctx-right {
    background-color: #252933;
  }
}
</style>
<style lang="scss" scoped>
.pt-radio {
  .el-radio {
    position: relative;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    height: 16px;
    cursor: pointer;
    color: rgb(32, 45, 64);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  }
  .el-radio__inner {
    border: 1px solid rgb(201, 207, 215);
  }
  div {
    font-size: 13px;
    line-height: 16px;
    padding-left: 24px;
    color: rgb(145, 153, 163);
    margin: 8px 0px 16px;
  }
}
</style>
