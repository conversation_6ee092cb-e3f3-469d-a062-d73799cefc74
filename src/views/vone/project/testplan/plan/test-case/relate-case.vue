<template>
  <el-dialog v-if="visible" width="78%" top="10vh" :visible="visible" :before-close="onClose" height="70%" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <template slot="title">
      <span style="margin-right:40px">关联用例</span>
      <el-switch v-model="status" active-text="展示全部树级" @change="switchTreeExpend" />
    </template>
    <el-row>
      <el-col v-loading="treeLoading" :span="8" class="elStyle l_body">
        <vone-tree
          ref="tree"
          show-checkbox
          check-strictly
          :data="dataTree"
          height="500px"
          node-key="id"
          :expand-on-click-node="false"
          highlight-current
          :default-expanded-keys="defaultSelectTree"
          :props="defaultProps"
          @node-click="treeCaseClick"
          @check="checkChange"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <svg class="icon iconCls" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <span class="treeNode">{{ data.name }}</span>
            <span v-if=" (node.checked || node.indeterminate) && data.num" class="num">{{ data.num }}</span>
          </span>
        </vone-tree>
      </el-col>
      <el-col :span="16" class="elStyle r_body">
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic table-search-key="projectReleaseCaseTable" v-model:model="formData" :table-ref="$refs['projectReleaseCaseTable']" @getTableData="getTableList(currentNode)">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="用例" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入用例" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="编号" prop="caseKey">
                    <el-input v-model="formData.caseKey" placeholder="请输入编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="标签" prop="tabName">
                    <el-select
                      v-model="formData.tabName"
                      filterable
                      multiple
                      clearable
                      placeholder="请输入标签筛选用例"
                      remote
                      :remote-method="getTagsListByLibraryId"
                      :loading="tagLoading"
                    >
                      <el-option v-for="(item) in tagsList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority ">
                    <vone-icon-select v-model="formData.priority" :data="prioritList" filterable clearable style="width:100%">
                      <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                        <i
                          :class="`iconfont ${item.icon}`"
                          :style="{
                            color: item.color,
                            fontSize: '16px',
                            paddingRight: '6px',
                          }"
                        />
                        {{ item.name }}
                      </el-option>
                    </vone-icon-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </vone-search-dynamic>
          </template>

        </vone-search-wrapper>
        <main style="height:416px;">
          <vxe-table
            ref="projectReleaseCaseTable"
            class="vone-vxe-table"
            height="auto"
            border
            resizable
            show-overflow="tooltip"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth:'120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-column type="checkbox" width="36" fixed="left" align="center" />
            <vxe-column title="用例" field="name">
              <template #default="{ row }">
                <div style="text-align: left">
                  <i v-if="row.stepType === 'subclause'" class="iconfont el-icon-application-view-list" style="color: #37cdde;" />
                  <i v-else class="el-icon-document" style="color: #3e7bfa;" />
                  <span style="margin-left:4px;">{{ row.caseKey+ ' '+row.name }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="优先级" field="priority">
              <template #default="{ row }">
                <span v-if="row.priority === 1">
                  <i class="iconfont el-icon-icon-dengji-zuidi2" style="color:#4ECF95;" />
                  最低
                </span>
                <span v-else-if="row.priority === 2">
                  <i class="iconfont el-icon-icon-dengji-jiaodi2" style="color:#5ACC5E;" />
                  较低
                </span>
                <span v-else-if="row.priority === 5">
                  <i class="iconfont el-icon-icon-dengji-zuigao2" style="color:#FA6A69;" />
                  最高
                </span>
                <span v-else-if="row.priority === 4">
                  <i class="iconfont el-icon-icon-dengji-jiaogao2" style="color:#FA8669;" />
                  较高
                </span>
                <span v-else>
                  <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color,#3e7bfa);" />
                  普通
                </span>
              </template>
            </vxe-column>
            <vxe-column title="执行人" field="execBy">
              <template #default="{ row }">
                <template v-if="row.execBy && row.userData">
                  <vone-user-avatar :avatar-path="row.userData.avatarPath" :name="row.userData.name" style="justify-content:center;" />
                </template>

              </template>
            </vxe-column>
          </vxe-table>
        </main>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <div class="excuteUser">
        <div class="label">指派新关联用例执行人</div>
        <vone-remote-user v-model="excuteUser" :default-data="defaultData" />
        <el-tooltip style="margin-left:2px" placement="top" content="直接指派勾选目标用例的执行人">
          <i class="el-icon-warning-outline" />
        </el-tooltip>
      </div>
      <el-button @click="onClose">取消</el-button>
      <el-button :loading="tableLoading" type="primary" @click="saveInfo">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { debounce } from 'lodash'

import { finddelCaseByIdPage, getAllChild } from '@/api/vone/testmanage/case'
import { findProductCaseTreeOfRepository } from '@/api/vone/testplan'
import { getPlanAllCases } from '@/api/vone/testmanage/plan'
import { getCaseLibraryTags } from '@/api/vone/testTab'

import { catchErr } from '@/utils'
import { list2Tree } from '@/utils/list2Tree'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    planData: {
      type: Object,
      default: () => { }
    },
    // 用例库id
    libraryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      status: false,
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      formData: {
        name: '',
        caseKey: '',
        tabName: [],
        priority: ''
      },
      dataTree: [],
      excuteUser: '', // 执行人
      defaultData: [],
      tableOptions: {
        isOperation: false // 表格有操作列时设置
      },
      treeLoading: false,
      tableLoading: false,
      selectedId: [], // 选中的id
      releasedCase: [], // 计划下已关联的所有用例
      tableData: { records: [] }, // 表格数据
      currentNode: {},
      defaultProps: {
        label: 'name'
      },
      tagLoading: false,
      tagsList: [],
      defaultSelectTree: [],
      releaseTreeMap: {}, // 已关联的用例树
      allTreeCaseMap: {} // 当前用例库下所有分组树节点和对应用例id数据
    }
  },
  watch: {
    async visible(v) {
      if (!v) return
      this.status = false
      this.selectedId = []
      this.treeLoading = true
      // 查询当前计划已关联的用例
      await this.getReleasedCases()
      this.gettreeData()
      this.getTagsListByLibraryId()
    }
  },
  methods: {
    switchTreeExpend(val) {
      const nodesMap = this.$refs.tree.store.nodesMap

      for (const id in nodesMap) {
        nodesMap[id].level > 1 && (nodesMap[id].expanded = val)
      }
    },
    // 标签搜索
    getTagsListByLibraryId: debounce(function(query) {
      this.tagLoading = true
      getCaseLibraryTags({
        id: this.libraryId,
        name: query
      }).then(res => {
        this.tagsList = res.data
        this.tagLoading = false
      }).catch(() => {
        this.tagLoading = false
      })
    }, 1000),
    // 查询树节点数据
    async gettreeData() {
      const [res, err] = await catchErr(findProductCaseTreeOfRepository(this.libraryId))
      if (err) return
      const tree = list2Tree(res.data, { parentKey: 'parentId' })
      this.dataTree = tree
      this.currentNode = tree[0] || {}
      this.currentNode.disabled = true // 禁用根节点
      this.getAllTreeCases(tree[0])
    },
    // 查询计划已关联的所有用例
    async getReleasedCases() {
      const [res, err] = await catchErr(getPlanAllCases(this.planData.id))
      if (err) return
      if (res.isSuccess) {
        this.releasedCase = res.data || []
        this.releaseTreeMap = {}
        this.defaultSelectTree = []
        this.selectedId = res.data?.map(v => {
          this.defaultSelectTree.push(v.treeId)
          // 缓存关联的树节点和用例
          this.releaseTreeMap[v.treeId] = this.releaseTreeMap[v.treeId] ? [...this.releaseTreeMap[v.treeId], v.id] : [v.id]
          return v.id
        }) || []
      }
    },
    // 查询当前用例树所有层级用例数量
    async getAllTreeCases(node) {
      const [res, err] = await catchErr(getAllChild(node.id))
      this.treeLoading = false
      if (err) return
      if (res.isSuccess) {
        this.allTreeCaseMap = res.data.reduce((acc, cur) => (acc[cur.treeId] = cur) && acc, {})
        // 设置当前树所有已选节点选中和用例数量显示
        this.$nextTick(() => {
          const allNodes = this.$refs.tree.store.nodesMap

          for (const id in this.releaseTreeMap) {
            const node = allNodes[id]
            if (!node) continue
            // 当前节点下所有用例id
            const caseIds = this.allTreeCaseMap[id]?.caseIds || []
            const releasedCases = this.releaseTreeMap[id] || []
            // 设置节点默认选中
            node.checked = releasedCases.length === caseIds.length
            node.indeterminate = !node.checked && releasedCases.length > 0
            // 设置默认选中节点用例数量和总数
            this.$set(node.data, 'num', releasedCases.length)
            this.$set(node.data, 'sum', caseIds.length)
            this.setParentNode(node)
          }
        })
      }
    },
    treeCaseClick(data) {
      this.currentNode = data
      this.getTableList(data)
    },
    async checkChange(row) {
      this.currentNode = row
      const node = this.$refs.tree.getNode(row.id)
      const checked = node.checked
      // 当前节点关联用例数
      const caseIds = this.allTreeCaseMap[row.id]?.caseIds || []
      caseIds.map(id => this.updatedCaseIds(id, checked))
      // 设置当前节点选中用例数
      this.releaseTreeMap[row.id] = checked ? caseIds : []
      this.$set(row, 'num', checked ? caseIds.length : 0)
      this.$set(row, 'sum', caseIds.length)
      // 设置父节点选中
      this.setParentNode(node)
      // 设置子节点选中和用例数量
      this.setChildNodesCheck(node.childNodes, checked)
      // 查询当前节点下所有用例
      this.getTableList(row)

      this.$refs.tree.setCurrentKey(row.id)
    },
    // 递归设置子节点节点半选
    async setChildNodesCheck(childNodes, checked = false) {
      childNodes?.map(node => {
        node.checked = checked
        checked && (node.indeterminate = false)
        const caseIds = this.allTreeCaseMap[node.data.id]?.caseIds || []
        // 添加子节点选中用例
        caseIds.map(id => this.updatedCaseIds(id, checked))
        this.releaseTreeMap[node.data.id] = checked ? caseIds : []
        this.$set(node.data, 'num', checked ? caseIds.length : 0)
        this.$set(node.data, 'sum', caseIds.length)
        this.setChildNodesCheck(node.childNodes, checked)
      })
    },
    // 设置父节点半选
    async setParentNode(node) {
      let parent = node.parent
      while (parent && parent.level > 0) {
        const num = parent.data.num || 0
        if (node.checked) {
          const sum = parent.data.sum != undefined ? parent.data.sum : this.allTreeCaseMap[parent.data.id]?.caseIds?.length || 0
          // 当前节点所有用例是否已全部关联
          const allCaseSelected = num === sum
          // 子节点是否全选
          const allChildChecked = parent.childNodes.every(item => item.checked)
          parent.checked = allCaseSelected && allChildChecked
        } else {
          parent.checked = false
        }
        // 子节点存在选中状态
        const childHalfChecked = parent.childNodes.some(item => item.indeterminate || item.checked)
        parent.indeterminate = !parent.checked && (childHalfChecked || num > 0)
        node = parent
        parent = parent.parent
      }
    },
    // 查询表格列表
    async getTableList(data) {
      // 当前节点不存在时，不查询
      if (!this.currentNode.id) return
      const params = {
        current: 1,
        size: 9999,
        order: 'descending',
        sort: 'updateTime',
        extra: {},
        model: {
          state: false,
          draft: false, // 筛除草稿状态用例
          ...this.formData,
          planId: this.planData.id,
          libraryId: this.libraryId,
          treeId: data.id
        }
      }
      this.tableLoading = true
      const [res, err] = await catchErr(finddelCaseByIdPage(params))
      if (err) return
      this.tableLoading = false
      if (res.isSuccess) {
        this.tableData = res.data
        const tableList = res.data.records
        Array.isArray(tableList) && tableList.forEach((v) => {
          v.userData = v.echoMap.execBy
        })
        // 清除选中
        this.clearSelection()
        if (tableList.length === 0) return
        // 当前树节点已选择的用例，设置表格默认选中
        const list = this.releaseTreeMap[data.id] || []
        const checkedDefaults = this.tableData.records.filter(v => list.indexOf(v.id) > -1)

        this.$nextTick(() => {
          this.$refs.projectReleaseCaseTable.setCheckboxRow(checkedDefaults, true)
        })
      }
    },
    onClose() {
      // 取消选择
      this.clearSelection()
      this.$emit('update:visible', false)
      this.tableData = { records: [] }
      this.currentNode = {}
      // 清空选中节点
      this.$refs.tree.setCheckedKeys([])
    },
    // 保存选中用例
    async saveInfo() {
      if (this.selectedId.length === 0) {
        this.$message.warning('请选择用例添加')
        return
      }
      // 当前选中树节点
      const halfChecked = this.$refs.tree.getHalfCheckedKeys()
      const checked = this.$refs.tree.getCheckedKeys()
      // 默认选中用例
      const defaultReleasedCase = this.releasedCase.map(item => item.id)
      // 新关联用例
      const caseIds = this.selectedId.filter((id) => defaultReleasedCase.indexOf(id) === -1)
      const treeIds = [...new Set([...checked, ...halfChecked])]
      const treeMap = treeIds.map(id => this.allTreeCaseMap[id] || {})
      this.$emit('success', { caseIds, treeIds, treeMap, execBy: this.excuteUser })
      this.onClose()
    },
    // 更新选中的用例id
    updatedCaseIds(id, checked) {
      let index = this.selectedId.indexOf(id)
      if (checked) {
        index === -1 && this.selectedId.push(id)
      } else {
        while (index > -1) {
          this.selectedId.splice(index, 1)
          index = this.selectedId.indexOf(id)
        }
      }
    },
    // 取消选择
    clearSelection() {
      this.$refs.projectReleaseCaseTable.clearCheckboxRow()
    },
    selectAllEvent({ checked }) {
      const selection = this.$refs.projectReleaseCaseTable.getCheckboxRecords()
      const isSelect = selection.length > 0
      this.tableData.records.map(row => this.updatedCaseIds(row.id, isSelect))

      this.updateNodeChecked(selection)
    },
    selectChangeEvent({ row }) {
      const selection = this.$refs.projectReleaseCaseTable.getCheckboxRecords()
      const isAdd = selection.indexOf(row) > -1
      this.updatedCaseIds(row.id, isAdd)

      this.updateNodeChecked(selection)
    },
    // 修改当前树节点选中状态
    async updateNodeChecked(selection) {
      const isSelect = selection.length > 0
      const selectAll = this.tableData.records.length === selection.length // 用例全选

      const node = this.$refs.tree.getNode(this.currentNode.id)
      const caseIds = selection.map(item => item.id)
      // 设置当前节点显示用例数量
      this.$set(node.data, 'num', selection.length)
      // 更新关联的用例
      this.releaseTreeMap[this.currentNode.id] = caseIds

      // 根据当前节点下子节点选中状态
      let allChildChecked = true
      let someChildChecked = false
      if (node.childNodes.length > 0) {
        allChildChecked = node.childNodes.every(item => item.checked) // 子节点是否全选
        someChildChecked = node.childNodes.some(item => item.indeterminate || item.checked) // 子节点存在选中状态
      }
      node.checked = selectAll && allChildChecked
      node.indeterminate = !node.checked && (someChildChecked || isSelect)

      // 设置父节点选中状态
      this.setParentNode(node)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-dialog__body {
    padding: 0;
  }
  .el-table::before {
    width: 0;
  }

  .pagination {
    margin: 0;
  }
}
.elStyle {
  height: 516px;
  padding: 16px 0 0 16px;
  overflow: auto;
  :deep(.el-tree-node > .el-tree-node__children) {
    min-width: min-content;
  }
}

.r_body {
  padding: 16px;
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
}
.custom-tree {
  // height: calc(100vh - 220px);
  overflow-y: overlay;
  padding-right: 10px;
  :deep(.el-tree > .el-tree-node) {
    display: grid;
    min-width: 100%;
    overflow-x: auto;
  }
  :deep(.el-tree-node > .el-tree-node__children) {
    display: inline;
    overflow: unset;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }
  .iconCls {
    margin: 0 3px;
  }

  .treeNode {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .num {
    padding: 0px 8px;
    flex: 0 0 auto;
    margin-left: 4px;
    min-width: 24px;
    background-color: rgb(212, 216, 222);
    border-radius: 8px;
    text-align: center;
    color: rgb(255, 255, 255);
    height: 16px;
    line-height: 16px;
  }
}

.excuteUser {
  width: 350px;
  float: left;
  gap: 0 8px;
  display: flex;
  align-items: center;
  color: var(--sub-font-color);
  .label {
    width: 280px;
  }
}
</style>
