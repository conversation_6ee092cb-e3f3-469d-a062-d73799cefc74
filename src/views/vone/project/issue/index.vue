<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="issue-table"
          v-model:model="formData"
          v-model:extra="extraData"
          :table-ref="$refs['issue-table']"
          show-grouping
          :grouping-options="tableOptions.groupingOptions"
          :hide-columns="tableOptions.hideColumns"
          v-model:default-fileds="defaultFileds"
          show-basic
          :show-column-sort="false"
          @getTableData="getInitTableData"
          @onTypeChange="onTypeChange"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <simpleAddIssue
            v-if="createSimple"
            :type-code="'ISSUE'"
            :biz-type="'ISSUE_FILE_UPLOAD'"
            @success="getInitTableData"
            @cancel="createSimple = false"
          />
          <div>
            <el-button-group class="ml-16">
              <el-tooltip content="快速新增" placement="top">
                <el-button
                  :disabled="!$permission('project_issue_add')"
                  class="subBtton"
                  :icon="`iconfont  ${
                    createSimple ? 'el-icon-direction-double-left' : 'el-icon-direction-double-down'
                  }`"
                  type="primary"
                  @click.stop="createSimple = !createSimple"
                />
              </el-tooltip>
              <el-button
                icon="iconfont el-icon-tips-plus-circle"
                type="primary"
                :disabled="!$permission('project_issue_add')"
                @click.stop="newIssue"
                >新增</el-button
              >
            </el-button-group>
          </div>
        </el-row>
        <el-dropdown trigger="click" @command="e => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more" /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{ height: tableHeight }">
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :cell-class-name="cellShow"
        :tree-config="{
          transform: false,
          rowField: 'id',
          parentField: 'parentId',
          hasChild: 'hasChildren',
          lazy: true,
          loadMethod: ({ row }) => getClidData(row, 1)
        }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @resizable-change="({ column }) => resizableChangeEvent(column, 'issue-table')"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          min-width="480"
          fixed="left"
          class-name="name_col custom-title-style"
          show-overflow="ellipsis"
          tree-node
        >
          <template #default="{ row }">
            <span v-if="row.delay && !row.groupType" style="position: absolute; left: 0">
              <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top" :visible-arrow="false">
                <i class="el-icon-warning-outline color-danger ml-2" />
              </el-tooltip>
            </span>
            <el-tooltip
              v-if="!row.groupType"
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`
                  }"
                />
                <span class="custom-title-style-text">{{ row.code + ' ' + row.name }}</span>
              </span>
            </el-tooltip>
            <span v-else>
              {{ row.name }}
              <span class="count-num">
                {{ row.count }}
              </span>
            </span>
            <span
              v-if="!row.groupType"
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' -4px',
                right: '-40px',
                display: copyRow && copyRow.id == row.id ? 'block' : ''
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="e => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown" class="custom-title-copy-dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" command="title">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" command="code">
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120" :edit-render="{}">
          <template #default="{ row }">
            <issueStatus
              v-if="row && !row.groupType"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('project_issue_flow')"
              @changeFlow="getInitTableData"
            />
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.leadingBy"
                  class="remoteuser"
                  :project-id="projectId"
                  :default-data="[row.echoMap.leadingBy]"
                  :disabled="row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                  @change="workitemChange(row, $event, 'leadingBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.handleBy"
                  :project-id="projectId"
                  class="remoteuser"
                  :default-data="[row.echoMap.handleBy]"
                  :disabled="row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                  @change="workitemChange(row, $event, 'handleBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.putBy"
                  :project-id="projectId"
                  class="remoteuser"
                  :default-data="[row.echoMap.putBy]"
                  :disabled="row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                  @change="workitemChange(row, $event, 'putBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>

        <vxe-column title="计划开始时间" field="planStime" width="135">
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="135">
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="需求来源" field="sourceCode">
          <template #default="{ row }">
            <span v-if="row.sourceCode && row.echoMap && row.echoMap.sourceCode">
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column title="进度" field="rateProgress" width="100">
          <template #default="{ row }">
            <template v-if="!row.groupType">
              <el-tooltip placement="top" :content="` ${row.rateProgress ? row.rateProgress : 0}%`">
                <el-progress
                  :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                  color="var(--main-theme-color,#3e7bfa)"
                  :show-text="false"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="100">
          <template #default="{ row }">
            <vone-icon-select
              v-if="!row.groupType"
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('project_issue_priority_update')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px'
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>

        <vxe-column
          field="planId"
          :title="$route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑'"
          width="100"
        >
          <template #header>
            <span>{{ $route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑' }}</span>
          </template>
          <template #default="{ row }">
            <span v-if="row.planId && row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column field="productId" title="关联产品" min-width="150">
          <template #default="{ row }">
            <template v-if="!row.groupType">
              {{ row.echoMap && row.echoMap.productId && row.echoMap.productId.name }}
            </template>
          </template>
        </vxe-column>
        <vxe-column field="testPlanId" title="测试计划">
          <template #default="{ row }">
            <span v-if="row.echoMap && row.echoMap.testPlan">
              <a @click="pushToPlan(row)"> {{ row.echoMap.testPlan.name }}</a>
            </span>
            <span v-else> {{ row.testPlanId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                icon="iconfont el-icon-application-edit"
                @click="editIssue(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('project_issue_del')"
                icon="iconfont el-icon-application-delete"
                @click="deleteIssue(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e(row)">
              <el-button type="text" icon="iconfont el-icon-application-more" class="operation-dropdown" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="iconfont el-icon-edit-character-b" :command="() => titleCopy(row, 'code')">
                  <span>复制编号</span>
                </el-dropdown-item>
                <el-dropdown-item
                  icon="iconfont el-icon-application-copy-content"
                  :command="() => titleCopy(row, 'title')"
                >
                  <span>复制标题</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('project_issue_add')"
                  icon="iconfont el-icon-icon-fuzhi"
                  :command="() => workItemCopy(row)"
                >
                  <span>复制工作项</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                  icon="iconfont el-icon-application-type"
                  :command="() => typeCodeChangeFn(row)"
                >
                  <span>变更类型</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!!(row.typeCode != 'EPIC' && row.typeCode != 'SOR') || !$permission('project_issue_break')"
                  icon="iconfont el-icon-application-split-up"
                  :command="() => issueMore(row)"
                >
                  <span>需求拆分</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />

    <!-- 新增 -->
    <vone-custom-add
      ref="vone-custom-add"
      v-if="issueAddParam.visible"
      :key="issueAddParam.key"
      v-model="issueAddParam.visible"
      v-bind="issueAddParam"
      :type-code="globalTypeCode"
      :is-tooltip="true"
      :title="issueAddParam.title"
      @success="saveChildSuccess"
    />

    <!-- 编辑需求 -->
    <vone-custom-edit
      ref="vone-custom-edit"
      v-if="issueParam.visible"
      :key="issueParam.key"
      v-model="issueParam.visible"
      v-bind="issueParam"
      :type-code="'ISSUE'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({})"
      @add-child="addChild"
    />

    <!-- 需求详情 -->
    <vone-custom-info
      ref="vone-custom-info"
      v-if="issueInfoParam.visible"
      :key="issueInfoParam.key"
      v-model="issueInfoParam.visible"
      v-bind="issueInfoParam"
      :type-code="'ISSUE'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({}, 'edit')"
      @add-child="addChild"
    />

    <!-- 史诗拆分需求 -->
    <epicToIssue
      v-if="epicToIssueParam.visible"
      v-model="epicToIssueParam.visible"
      v-bind="epicToIssueParam"
      @success="getInitTableData({}, 'edit')"
      @initList="getInitTableData({}, 'edit')"
    />

    <!-- 导入需求 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getInitTableData({}, 'edit')"
    />
    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model="editAllParam.visible"
      :type-code="'ISSUE'"
      @success="getInitTableData({}, 'edit')"
    />
    <!-- 变更工作项类型 -->
    <type-code-change
      v-if="typeCodeChangeParam.visible"
      v-bind="typeCodeChangeParam"
      v-model="typeCodeChangeParam.visible"
      @success="getInitTableData({}, 'edit')"
    />
  </page-wrapper>
</template>

<script>
import {
  apiAlmGetInfo,
  apiAlmIssueDel,
  apiAlmIssueInfo,
  apiAlmIssuePage,
  apiAlmSourceNoPage,
  getGroup
} from '@/api/vone/project/issue'
import {apiAlmGetTypeNoPage, apiAlmPriorityNoPage} from '@/api/vone/alm/index'
import {editById, getWorkItemState, productListByCondition} from '@/api/vone/project/index'
import {catchErr, download, gainTreeList} from '@/utils'
import {apiBaseFileLoad} from '@/api/vone/base/file'
import {queryFieldList} from '@/api/common'

// 组件
import simpleAddIssue from './function/simple-add-issue.vue'

import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import epicToIssue from './function/epic-to-issue.vue'

import editAll from '../common/edit-all'
// import tagSelect from '@/components/CustomEdit/components/tag-select'
import typeCodeChange from '@/components/CustomEdit/components/type-code-change'
import {apiAlmProjectPlanNoPage} from '@/api/vone/project/iteration'
import {getModule} from '@/api/vone/product/index'
import dayjs from 'dayjs'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    // tagSelect,
    issueStatus,
    simpleAddIssue,
    epicToIssue,
    typeCodeChange,
    editAll
  },
  mixins: [setDataMixin],
  data() {
    return {
      globalTypeCode: 'ISSUE',
      defaultFileds: [],
      tableList: [], // 用于编辑时切换上一个下一个
      typeCodeList: [],
      prioritList: [],
      stateCodeList: [],
      selecteTableData: [],
      formData: {},
      extraData: {},
      tableLoading: false,
      createSimple: false,
      tableData: {
        records: []
      },
      tableOptions: {
        isOperation: true,
        isSelection: true,
        hideColumns: [
          'files',
          'code',
          'description',
          'delay',
          'estimatePoint',
          'planStime',
          'projectId',
          'ideaId',
          'sourceCode',
          'rateProgress',
          'typeCode',
          'putBy',
          'leadingBy'
        ], // 默认隐藏列
        isGrouping: true,
        groupingOptions: [
          { name: '按处理人', value: 'handle_by', key: 'handleBy' },
          { name: '按负责人', value: 'leading_by', key: 'leadingBy' },
          { name: '按产品', value: 'product_id', key: 'productId' },
          { name: '按迭代', value: 'plan_id', key: 'planId' },
          {
            name: '按计划完成时间',
            value: 'date(plan_etime)',
            key: 'planEtime'
          }
        ]
      },
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete',
          fn: this.deleteAll,
          disabled: !this.$permission('project_issue_del')
        },
        {
          name: '批量编辑',
          // icon: 'iconfont el-icon-application-edit',
          fn: this.editAll,
          disabled: !this.$permission('project_issue_edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('project_issue_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('project_issue_export')
        }
      ],
      issueAddParam: {
        // 新增
        visible: false
      },
      issueParam: {
        // 编辑
        visible: false
      },
      issueInfoParam: {
        // 详情
        visible: false
      },
      epicToIssueParam: {
        visible: false
      },
      importParam: { visible: false }, // 用户导入
      editAllParam: { visible: false }, // 批量编辑
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '变更记录',
          name: 'activityRecord'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [
        {
          label: '需求',
          name: 'IssueToIssue'
        },
        {
          label: '关联任务',
          name: 'TaskTab'
        },
        {
          label: '关联缺陷',
          name: 'BugTab'
        },
        {
          label: '关联代码',
          name: 'DevelopTab'
        },
        {
          label: '测试用例',
          name: 'TestCase'
        }
      ],
      rowData: {},
      formContainer: {},
      timeStamp: '',

      typeCodeChangeParam: {
        visible: false
      },
      copyRow: null,
      initRequest: true, // 第一次调用，回显功能模块
      projectId: this.$route.params?.id || ''
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  watch: {
    $route: {
      handler(val) {
        if (val.query && val.query.queryId && val.query.type == 'edit') {
          this.editIssue({
            id: val.query.queryId,
            typeCode: val.query.rowTypeCode,
            stateCode: val.query.stateCode
          })
        } else if (val.query && val.query.queryId) {
          this.showInfo({
            id: val.query.queryId,
            typeCode: val.query.rowTypeCode,
            stateCode: val.query.stateCode
          })
        }
      },
      // 一进页面就执行
      immediate: true,
      // 深度观察监听
      deep: true
    }
  },
  // 路由离开生命周期函数
  beforeRouteLeave(to, from, next) {
    // 即将跳转的路由地址
    if (to.path != 'project_issue_view') {
      this.$store.state.project.itemName = undefined
      next()
    }
  },
  created() {
    // this.getQueryFieldList()
  },
  mounted() {
    this.getQueryFieldList()
  },
  methods: {
    saveChildSuccess() {
      this.getInitTableData({})
      this.$refs['vone-custom-info']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      this.$refs['vone-custom-info']?.$refs['TaskTab']?.[0]?.getTableData()
      this.$refs['vone-custom-info']?.$refs['BugTab']?.[0]?.getTableData()
      this.$refs['vone-custom-edit']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      this.$refs['vone-custom-edit']?.$refs['TaskTab']?.[0]?.getTableData()
      this.$refs['vone-custom-edit']?.$refs['BugTab']?.[0]?.getTableData()
    },
    addChild(type) {
      if (type == 'issue') {
        this.globalTypeCode = 'ISSUE'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增需求'
        }
      } else if (type == 'task') {
        this.globalTypeCode = 'TASK'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增任务'
        }
      } else if (type == 'bug') {
        this.globalTypeCode = 'BUG'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增缺陷'
        }
      }

      setTimeout(async () => {
        // 获取需求详情
        const res = await apiAlmGetInfo(`/api/alm/alm/requirement/${this.issueParam.visible ? this.issueParam.id : this.issueInfoParam.id}`)

        // 基本信息赋值
        const fixdForm = this.$refs['vone-custom-add']?.fixdForm
        this.$set(fixdForm, 'name', res.data.name)
        this.$set(fixdForm, 'planEtime', res.data.planEtime)
        this.$set(fixdForm, 'description', res.data.description)

        // 自定义组件集合
        const customList = this.$refs['vone-custom-add'].customList || []
        // 基本属性赋值
        const form = this.$refs['vone-custom-add']?.form
        if (type === 'issue') {
          this.$set(form, 'parentId', res.data.id)
          this.$set(form, 'sourceCode', res.data.sourceCode)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(form, 'productModuleFunctionId', res.data.productModuleFunctionId)
        } else if (type === 'task') {
          this.$set(form, 'sourceCode', customList.find(item => item.key === 'sourceCode')?.options?.[0]['code'])
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(form, 'productRepairVersionId', res.data.productRepairVersionId)
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'c4', res.data.putBy)
        } else if (type === 'bug') {
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'sourceCode', customList.find(item => item.key === 'sourceCode')?.options?.[0]['code'])
          this.$set(form, 'envCode', customList.find(item => item.key === 'envCode')?.options?.[0]['id'])
        }
      }, 500)
    },
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    getDaley() {
      const delay = [
        { name: '是', code: true, id: '1' },
        { name: '否', code: false, id: '2' }
      ]
      this.setData(this.defaultFileds, 'delay', delay)
    },
    typeCodeChangeFn(e) {
      this.typeCodeChangeParam = {
        visible: true,
        dataId: e.id,
        typeClassfiy: 'ISSUE'
      }
    },
    workItemCopy(e) {
      this.createSimple = false
      this.issueAddParam = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: e.typeCode,
        title: '复制需求',
        id: e.id
      }
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('requirement', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },

    handleSelectionChange(e, row) {
      this.selecteTableData = Array.from(new Set([...this.selecteTableData, ...e]))
    },
    childSelectChange({ checked, row }) {
      if (checked) {
        this.selecteTableData.push(row)
      } else {
        this.selecteTableData = this.selecteTableData.filter(el => el.id != row.id)
      }
    },
    handlePageChange(e, row) {
      row.load = false
      this.getClidData(row, e)
    },
    getClidData(row, page) {
      if (row.load) return
      row.loading = true
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20
      }

      const groupMap = this.tableOptions.groupingOptions.reduce((r, v) => (r[v.value] = v.key) && r, {})
      const formParams = {}
      this.$set(formParams, 'projectId', [this.$route.params.id])
      if (groupMap[row.groupType] == 'planEtime') {
        this.$set(formParams, 'planEtime', {
          start: row.id + ' 00:00:00',
          end: row.id + ' 23:59:59'
        })
      } else {
        this.$set(formParams, groupMap[row.groupType], [row.id])
      }
      const params = {
        ...tableAttr,
        current: page || 1,
        order: 'descending',
        size: 99999,
        sort: 'createTime',
        extra: {},
        model: { ...this.formData, ...formParams }
      }

      return apiAlmIssuePage(params)
        .then(res => {
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          res.data.records.forEach(element => {
            element.tag =
              element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
                ? element.echoMap.tagId.map(r => r.name)
                : []
          })
          row.load = true
          row.loading = false
          this.$nextTick(() => {
            row.childrenData = res.data.records || []
            row.pageData = res.data
            row.count = res.data.total
            this.timeStamp = new Date().valueOf()
          })
          return res.data.records
        })
        .catch(() => {
          this.$set(row, 'loading', false)
        })
    },
    handleRow(index, row, item, table) {
      if (typeof item.handler === 'function') {
        item.handler(row, index)
      }
      this.rowData = table
    },
    isDisabled(scope, { disabled }) {
      if (typeof disabled === 'function') {
        disabled = disabled(scope.$index, scope.row)
      }
      return !!disabled
    },
    cellShow(row) {
      if (
        (row.row.groupType && row.columnIndex === 0) ||
        (!row.row.groupType && row.columnIndex === 1) ||
        (row.row.groupType && row.column.label === '操作')
      ) {
        return 'hide-node'
      }
    },
    expandChange(row) {
      this.getClidData(row, 1)
    },
    // 查询需求类型
    async getIssueType() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, 'ISSUE')
      if (!res.isSuccess) {
        return
      }
      // this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },

    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 迭代计划
    async getplanId() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'planId', res.data)
    },
    // 需求拆分
    issueMore(row) {
      this.epicToIssueParam = { visible: true, rowId: row.id }
    },
    getStatus(index, row) {
      return !!(row.typeCode != 'EPIC' && row.typeCode != 'SOR') || !this.$permission('project_issue_break')
    },
    getStatusCode(index, row) {
      return row?.stateCode == 'DONE' || !this.$permission('project_issue_edit')
    },
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs['issue-table'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs['issue-table'].getCheckboxRecords()
    },
    // 初始化进入页面列表
    async getInitTableData(e, type) {
      if (this.extraData && this.extraData.groupView && this.extraData.groupView != 'none') {
        this.groupingCommand(this.extraData.groupView)
        return
      }

      this.$set(this.formData, 'projectId', [this.$route.params.id])

      if (e && Object.keys(e).length !== 0) {
        this.formContainer = e
      }
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      // const sortObj = this.$refs.searchForm?.sortObj
      if (this.initRequest && this.formData?.productId) {
        this.getModuleList(this.formData.productId?.[0])
        this.initRequest = false
      }
      if (this.formData?.productModuleFunctionId && typeof this.formData?.productModuleFunctionId == 'string') {
        this.formData.productModuleFunctionId = [this.formData?.productModuleFunctionId]
      }
      const params = {
        ...tableAttr,
        // ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      if (this.formData.createTime && this.formData.createTime.length > 0) {
        params.model.createTime = {
          start: this.formData.createTime[0],
          end: this.formData.createTime[1]
        }
      }
      if (!this.formContainer.grouping || this.formContainer.grouping == 'none') {
        this.tableLoading = true
        // this.$set(this.formData, 'projectId', this.$route.params.id)

        const res = await apiAlmIssuePage(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        res.data.records.forEach(element => {
          element.tag =
            element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
              ? element.echoMap.tagId.map(r => r.name)
              : []
        })
        this.tableData = res.data
        this.tableList = res.data.records // 用于编辑时切换上一个下一个IssueStatus
      } else {
        if (type == 'edit') {
          this.rowData.load = false
          this.getClidData(this.rowData, this.rowData.pageData.current * 1)
        }
      }
    },
    // 更新当前表格状态
    async editRowStatus(row, index, table) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIssueInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(res.data, 'tag', res.data.echoMap.tagId ? res.data.echoMap.tagId.map(r => r.name) : [])
      }
      this.$set(row, 'stateCode', res.data.stateCode)
      this.$set(row, 'echoMap', res.data.echoMap)
      // if (table) {
      //   table.childrenData.splice(index, 1, res.data)
      // } else {
      //   this.tableData.records.splice(index, 1, res.data)
      // }
    },
    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this
      const message = type == 'code' ? row.code : row.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    newIssue() {
      // 隐藏快速新增
      this.createSimple = false
      this.issueAddParam = {
        visible: true,
        key: Date.now(),
        title: '新增需求',
        infoDisabled: false
      }
    },
    editIssue(row) {
      this.issueParam = {
        visible: true,
        title: '编辑需求',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        // 拆分子项是否弹框
        showPopupForSplit: true
      }
    },
    showInfo(row) {
      this.issueInfoParam = {
        visible: true,
        title: '需求详情',
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        // 拆分子项是否弹框
        showPopupForSplit: true
      }
    },
    // 删除
    async deleteIssue(row) {
      try {
        await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })

        const { isSuccess, msg } = await apiAlmIssueDel([row.id])
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }
        this.$message.success('删除成功')
        this.getInitTableData()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 批量删除
    async deleteAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除当前数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        this.tableLoading = true
        const selectId = []
        this.selecteTableData.forEach(r => {
          const code = r.echoMap?.typeCode?.classify?.code
          if (code) {
            selectId.push(r.id)
          }
        })
        const res = await apiAlmIssueDel(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getInitTableData()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '项目-需求',
        url: '/api/alm/alm/requirement/excel/downloadImportTemplate',
        importUrl: `/api/alm/alm/requirement/excel/import?projectId=${this.$route.params.id}`
      }
    },

    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true

        download(`需求信息.xls`, await apiBaseFileLoad('/api/alm/alm/requirement/excel/export', this.formData))

        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
    // 批量编辑
    editAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selecteTableData }
    },
    // 跳转到测试计划
    pushToPlan(row) {
      // this.$router.push('project_test_plan_view')

      this.$router.push({
        name: 'project_test',
        projectTypeCode: this.$route.params.projectTypeCode,
        id: this.$route.params.id,
        query: {
          planId: row.echoMap?.testPlan?.id
        }
      })
    },
    async getQueryFieldList(typeCodes) {
      const fixedField = ['name', 'handleBy', 'stateCode', 'tagId', 'createTime', 'typeCode']
      const form = {
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE',
        typeCodes: typeCodes
      }
      const res = await queryFieldList(form)
      if (!res.isSuccess) {
        return
      }
      const vId = ['productId', 'planId']
      const filter = res.data.filter(r => r.isSearch && r.key != 'projectId')
      filter.forEach(element => {
        if (element.key == 'productModuleFunctionId') {
          element.type.code = 'TREE'
        }
        element.isBasicFilter = !fixedField.includes(element.key)
        element.multiple = element.type.code != 'ICON'

        element.valueType = vId.includes(element.key) ? 'id' : null
      })
      this.defaultFileds = filter
      this.getOptions()
    },
    getOptions() {
      this.getPrioritList()
      this.getIssueType()
      this.getAllStatus()
      this.getDaley()
      this.getsourceCode()
      this.productList()
      this.getplanId()
    },
    // 查询来源
    async getsourceCode() {
      // if (this.maps['sourceCode'].length > 0) return
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'sourceCode', res.data)
    },
    // 归属产品
    async productList() {
      // if (this.maps['productId']?.length > 0) return
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productId', res.data)
    },
    // 产品模块功能
    async getModuleList(value) {
      const isArray = typeof value == 'object' && Array.isArray(value)
      // 查询产品功能模块
      const res = await getModule({
        model: {
          productId: isArray ? value.join(',') : value
        }
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productModuleFunctionId', gainTreeList(res.data))
    },
    onTypeChange(item) {
      // this.getQueryFieldList(item)
      if (item.key == 'productId') {
        if (item.value?.length != 0) {
          this.getModuleList(item.value)
        }
      }
    },
    // 分组切换
    async groupingCommand() {
      console.log(this.formData)
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const params = {
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await getGroup(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(e => {
        if (this.groupItem?.type === 'time') {
          e.bizId = e.bizId ? dayjs(e.bizId).format('YYYY-MM-DD') : '未规划时间'
        }
        e.name = e.data?.name || e.bizId
        e.id = e.bizId
        e.hasChildren = e.count > 0
      })
      this.tableData = res.data
    }
  }
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.noSetting {
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
:deep(.hide-checkbox) {
  .el-checkbox__input {
    display: none;
  }
}
.count-num {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  color: #838a99;
  padding: 0px 6px;
  background: #f2f3f5;
  border-radius: 9px;
  margin-left: 8px;
}
.child-table {
  font-size: 14px;
}
</style>
<style>
.userList .el-input__inner {
  padding: 0;
  border: 0;
  padding-left: 25px;
}
.userList .el-input__icon {
  display: none;
}
.userList .el-input__prefix {
  left: 0px;
}
</style>
