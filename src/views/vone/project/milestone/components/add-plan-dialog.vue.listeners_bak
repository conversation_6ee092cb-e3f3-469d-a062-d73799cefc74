<template>
  <div>
    <el-dialog title="新增里程碑" width="520px" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" v-on="$listeners">
      <el-form ref="iterationForm" v-loading="formLoading" :model="iterationForm" :rules="iterationRules">
        <el-form-item v-if="hasParent" label="父里程碑" prop="parentId">
          <el-select v-model="iterationForm.parentId" placeholder="请选择父里程碑" style="width: 90%" filterable disabled>
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="iterationForm.name" placeholder="请输入名称" style="width: 90%; margin-right: 10px" />
          <el-tooltip placement="top" content="里程碑名称不超过30个字符">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="parentList.length" label="添加在哪个阶段后" prop="sort">
          <el-select v-model="iterationForm.sort" placeholder="请选择添加在哪个阶段后" style="width: 90%" filterable>
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.sort" />
          </el-select>
        </el-form-item>

        <el-form-item label="开始日期" prop="planStime">
          <el-date-picker v-model="iterationForm.planStime" type="date" placeholder="请选择开始日期" value-format="yyyy-MM-dd" :default-value="timeDefaultShowStart" :picker-options="pickerOptionsStart" style="width: 90%" :editable="false" :disabled="disabledStart" @change="changeTime" />
          <el-tooltip style="margin-left: 10px" placement="top" content="请先确定项目开始时间">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="结束日期" prop="planEtime">
          <el-date-picker v-model="iterationForm.planEtime" value-format="yyyy-MM-dd" :picker-options="pickerOptions" type="date" placeholder="请选择结束日期" style="width: 90%" :editable="false" :disabled="disabledEnd" :default-value="timeDefaultShow" @change="changeEnd" />
          <el-tooltip style="margin-left: 10px" placement="top" content="请先确定项目结束时间">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </el-form-item>

        <el-form-item label="负责人" prop="leadingBy">
          <vone-icon-select v-model="iterationForm.leadingBy" select-type="user" clearable filterable :data="userList" style="width:90%">
            <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id">
              <vone-user-avatar
                v-if="item.echoMap"
                :avatar-path="item.avatarPath"
                :avatar-type="item.avatarType"
                :show-name="false"
                height="22px"
                width="22px"
              />
              {{ item.name }}
            </el-option>
          </vone-icon-select>
          <!-- <el-select v-model="iterationForm.leadingBy" placeholder="请选择负责人" style="width: 100%" filterable>
            <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
        </el-form-item>
        <el-form-item label="里程碑状态" prop="state" style="display: none">
          <el-select v-model="iterationForm.state" placeholder="请选择里程碑状态" style="width: 90%">
            <el-option label="关闭" value="-1" />
            <el-option label="未开始" value="0" />
            <el-option label="已开始" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="里程碑目标" prop="description">
          <el-input v-model="iterationForm.description" placeholder="请输入里程碑目标" style="width: 90%" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button :loading="saveLoading" type="primary" @click="sureAdd">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { apiAlmProjectPlanAdd, apiAlmPlanInfo, apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    hasParent: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: undefined
    },
    node: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      timeDefaultShow: new Date(),
      timeDefaultShowStart: new Date(),
      pickerOptionsStart: {
        disabledDate(time) {
          // var endTime=
          // return time.getTime() < startTime()
        }
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.iterationForm.planStime) {
            // this.timeDefaultShow = new Date()
            return time.getTime() < new Date(this.iterationForm.planStime).getTime()
          } else {
            // time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      formLoading: false,
      iterationForm: {
        projectId: this.$route.params.id,
        type: 'MILESTONE',
        stateCode: '1',
        sort: '1'
      },
      iterationRules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' }],
        planStime: [{ required: true, message: '请选择开始日期' }],
        planEtime: [{ required: true, message: '请选择结束日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        // state: [{ required: true, message: '请选择里程碑状态' }],
        sort: [{ required: true, message: '请选择添加在哪个阶段后' }],
        target: [{ pattern: '^.{1,225}$', message: '里程碑目标不超过225个字符' }]
      },
      saveLoading: false,
      userList: [],
      disabledStart: false,
      disabledEnd: false,
      parentList: []
    }
  },
  watch: {
    'iterationForm.planStime': function(val) {
      if (val) {
        this.timeDefaultShow.setFullYear(new Date(val).getFullYear())
        this.timeDefaultShow.setMonth(new Date(val).getMonth())
      }
    }
  },
  mounted() {
    this.getUserList()
    this.getTestPlanList()
    if (this.hasParent) {
      this.getTime()
    }
    if (this.id) {
      // 里程碑详情
      this.getPlanInfo()
    }
  },
  methods: {
    getTime() {
      var that = this
      // 新建子历程的时间选择
      if (this.node.planStime) {
        this.pickerOptionsStart = {
          disabledDate(time) {
            return time.getTime() < new Date(that.node.planStime).getTime() || time.getTime() > new Date(that.node.planEtime).getTime()
          }
        }
        this.timeDefaultShowStart.setFullYear(new Date(this.node.planStime).getFullYear())
        this.timeDefaultShowStart.setMonth(new Date(this.node.planStime).getMonth())
      }
      if (this.node.planEtime) {
        this.pickerOptions = {
          disabledDate(time) {
            return time.getTime() < new Date(that.node.planStime).getTime() || time.getTime() > new Date(that.node.planEtime).getTime()
          }
        }
      }
    },
    // 查询所有里程碑
    async getTestPlanList() {
      this.$set(this.iterationForm, 'sort', null)
      this.formLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id
      })
      this.formLoading = false

      if (!res.isSuccess) {
        return
      }
      this.parentList = res.data
      this.$set(this.iterationForm, 'parentId', this.parentId)
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.iterationForm.resetFields()
    },
    changeTime() {
      this.$set(this.iterationForm, 'planEtime', '')
      // this.$refs.iterationForm.clearValidate();

      this.$nextTick(() => {
        this.$refs.iterationForm.clearValidate(['planEtime'])
      })
    },
    changeEnd() {
      if (this.iterationForm.planStime && this.iterationForm.planStime > this.iterationForm.planEtime) {
        this.$message.warning('开始日期不能大于结束日期')
        this.changeTime()
      }
    },
    // 查询负责人下拉框数据
    async getUserList() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id
      })

      if (!res.isSuccess) {
        return
      }

      this.userList = res.data
    },
    async sureAdd() {
      this.saveLoading = true
      try {
        await this.$refs.iterationForm.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }

      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...this.iterationForm
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('保存成功')
      this.onClose()
      this.$emit('success')
    },
    async getPlanInfo() {
      this.formLoading = true
      const res = await apiAlmPlanInfo(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
    }
  }
}
</script>
