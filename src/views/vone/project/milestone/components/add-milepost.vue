<template>
  <div>
    <el-dialog title="新建里程碑" width="50%" v-model="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="iterationForm" v-loading="formLoading" label-position="top" :model="iterationForm" :rules="iterationRules">
        <el-form-item v-if="hasParent" label="父里程碑" prop="parentId">
          <el-select v-model="iterationForm.parentId" placeholder="请选择父里程碑" filterable disabled>
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="iterationForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="leadingBy">
              <vone-icon-select v-model="iterationForm.leadingBy" select-type="user" clearable filterable :data="userList">
                <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id">
                  <vone-user-avatar
                    :avatar-path="item.avatarPath"
                    :avatar-type="item.avatarType"
                    :show-name="false"
                    height="22px"
                    width="22px"
                  />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="stateCode">
              <el-select v-model="iterationForm.stateCode" placeholder="请选择里程碑状态">
                <el-option label="未开始" value="1" />
                <el-option label="进行中" value="2" />
                <el-option label="已完成" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="完成日期" prop="planEtime">
              <el-date-picker v-model="iterationForm.planEtime" style="width: 100%" value-format="yyyy-MM-dd" :picker-options="pickerOptions" type="date" placeholder="请选择完成日期" :editable="false" :disabled="disabledEnd" :default-value="timeDefaultShow" @change="changeEnd" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item v-if="parentList.length" label="添加在哪个里程碑后" prop="sort">
              <el-select v-model="iterationForm.sort" placeholder="请选择添加在哪个里程碑后" filterable>
                <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.sort" />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="iterationForm.description" type="textarea" :rows="2" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button :loading="saveLoading" type="primary" @click="sureAdd">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { apiAlmProjectPlanAdd, apiAlmPlanInfo, apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    hasParent: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: undefined
    },
    node: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      timeDefaultShow: new Date(),
      timeDefaultShowStart: new Date(),
      pickerOptionsStart: {
        disabledDate(time) {
          // var endTime=
          // return time.getTime() < startTime()
        }
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.iterationForm.planStime) {
            // this.timeDefaultShow = new Date()
            return time.getTime() < new Date(this.iterationForm.planStime).getTime()
          } else {
            // time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      formLoading: false,
      iterationForm: {
        projectId: this.$route.params.id,
        type: 'MILESTONE',
        stateCode: '1',
        sort: '1',
        leadingBy: ''
      },
      iterationRules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' }],
        planEtime: [{ required: true, message: '请选择结束日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        stateCode: [{ required: true, message: '请选择里程碑状态' }],
        sort: [{ required: true, message: '请选择添加在哪个阶段后' }]
      },
      saveLoading: false,
      userList: [],
      disabledStart: false,
      disabledEnd: false,
      parentList: []
    }
  },
  watch: {
    'iterationForm.planStime': function(val) {
      if (val) {
        this.timeDefaultShow.setFullYear(new Date(val).getFullYear())
        this.timeDefaultShow.setMonth(new Date(val).getMonth())
      }
    }
  },
  mounted() {
    this.getUserList()
    this.getTestPlanList()
    if (this.hasParent) {
      this.getTime()
    }
    if (this.id) {
      // 里程碑详情
      this.getPlanInfo()
    }
  },
  methods: {
    getTime() {
      var that = this
      // 新建子历程的时间选择
      if (this.node.planStime) {
        this.pickerOptionsStart = {
          disabledDate(time) {
            return time.getTime() < new Date(that.node.planStime).getTime() || time.getTime() > new Date(that.node.planEtime).getTime()
          }
        }
        this.timeDefaultShowStart.setFullYear(new Date(this.node.planStime).getFullYear())
        this.timeDefaultShowStart.setMonth(new Date(this.node.planStime).getMonth())
      }
      if (this.node.planEtime) {
        this.pickerOptions = {
          disabledDate(time) {
            return time.getTime() < new Date(that.node.planStime).getTime() || time.getTime() > new Date(that.node.planEtime).getTime()
          }
        }
      }
    },
    // 查询所有里程碑
    async getTestPlanList() {
      this.$set(this.iterationForm, 'sort', null)
      this.formLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        type: 'MILESTONE'
      })
      this.formLoading = false

      if (!res.isSuccess) {
        return
      }
      this.parentList = res.data
      this.$set(this.iterationForm, 'parentId', this.parentId)
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.iterationForm.resetFields()
    },
    changeTime() {
      this.$set(this.iterationForm, 'planEtime', '')
      // this.$refs.iterationForm.clearValidate();

      this.$nextTick(() => {
        this.$refs.iterationForm.clearValidate(['planEtime'])
      })
    },
    changeEnd() {
      if (this.iterationForm.planStime && this.iterationForm.planStime > this.iterationForm.planEtime) {
        this.$message.warning('开始日期不能大于结束日期')
        this.changeTime()
      }
    },
    // 查询负责人下拉框数据
    async getUserList() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id
      })

      if (!res.isSuccess) {
        return
      }

      this.userList = res.data
    },
    async sureAdd() {
      this.saveLoading = true
      try {
        await this.$refs.iterationForm.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }

      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...this.iterationForm
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('保存成功')
      this.onClose()
      this.$emit('success')
    },
    async getPlanInfo() {
      this.formLoading = true
      const res = await apiAlmPlanInfo(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
    }
  }
}
</script>
