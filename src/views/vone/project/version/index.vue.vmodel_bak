<template>
  <div class="pageConetent">
    <section class="leftContent">
      <header>
        <span v-if="productName == '当前项目未关联产品'" class="titleDisabled">{{ productName }}</span>
        <!-- <strong v-else>  {{ productName }}</strong> -->
        <el-select v-else v-model="productId" filterable placeholder="请选择产品" @change="checkProduct">
          <!-- <div slot="prefix" class="selectPrefix">
            <el-button type="text">{{ isMain?'主':'辅' }}</el-button>
          </div> -->
          <el-option v-for="item in productList" :key="item.id" :value="item.id" :label="item.name" />

        </el-select>
      </header>
      <div v-if="planList.length" class="list-container">
        <div
          v-for="(item, index) in planList"
          :key="index"
          :class="{
            ['it_stateId_' + item.stateId]: true,
            selected: productVersionId === item.id,
          }"
          class="list_item"
          @click.stop="sendId(item)"
        >
          <el-row type="flex" justify="space-between" align="middle">
            <span class="name">{{ item.name }}</span>

            <div class="btn-status" :style="{ textAlign:'center', border:`1px solid ${ item.stateColor}`,color:`${item. stateColor}`}">
              {{ versionMap[item.stateId] || "未开始" }}

            </div>

          </el-row>

          <el-row class="mt-8" type="flex" justify="space-between" align="middle">
            <small class="it_item_time">
              {{ item.planStime | format }} -
              {{ item.planRtime | format }}
            </small>

          </el-row>
        </div>
      </div>
      <vone-empty v-else />
    </section>
    <section class="rightContent">
      <header>
        <span>{{ pageName }}</span>
        <el-button :disabled="!$permission('project_version_contact')" type="primary" size="mini" icon="iconfont  el-icon-tips-plus-circle" @click="addPlan">{{ btnName }}</el-button>
      </header>
      <div style="padding:16px;overflow: hidden;height:calc(100vh - 123px);">
        <vxe-table
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :loading="pageLoading"
          :empty-render="{ name: 'empty' }"
          :data="data"
          row-id="id"
          :column-config="{ minWidth:'120px' }"
        >
          <vxe-column field="name" :title="`${code!='WALL'?'迭代名称':'名称'}`" min-width="200" />
          <vxe-column field="state" title="状态" width="80">
            <template #default="{row}">
              <span v-if="row.stateCode && stateMap[row.stateCode] ">
                {{ stateMap[row.stateCode] }}
              </span>
              <span v-else>
                {{ row.stateCode }}
              </span>
            </template>
          </vxe-column>
          <vxe-column field="planStime" title="开始时间" width="90">
            <template #default="{row}">
              {{ row.planStime | format }}
            </template>
          </vxe-column>
          <vxe-column field="planEtime" title="结束时间" width="90">
            <template #default="{row}">
              {{ row.planEtime | format }}
            </template>
          </vxe-column>
          <vxe-column field="description" :title="`${code!='WALL'?'迭代目标':'目标'}`" />
          <vxe-column title="操作" fixed="right" align="left" width="80">
            <template #default="{ row }">
              <div class="operation-icon">
                <el-button type="text" size="small" style="padding:0;min-width:10px" icon="el-icon-link" :disabled="!$permission('project_version_cancle')" @click.native.prevent="deleteRow(row)" />
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </section>

    <!-- 关联迭代对话框 -->
    <versionPlan v-if="versionPlanParam.visible" :code="code" v-model:visible="versionPlanParam.visible" v-bind="versionPlanParam" @success="refresh" />
  </div>

</template>

<script>
const stateMap = {
  1: '未开始',
  2: '进行中',
  3: '已完成',
  4: '已归档'
}

const versionMap = {
  1: '规划中',
  2: '开发中',
  3: '发布成功',
  4: '发布失败'
}

import * as dayjs from 'dayjs'

import {
  getProductVersionList
} from '@/api/vone/product'
import {
  apiProjectInfo
} from '@/api/vone/project/index'
import {
  apiAlmProjectPlanNoPage,
  updateProjectPlan
} from '@/api/vone/project/iteration'
import versionPlan from './components/plan.vue'

export default {
  components: {
    versionPlan
  },
  filters: {
    format(val) {
      if (!val) return ''
      return dayjs(val).format('MM月DD日')
    }
  },
  data() {
    return {
      code: '',
      formData: {
        // readonly: false
      },
      btnName: '关联迭代',
      stateMap,
      versionMap,
      productVersionId: '', // 版本ID
      dialogFormVisible: false,
      formLoading: false,
      rules: {},
      planForm: {
        plan: []
      },
      plans: [],
      productName: '当前项目未关联产品',
      pageName: '',
      data: [],
      pageLoading: false,
      listLoading: false,
      saveLoading: false,
      versionPlanParam: { visible: false },
      planList: [],
      currentNodekey: '',
      productId: '',
      productList: []
    }
  },
  mounted() {
    this.code = this.$route.params.projectTypeCode
    this.getProjectInfo()

    this.btnName = this.$route.params.projectTypeCode == 'WALL' ? '关联里程碑' : '关联迭代'
  },
  methods: {
    checkProduct(val) {
      this.productList.forEach(v => {
        if (v.id == val) {
          this.isMain = !!v.isMain
        }
      })
      this.getProductVersion(val)
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.planForm.resetFields()
    },
    addPlan() {
      if (!this.planList.length) {
        this.$message.warning('当前项目无关联产品版本,请先创建产品版本')
        return
      }
      this.versionPlanParam = { visible: true, productVersionId: this.productVersionId }
    },

    // 取消迭代
    async deleteRow(row) {
      try {
        await this.$confirm(
          `确定要取消【${row.name}】吗？`,
          '取消迭代',
          {
            type: 'warning'
          }
        )
        const res = await updateProjectPlan(this.productVersionId, 'cancel',
          [row.id]
        )
        if (!res.isSuccess) {
          this.$message.error(res.msg)
        }
        this.$message.success('取消迭代成功')
        this.getProjectInfo()
      } catch (error) {
        //
      }
    },
    sendId(val) {
      if (val.id == this.productVersionId) {
        return
      }
      this.pageName = val.name
      this.productVersionId = val.id
      this.getTableData(val.id)
    },
    // 查询项目详情
    async getProjectInfo() {
      this.listLoading = true
      const res = await apiProjectInfo(this.$route.params.id)
      this.listLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      this.isMain = !!res.data.hostProductId
      if (res.data && res.data.hostProductId) {
        // this.productName = res.data && res.data.echoMap ? res.data.echoMap.hostProduct.name : '当前项目未关联产品'
        // this.productId = res.data.hostProductId

        this.productList = [
          {
            id: res.data.echoMap.hostProduct.id,
            name: res.data.echoMap.hostProduct.name,
            isMain: true
          }
        ]
      }
      if (res.data.echoMap.assistProduct) {
        this.productList.push(...res.data.echoMap.assistProduct)
      }
      if (this.productList && this.productList.length > 0) {
        this.productName = this.productList[0].name
        this.productId = this.productList[0].id
        await this.getProductVersion(this.productList[0].id)
      } else {
        this.planList = []
        this.productName = '当前项目未关联产品'
      }
    },
    // 查询项目所属的产品的版本信息
    async getProductVersion(val) {
      const { data, isSuccess, msg } = await getProductVersionList({
        productId: val
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }

      const colorMap = {
        1: '#FFBF47',
        2: '#64BEFA',
        3: '#3CB540',
        4: '#FA6B57'
      }

      data.forEach(element => {
        element.stateColor = colorMap[element.stateId] || '#ccc'
      })
      this.planList = data
      if (!data.length) {
        return
      }
      this.getTableData(data[0].id)
      this.pageName = data[0].name
      this.productVersionId = data[0].id
    },
    // 刷新
    refresh() {
      this.getTableData(this.productVersionId)
    },

    // 获取表格数据
    getTableData(val) {
      this.pageLoading = true
      this.$set(this.formData, 'productVersionId', val)
      this.$set(this.formData, 'projectId', this.$route.params.id)
      // let params = {}
      // const tableAttr = this.$refs['plan_table'].exportTableQueryData()
      // params = {
      //   ...tableAttr,
      //   extra: {},
      //   model: { ...this.formData }
      // }
      apiAlmProjectPlanNoPage({
        productVersionId: val,
        projectId: this.$route.params.id
      }).then((res) => {
        if (!res.isSuccess) {
          this.pageLoading = false
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
      })
    }

  }
}
</script>

<style lang='scss' scoped>
.pageConetent {
  background-color: var(--main-bg-color);
  height: calc(100vh - 68px);
  border-radius: 4px;
  display: flex;
  .leftContent {
    width: 300px;
    border-right: 1px solid var(--disabled-bg-color);
		:deep(.el-input__inner) {
			padding:0
		}
    header {
      height: 56px;
      line-height: 56px;
      padding: 0 16px;
      border-bottom: 1px solid var(--disabled-bg-color);
      .titleDisabled {
        color: var(--auxiliary-font-color);
      }
      :deep(.el-input__prefix) {
        right: 40px;
        left: auto;
      }
			:deep(.el-input__suffix) {
				right:0;

			}
      :deep(.el-input__inner) {
        border: 0;
        padding-left: 0;
				font-size: 16px;
				font-weight:500;
      }
      .selectPrefix {
        :deep(.el-button) {
          min-width: auto;
          height: 18px;
					padding:0 6px;
          border: 1px solid #f53ba1;
          color: #f53ba1;
					border-radius: 2px;
					font-size: 12px;
					font-weight: 500;
        }
      }
			:deep(.el-select__caret) {
           color:#6B7385;
           font-size: 16px;
        }
    }

    .empty {
      height: calc(100% - 56px);
    }
    .list-container {
      height: calc(100% - 56px);
      overflow-y: auto;
      padding: 16px;
      > div {
        padding: 12px 16px;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        border: 1px solid #EAECF0;
        border-radius: 4px;
				>.el-row:not(:last-child){
					padding-bottom:8px;
				}
				.name {
   color:#1D2129;
				}
				.btn-status{
					display: flex;
					align-items: center;
					height: 24px;
					padding:0 8px;
					font-size: 12px;
					font-weight: 500;
					border-radius: 2px;
				}
        &:hover {
					border:1px solid #3E7BFA;
          background-color: #FFF;
          cursor: pointer;
        }
        & + div {
          margin-top: 12px;
        }
        .it_item_time {
					font-size: 12px;
          color: #838A99;
        }

        // &.it_stateId_1 {
        //   border-left: 4px solid #fde3b7;
        // }
        // &.it_stateId_2 {
        //   border-left: 4px solid #76bef5;
        // }
        // &.it_stateId_3 {
        //   border-left: 4px solid #5fb159;
        // }
        // &.it_stateId_4 {
        //   border-left: 4px solid #ef826f;
        // }
        &.selected {
					border:1px solid #3E7BFA;
          // background-color: rgb(230, 243, 255, 0.6);
        }
      }
    }
  }
  .rightContent {
    flex: 1;
    header {
      display: flex;
      justify-content: space-between;
      height: 56px;
      line-height: 56px;
      padding: 0 20px;
      align-items: center;
      border-bottom: 1px solid var(--disabled-bg-color);
    }
  }
}

</style>
