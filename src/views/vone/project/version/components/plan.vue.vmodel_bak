<template>
  <div>
    <el-dialog :title="`${code!='WALL'?'关联迭代':'关联里程碑'}`" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" width="30%" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="planForm" v-loading="formLoading" :rules="rules" :model="planForm" label-position="top">
        <el-form-item :label="`${code!='WALL'?'迭代':'里程碑'}`" prop="plan">
          <el-select
            v-model="planForm.plan"
            style="width: 100%"
            clearable
            :placeholder="`${code!='WALL'?'请选择迭代':'请选择里程碑'}`"
            multiple
            filterable
            :rules="[
              { required: true, message: code!='WALL'?'请输入里程碑':'请输入迭代', trigger: 'change' },
            ]"
          >
            <el-option v-for="(item, index) in plans" :key="index" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button :loading="saveLoading" type="primary" @click="sureAdd">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiAlmProjectPlanNoPage,
  updateProjectPlan
} from '@/api/vone/project/iteration'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productVersionId: {
      type: String,
      default: undefined
    },
    code: {
      type: String,
      default: undefined
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value == '') {
        callback(this.code != 'WALL' ? new Error('请输入迭代') : new Error('请输入里程碑'))
      } else {
        callback()
      }
    }
    return {
      planForm: {
        plan: []
      },
      formLoading: false,
      saveLoading: false,
      rules: {
        plan: [{ required: true, validator: validatePass, trigger: 'change' }]
      },
      plans: []

    }
  },

  mounted() {
    this.getPlans()
  },
  methods: {

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.planForm.resetFields()
    },
    // 查询未关联版本的迭代数据
    async getPlans() {
      this.formLoading = true
      const { data, isSuccess, msg } = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id
      })
      this.formLoading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.plans = data.filter(r => !r.productVersionId)
    },
    // 保存关联
    async sureAdd() {
      this.$refs.planForm.validate((valid) => {
        if (valid) {
          this.submit()
        }
      })
    },
    async submit() {
      this.saveLoading = true
      try {
        const res = await updateProjectPlan(this.productVersionId, 'relation',
          this.planForm.plan
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
        }
        this.$message.success('关联成功')
        this.$emit('success')
        this.onClose()
      } catch (error) {
        this.saveLoading = false
        this.$message.error('保存失败')
      }
    }

  }
}
</script>
