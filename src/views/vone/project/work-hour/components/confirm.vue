<template>
  <div class="box">
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          show-basic
          v-model:default-fileds="defaultFileds"
          table-search-key="hours-table"
          v-model:extra="extraData"
          v-model:model="formData"
          :table-ref="$refs['hours-table']"
          @getTableData="getInitTableData"
        />
      </template>
      <template slot="actions">
        <el-button class="system" :disabled="!$permission('project_man_hour_confirm')" @click="batch('0')">
          一键确认
        </el-button>
        <el-button class="smoke" :disabled="!$permission('project_man_hour_confirm')" @click="batch('1')">
          一键驳回
        </el-button>
        <el-button type="primary" :disabled="!$permission('project_man_hour_confirm')" @click="batchActions('approval')">批量审批</el-button>

      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <main style="height: calc(100% - 85px)">
      <vxe-table
        ref="hours-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :cell-class-name="cellShow"
        :expand-config="{lazy: true, loadMethod: ({row})=>getClidData(row,1)}"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <!-- <vxe-column type="expand" width="60" fixed="left" :visible="groupItem.value !=='none'">
          <template #content="{ row }">
            <vxe-table
              
              ref="child-table"
              :key="timeStamp"
              v-loading="row.loading"
              class="child-table"
              :show-header="false"
              :cell-class-name="cellShow"
              :data="row.childrenData"
              @checkbox-change="childSelectChange"
              @selection-change="handleSelectionChange($event, row)"
            >
              <vxe-column type="checkbox" width="36" fixed="left" align="center" />
              <vxe-column v-if="columnKeysCheck['fillingTime']" min-width="228" field="fillingTime" title="工时日期">
                <template #default="scope">
                  <span>{{ scope.row.fillingTime | formatVal }}</span>
                </template>
              </vxe-column>
              <vxe-column v-if="columnKeysCheck['name']" field="name" title="工作项名称" min-width="200">
                <template #default="scope">
                  <div v-if="!scope.row.groupType">
                    <i v-if="scope.row.type.code === 'ISSUE'" class="iconfont el-icon-icon-xuqiu" style="color: rgb(135, 145, 250);" />
                    <i v-if="scope.row.type.code === 'BUG'" class="iconfont el-icon-icon-quexian" style="color: rgb(250, 107, 87);" />
                    <i v-if="scope.row.type.code === 'TASK'" class="iconfont el-icon-icon-renwu" style="color: rgb(62, 123, 250);" />
                    <span v-if="scope.row.echoMap">{{ scope.row.echoMap.biz? scope.row.echoMap.biz.name : !scope.row.groupType? '工作项已不存在': '' }}</span>
                  </div>
                </template>
              </vxe-column>
              <vxe-column v-if="columnKeysCheck['duration']" field="duration" title="本次填报工时" width="120">
                <template #default="scope">
                  {{ scope.row.duration? scope.row.duration + 'h' : '' }}
                </template>
              </vxe-column>
              <vxe-column v-if="columnKeysCheck['createTime']" field="createTime" title="填报日期" width="120">
                <template #default="scope">
                  {{ scope.row.createTime | formatVal }}
                </template>
              </vxe-column>
              <vxe-column v-if="columnKeysCheck['verified']" class-name="column-style" field="verified" title="审批状态" width="100">
                <template #default="scope">
                  <el-dropdown v-if="!scope.row.groupType" :disabled="scope.row.valid||!$permission('man_hour_statistics_approval')" trigger="click" @command="approvalChange($event,scope.row,row)">
                    <span class="el-dropdown-link">
                      <el-tag v-if="!scope.row.verified" type="info" class="unVerified">未审批</el-tag>
                      <el-tag v-if="scope.row.verified&&scope.row.valid" type="success">已确认</el-tag>
                      <el-tag v-if="scope.row.verified&&!scope.row.valid" type="danger">已驳回</el-tag>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="true">确认</el-dropdown-item>
                      <el-dropdown-item :command="false">驳回</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </vxe-column>

              <vxe-column v-if="columnKeysCheck['filledBy']" field="filledBy" title="填报人" min-width="100">
                <template #default="scope">
                  <vone-user-avatar v-if="scope.row.echoMap.filledBy" :avatar-path="scope.row.echoMap.filledBy.avatarPath" :name="scope.row.echoMap.filledBy.name" width="20px" height="20px" />
                </template>
              </vxe-column>
              <vxe-column title="操作" fixed="right" align="left" width="50">
                <template #default="{ row }">
                  <template>
                    <el-tooltip class="item" content="工时分摊" placement="top">
                      <el-button
                        type="text"
                        icon="iconfont el-icon-application-apportion"
                        :disabled="row.verified&&row.valid ? false : true"
                        @click="shareTime(row)"
                      />
                    </el-tooltip>

                  </template>
                </template>
              </vxe-column>

            </vxe-table>
            <div class="pagination" style="padding: 16px 0;">
              <el-pagination
                v-if="row.childrenData && row.childrenData.length > 0"
                layout="->,total, prev, pager, next, jumper"
                :current-page="row.pageData.current * 1"
                :page-size="10"
                :pager-count="5"
                :total="row.pageData.total * 1 || 0"
                @current-change="handlePageChange($event,row)"
              />
            </div>
          </template>
        </vxe-column> -->
        <vxe-column title="工时日期" field="fillingTime" min-width="180">
          <template #default="{ row }">
            <span v-if="!row.groupType">{{ row.fillingTime | formatVal }}</span>
            <div v-else>
              {{ row.name }}
              <span class="count-num">
                {{ row.count }}
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="工作项名称" field="name" min-width="200">
          <template #default="{row}">
            <div v-if="!row.groupType">
              <i v-if="row.type.code === 'ISSUE'" class="iconfont el-icon-icon-xuqiu" style="color: rgb(135, 145, 250);" />
              <i v-if="row.type.code === 'BUG'" class="iconfont el-icon-icon-quexian" style="color: rgb(250, 107, 87);" />
              <i v-if="row.type.code === 'TASK'" class="iconfont el-icon-icon-renwu" style="color: rgb(62, 123, 250);" />
              <span v-if="row.echoMap">{{ row.echoMap.biz? row.echoMap.biz.name : !row.groupType? '工作项已不存在': '' }}</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="本次填报工时" field="duration" width="120">
          <template #default="{row}">
            {{ row.duration? row.duration + 'h' : '' }}
          </template>
        </vxe-column>
        <vxe-column title="填报日期" field="createTime" width="120">
          <template #default="{ row }">
            {{ row.createTime | formatVal }}
          </template>
        </vxe-column>
        <vxe-column field="status" title="审批状态" width="120" class-name="column-style">
          <template #default="{row}">
            <el-dropdown v-if="!row.groupType" :disabled="row.verified||!$permission('project_man_hour_confirm')" trigger="click" @command="approvalChange($event,row)">
              <span class="el-dropdown-link">
                <el-tag v-if="!row.verified" type="info" class="unVerified">未审批</el-tag>
                <el-tag v-if="row.verified&&row.valid" type="success">已确认</el-tag>
                <el-tag v-if="row.verified&&!row.valid" type="danger">已驳回</el-tag>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="true">确认</el-dropdown-item>
                <el-dropdown-item :command="false">驳回</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>

        <vxe-column title="报工人" field="filledBy">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <vone-user-avatar v-if="row.echoMap.filledBy" :avatar-path="row.echoMap.filledBy.avatarPath" :name="row.echoMap.filledBy.name" width="20px" height="20px" />
            </div>
          </template>
        </vxe-column>

        <!-- <vxe-column title="操作" fixed="right" align="left" width="50">
          <template #default="{ row }">
            <template v-if="!row.groupType">
              <el-tooltip class="item" content="工时分摊" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-application-apportion"
                  :disabled="row.verified&&row.valid && $permission('project_man_hour_share') ? false : true"
                  @click="shareTime(row)"
                />
              </el-tooltip>

            </template>
          </template>
        </vxe-column> -->

      </vxe-table>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />
    </main>
    

    <el-dialog
      :title="dialogTitle"
      v-model="batchActionsVisible"
      width="30%"
    >
      <el-form ref="batchActionsForm" :model="batchActionsForm" :rules="rules">
        <el-form-item v-if="batchActionsForm.type === 'approval'" label="审批" prop="valid">
          <el-select v-model="batchActionsForm.valid" placeholder="请选择审批结果">
            <el-option label="确认" :value="true" />
            <el-option label="驳回" :value="false" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchActionsVisible = false">取消</el-button>
        <el-button type="primary" @click="batchActionsSubmit()">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 工时分摊 -->
    <ShareHourDialog v-if="shareParam.visible" v-bind="shareParam" v-model="shareParam.visible" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getWorkingHoursInfopage, geWorkingHoursInfoQuery, bathConfirm, getWorkingHoursConfig, batchSetbilled, getGroup, batchConfirm } from '@/api/vone/manhour/index'
import ShareHourDialog from './share-hour-dialog.vue'

export default {
  name: 'Confirm',
  components: {
    ShareHourDialog
  },
  filters: {
    formatVal(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD')
    }
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'filledBy',
          name: '填报人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择填报人'
        },
        {
          key: 'type',
          name: '事项类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择事项类型',
          optionList: [
            {
              code: 'ISSUE',
              name: '需求',
              id: 'ISSUE'
            },
            {
              code: 'BUG',
              name: '缺陷',
              id: 'BUG'
            },
            {
              code: 'TASK',
              name: '任务',
              id: 'TASK'
            }
          ]
        },
        {
          key: 'status',
          name: '审批状态',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择审批状态',
          optionList: [
            {
              code: '0',
              name: '未审批',
              id: '0'
            },
            {
              code: '1',
              name: '已确认',
              id: '1'
            },
            {
              code: '2',
              name: '已驳回',
              id: '2'
            }
          ]
        },
        {
          key: 'submitTime',
          name: '提报时间',
          type: {
            code: 'DATE'
          },
          placeholder: '请选择提报时间'
        }
      ],
      shareParam: { visible: false },
      formData: {
        projectId: this.$route.params.id
      },
      pageLoading: false,
      tableData: { records: [], total: 0 },
      tableOptions: {
        isSelection: true,
        hideColumns: [],
        groupingOptions: [
          { name: '按用户', value: 'filledBy', key: 'echoMap.filledBy.name', form: 'filledBy' },
          // { name: '按项目', value: 'project', key: 'echoMap.project.name', form: 'projectId' },
          { name: '按填报时间', value: 'fillingTime', key: 'fillingTime', type: 'time', form: 'fillingTime' }
        ]
      },
      grouping: '',
      selecteTableData: [],
      noPage: false,
      isShow: '0',
      batchActionsVisible: false,
      dialogTitle: '',
      batchActionsForm: {
      },
      rules: {
        valid: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        billed: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      columnsList: [],
      maps: new Map(),
      pids: [],
      columnKeysCheck: {},
      groupItem: {
        value: 'none'
      },
      pageData: { // 分页配置
        current: 1,
        size: 10,
        total: 0
      },
      rowData: {},
      formContainer: {},
      timeStamp: ''
    }
  },
  mounted() {
    // this.getInitTableData()
    this.getWorkingHoursConfFn()
    this.$nextTick(() => {
      const tableColum = this.$refs['hours-table'] && this.$refs['hours-table'].getTableColumn()?.fullColumn || []
      const columns = tableColum.filter(item => item.type != 'checkbox' && item.type != 'expand' && item.title != '操作')

      this.columnKeysCheck = columns.reduce((acc, ele) => {
        acc[ele.field] = ele.visible
        return acc
      }, {})
    })
  },
  methods: {
    checkMethod({ row }) {
      return false
    },
    handleSelectionChange(e, row) {
      this.selecteTableData = Array.from(new Set([...this.selecteTableData, ...e]))
    },
    childSelectChange({ checked, row }) {
      if (checked) {
        this.selecteTableData.push(row)
      } else {
        this.selecteTableData = this.selecteTableData.filter(el => el.id != row.id)
      }
    },
    handlePageChange(e, row) {
      row.load = false
      this.getClidData(row, e)
    },
    getClidData(row, page) {
      if (row.load) return
      row.loading = true
      const tableAttr = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        current: page || 1,
        order: 'descending',
        size: 10,
        sort: 'createTime',
        extra: {},
        model: {}
      }
      params.model[this.groupItem.form] = row.bizId
      this.$set(params.model, 'projectId', this.$route.params.id)

      return getWorkingHoursInfopage(params).then(res => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        res.data.records.forEach(element => {
          element.pid = row.id
        })
        row.load = true
        row.loading = false
        this.$nextTick(() => {
          row.childrenData = res.data.records || []
          row.pageData = res.data
          row.count = res.data.total
          this.timeStamp = new Date().valueOf()
          this.maps.set(row.id, row)
          // this.$refs['child-table'].doLayout()
        })
      }).catch(() => {
        this.$set(row, 'loading', false)
      })
    },
    handleRow(index, row, item, table) {
      if (typeof item.handler === 'function') {
        item.handler(row, index)
      }
      this.rowData = table
    },
    isDisabled(scope, { disabled }) {
      if (typeof disabled === 'function') {
        disabled = disabled(scope.$index, scope.row)
      }
      return !!disabled
    },
    cellShow(row) {
      if (row.row.groupType && row.columnIndex === 0) {
        return 'hide-node'
      }
    },
    childCellShow(row) {
      if (row.row.groupType && row.columnIndex === 0 || (!row.row.groupType && row.columnIndex === 1)) {
        return 'hide-node'
      }
    },
    expandChange(row) {
      this.getClidData(row, 1)
    },
    async getWorkingHoursConfFn() {
      try {
        const res = await getWorkingHoursConfig()
        this.isShow = res.data.find(e => {
          return e.key === 'ENABLE_BILLED'
        }).value
        // if (this.isShow === '1') {
        //   var arry = this.$refs['hours-table'].columns
        //   this.columnsList = [...arry, ...[
        //     {
        //       label: '是否计费',
        //       width: '120',
        //       prop: 'billed'
        //     }
        //   ]]
        // }
      } catch (e) {
        return
      }
    },
    async batchActionsSubmit() {
      try {
        await this.$refs.batchActionsForm.validate()
        this.selecteTableData = Array.from(new Set(this.selecteTableData))
        this.pids = Array.from(new Set(this.selecteTableData.map(e => e.pid)))
        if (this.batchActionsForm.type === 'approval') {
          if (this.selecteTableData.some(e => e.verified && e.valid)) {
            this.$message.warning('已确认的不能再审批')
            return
          }
          const params = {
            valid: this.batchActionsForm.valid,
            workingHoursIds: this.selecteTableData.map(e => e.id)
          }
          this.approvalFn(params)
        } else {
          const params = {
            billed: this.batchActionsForm.billed,
            ids: this.selecteTableData.map(e => e.id)
          }
          const res = await batchSetbilled(params)
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.$message.success('修改成功')
          this.$refs.batchActionsForm.resetFields()
          this.batchActionsVisible = false
          this.refresh()
        }
      } catch (e) {
        return
      }
    },
    batchActions(type) {
      this.batchActionsForm.type = type
      if (!this.selecteTableData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.batchActionsVisible = true
      if (type == 'approval') {
        this.dialogTitle = '批量审批'
      } else {
        this.dialogTitle = '批量编辑'
      }
    },
    async approvalFn(e, row, propsrow) {
      const res = await bathConfirm(e)
      if (res.isSuccess) {
        this.$message.success('审批成功')
        if (this.batchActionsVisible) {
          this.$refs.batchActionsForm.resetFields()
          this.batchActionsVisible = false
        }
        this.refresh(propsrow)
      }
    },
    refresh(propsrow) {
      if (this.formContainer.grouping === 'none' || !this.formContainer.grouping) {
        this.getInitTableData({}, 'edit')
      } else {
        if (propsrow) {
          propsrow.load = false
          this.getClidData(propsrow, propsrow.pageData.current * 1)
        } else {
          this.pids.forEach(e => {
            const data = this.maps.get(e)
            data.load = false
            this.getClidData(data, data.pageData.current * 1)
          })
        }
      }
    },
    approvalChange(e, row, propsrow) {
      const params = {
        valid: e,
        workingHoursIds: [row.id]
      }
      this.approvalFn(params, row, propsrow)
    },
    selectedDisabled(row, index) {
      if (row.rowType == 'group') {
        return false
      } else {
        return true
      }
    },
    async load(tree, treeNode, resolve) {
      const params = {
        filledBy: this.grouping == 'filledBy' ? tree.id : '',
        fillingTime: this.grouping == 'fillingTime' ? tree.id : '',
        projectId: this.grouping == 'project' ? tree.id : ''
      }
      const res = await geWorkingHoursInfoQuery(params)
      res.data.forEach(e => {
        e.pid = tree.id
      })
      this.maps.set(tree.id, { tree, treeNode, resolve })
      resolve(res.data)
    },
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs['hours-table'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs['hours-table'].getCheckboxRecords()
    },
    // 查询工时记录
    async getInitTableData(e, type) {
      try {
        let params = {}
        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
        const sortObj = this.$refs.searchForm?.sortObj
        this.$set(this.formData, 'projectId', this.$route.params.id)
        params = {
          ...tableAttr,
          ...sortObj,
          extra: {},
          model: { ...this.formData }
        }
        if (e && Object.keys(e).length !== 0) {
          this.formContainer = e
        }
        if (!this.formContainer.grouping || this.formContainer.grouping == 'none') {
          this.pageLoading = true

          const res = await getWorkingHoursInfopage(params)

          this.pageLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.tableData = res.data
        } else {
          if (type == 'edit') {
            this.rowData.load = false
            this.getClidData(this.rowData, this.rowData.pageData.current * 1)
          } else {
            this.groupItem = this.tableOptions.groupingOptions.find(item => {
              return item.value == this.formContainer.grouping
            })
            params.sort = 'bizId'
            params.model = { ...params.model, ...{ groupBy: this.formContainer.grouping }}
            const res = await getGroup(params)
            this.pageLoading = false
            if (!res.isSuccess) {
              this.$message.warning(res.msg)
              return
            }
            res.data.records.forEach(e => {
              if (this.groupItem?.type === 'time') {
                e.bizId = dayjs(e.bizId).format('YYYY-MM-DD')
              }
              e.name = e.data?.name || e.bizId
              e.id = e.bizId
            })
            this.tableData = res.data
          }
          this.$nextTick(() => {
            this.$refs['hours-table'].refreshColumn()
          })
        }
      } catch (e) {
        this.pageLoading = false
      }
    },
    shareTime(row) {
      this.shareParam = { visible: true, id: row.id, hour: row.duration }
    },
    async batch(type) {
      const data = this.$refs['hours-table'].tableData
      const unVerified = data.filter(r => r.valid == null)
      if (!unVerified.length) {
        return this.$message.warning('暂无[未审批]数据')
      }

      await this.$confirm(`确定批量${type == '0' ? '确认' : '驳回'}工时记录吗?`, '提示', {
        type: 'warning',
        closeOnClickModal: false
      })
      const res = await batchConfirm({
        ...this.formData,
        valid: type == 0
      })
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.getInitTableData()
    }
  }
}
</script>
<style lang='scss' scoped>
.box{
  padding: 16px;
  height: calc(100vh - 125px);
}
.userList {
  :deep(.el-input__inner) {
    padding:0;
    border: 0;
    padding-left:28px;
  }
  .el-input__icon {
    display: none;
  }
  .el-input__prefix{
    left:2px;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
.iconfont {
  line-height: 22px;
  margin-right: 3px;
}
.fontstyle {
  overflow:hidden;
  white-space:nowrap;
  text-overflow:ellipsis;
}
:deep() {
  .vone-vxe-table .vxe-table--body-wrapper {
    height: initial;
  }
}
:deep(.el-table [class*=el-table__row--level]) {
  .el-table__placeholder{
    display: none;
  }
  .el-table__expand-icon {
    position: absolute;
    left: -10px;
  }
}
:deep(.el-table) {
  .el-dropdown {
    cursor: pointer;
  }
}
:deep(.hide-checkbox) {
  .el-checkbox__input {
    display: none;
  }
}
.count-num {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  color: #838A99;
  padding: 0px 6px;
  background: #F2F3F5;
  border-radius: 9px;
  margin-left: 8px;
}
.child-table {
  font-size: 14px;
}
.unVerified{
  cursor: pointer;
}
.system {
  background: var(--Green);
  color: #fff;
  border: none;
}
.smoke {
  background: var(--Red);
  color: #fff;
  border: none;
  }
:deep(.el-button.is-disabled:not(.el-button--text)) {
  color: var(--font-disabled-color);
  border: 1px solid var(--input-border-color);
  background-color: var(--content-bg-disabled-color);
}
</style>
