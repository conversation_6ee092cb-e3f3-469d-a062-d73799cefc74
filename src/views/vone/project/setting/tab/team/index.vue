<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>团队树级</span>
        <div class="search">
          <el-popover v-model="visible" trigger="click" placement="bottom-start" width="300" popper-class="table-search-form org-search-form">
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">筛选团队</span>
              </div>
              <div class="search-form">
                <el-form ref="searchForm" inline label-position="top">
                  <el-form-item label="团队名称" prop="name">
                    <el-input v-model.trim="searchForm.name" style="width: 100%" placeholder="输入机构名称" />
                  </el-form-item>
                  <el-form-item label="负责人" prop="leaderBy">
                    <vone-remote-user v-model="searchForm.leaderBy" style="width: 100%" placeholder="选择负责人" />
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer org-footer">
                <el-button plain @click="reset">重置</el-button>
                <el-button type="primary" @click="searchTree">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="筛选" placement="top">
                <i :class="['iconfont', 'el-icon-application-filter', visible ? 'active' : '']" style="margin-left: 12px;cursor: pointer;" />
                <!-- <el-button>sss</el-button> -->
              </el-tooltip>
            </span>
          </el-popover>
          <el-divider style="margin:-3px 8px 0px 8px; " direction="vertical" />
          <el-dropdown trigger="click" placement="bottom">
            <el-tooltip class="item" effect="dark" content="新建团队" placement="top">
              <i class="iconfont el-icon-tips-plus" />
            </el-tooltip>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="creatTeam()">
                新建团队
              </el-dropdown-item>
              <el-dropdown-item @click.native="operatorTeam()">
                导入团队
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="treeContent">
        <vone-empty v-if="data.length == 0" />
        <el-tree
          v-else
          ref="teamTree"
          v-loading="treeLoading"
          class="tree custom-tree-icon"
          :data="data"
          node-key="id"
          default-expand-all
          check-strictly
          :current-node-key="currentNode"
          :expand-on-click-node="false"
          @node-click="changeUser"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <el-tooltip v-showTooltips :content="data.label" placement="top" class="node-label">
              <div>
                <svg-icon v-if="data.type == 'OrgTeam'" icon-class="setting-team" />
                <svg-icon v-else icon-class="setting-project" />
                <span class="label" :data-node="node.level">{{ data.label }}</span>
              </div>
            </el-tooltip>
            <span class="manager">
              <el-tag effect="dark">{{ data.echoMap && data.echoMap.leaderBy &&data.echoMap.leaderBy.name }}</el-tag>
            </span>
            <!-- v-if="data.type == 'ProjectTeam'"  -->
            <span class="operation-tree-icon">
              <el-button v-if="$permission('project_setting_team_add')" type="text" size="mini" icon="iconfont el-icon-tips-plus-circle" @click.stop="() => add(node, data)" />
              <el-button v-if="$permission('project_setting_team_edit')" type="text" size="mini" icon="iconfont el-icon-application-edit" @click.stop="() => edit(node, data)" />
              <el-button v-if="$permission('project_setting_team_del')" type="text" size="mini" icon="iconfont el-icon-application-delete" @click.stop="() => remove(node, data)" />
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="rightSection">
      <div class="rightTopSection">
        <div class="title">
          <svg-icon v-if="clickNode && clickNode.type == 'OrgTeam'" icon-class="setting-team" />
          <svg-icon v-else icon-class="setting-project" />
          {{ clickNode &&clickNode.name || '暂无团队' }}</div>
        <div class="detail">
          <span v-if="clickNode && clickNode.type == 'OrgTeam'">隶属机构<p>{{ clickNode.echoMap &&clickNode.echoMap.orgId && clickNode.echoMap.orgId.name || '-' }}</p></span>
          <span v-else-if="clickNode &&clickNode.type == 'ProjectTeam'">所属项目<p>{{ clickNode.echoMap &&clickNode.echoMap.projectInfos &&clickNode.echoMap.projectInfos[0].name }}</p></span>
          <span>上级团队<p>{{ clickNode && clickNode.echoMap &&clickNode.echoMap.parentId &&clickNode.echoMap.parentId.name || '-' }}</p></span>
          <span>类型<p>{{ clickNode && clickNode.echoMap &&clickNode.echoMap.type &&clickNode.echoMap.type.name || '-' }}</p></span>
          <span>负责人<p>
            <vone-user-avatar
              v-if="clickNode && clickNode.echoMap && clickNode.echoMap.leaderBy"
              :avatar-path="clickNode.echoMap.leaderBy.avatarPath"
              :name="clickNode.echoMap.leaderBy.name"
              :show-name="true"
              height="20px"
              width="20px"
            /></p>
          </span>
        </div>
      </div>
      <div class="rightBottomSection">
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic
              ref="searchForm"
              table-search-key="projectTeamUser"
              :model="formData"
              :default-fileds="defaultFileds"
              show-basic
              :extra="extraData"
              @getTableData="getTableData"
            />
          </template>
          <template v-if="clickNode &&clickNode.id" slot="actions">
            <el-button :disabled="!$permission('project_setting_user_del')" @click="deleteUser">批量删除</el-button>
            <el-button :disabled="!$permission('project_setting_user_add')" type="primary" @click="addUser">添加成员</el-button>
          </template>
          <template slot="fliter">
            <vone-search-filter
              :extra="extraData"
              :model="formData"
              :default-fileds="defaultFileds"
              @getTableData="getTableData"
            />
          </template>
        </vone-search-wrapper>
        <div>
          <vxe-table
            ref="projectTeamUser"
            class="vone-vxe-table"
            border
            resizable
            show-overflow="tooltip"
            :empty-render="{name: 'empty'}"
            :loading="pageLoading"
            :data="tableData.records"
            :column-config="{
              minWidth:'120px'
            }"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-column type="checkbox" width="37" fixed="left" />
            <vxe-column
              field="account"
              title="账号"
              show-overflow-tooltip
              min-width="100"
            />
            <vxe-column title="名称" field="name" min-width="150">
              <template #default="{ row }">
                <vone-user-avatar :avatar-path="row.avatarPath" :avatar-type="row.avatarType" :name="row.name" />
              </template>
            </vxe-column>

            <vxe-column field="email" show-overflow-tooltip title="邮箱" />
            <vxe-column field="userRoles" title="角色" width="180">
              <template #default="{ row }">
                {{ row.allRole }}
              </template>
            </vxe-column>
            <!-- <vxe-column
            fixed="updateTime"
            title="更新时间"
            show-overflow-tooltip
            width="165"
          /> -->
            <vxe-column field="userType" show-overflow-tooltip title="用户类型">
              <template #default="{ row }">
                {{ userTypeName(row) }}
              </template>
            </vxe-column>
            <vxe-column field="mobile" show-overflow-tooltip title="手机号">
              <template #default="{ row }">
                {{ row.mobile||'-' }}
              </template>
            </vxe-column>
            <vxe-column title="操作" field="right" align="left" width="80">
              <template #default="{ row }">
                <template>
                  <el-tooltip class="item" content="编辑" placement="top">
                    <el-button :disabled="!$permission('project_setting_user_edit')" type="text" icon="iconfont el-icon-application-edit" @click="editUser(row)" />
                  </el-tooltip>
                  <el-tooltip class="item" content="移除" placement="top">
                    <el-button :disabled="!$permission('project_setting_user_del')" type="text" icon="iconfont el-icon-application-delete" @click="deleteUser(row)" />
                  </el-tooltip>
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </div>

        <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
      </div>
    </div>
    <addEdit v-if="dialogVisible" ref="editDialog" v-model:dialog-visible="dialogVisible" :edit-title="editTitle" :click-node="editNode" @success="getTeamList" />
    <userDialog v-if="userVisible" v-model="userVisible" :click-node="clickNode" @success="getTableData" />
    <import-team v-if="importVisible" v-model="importVisible" :check-list="checkList" @success="getTeamList" />
    <creat-team v-if="addTeamVisible" v-model="addTeamVisible" @success="getTeamList" />
    <el-dialog
      :title="'编辑成员'"
      :visible="editVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="close"
    >
      <el-form ref="editForm" :model="editForm" :rules="editRule">
        <el-form-item label="角色" prop="role">
          <el-select v-model="editForm.role" multiple placeholder="请选择角色">
            <el-option v-for="(item,index) in roleList" :key="index" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getRoleListByIdNoPage, queryDetail } from '@/api/vone/base/team'
import { projectTeamList, delUserListById, editRoleByUserId, delProjectTeam, getProjectUserList } from '@/api/vone/project/team'
import addEdit from './add-edit-dialog.vue'
import userDialog from './user-dialog.vue'
import ImportTeam from './import-team.vue'
import CreatTeam from './creat-team.vue'
import { gainTreeList } from '@/utils'
function treeToList(data, arr = []) {
  data.map(item => {
    arr.push(item.id)
    if (item.children?.length) {
      treeToList(item.children, arr)
    }
  })
  return arr
}
export default {
  components: {
    addEdit,
    CreatTeam,
    userDialog,
    ImportTeam
  },
  data() {
    return {
      defaultFileds: [
        {
          key: 'name',
          name: '用户名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入用户名称'
        }
      ],
      treeLoading: false,
      data: [],
      pageLoading: false,
      tableData: {},
      formData: {
        // name: '',
        // account: ''
      },
      extraData: {},
      dialogVisible: false,
      editTitle: '',
      userVisible: false,
      visible: false,
      searchForm: {
        name: '',
        leadingBy: ''
      },
      clickNode: {},
      selecteTableData: [],
      currentNode: '',
      editNode: {},
      editVisible: false,
      roleList: [],
      editForm: {
        role: []
      },
      editRule: {
        role: [{ required: true, message: '请选择角色' }]
      },
      editRow: {},
      importVisible: false,
      checkList: [],
      addTeamVisible: false
    }
  },
  computed: {
    userTypeName() {
      return function(row) {
        return row.echoMap?.userType?.name || ''
      }
    }
  },
  mounted() {
    this.getTeamList()
    this.getRoleList()
  },
  methods: {
    getRoleList() {
      getRoleListByIdNoPage(this.$route.params.id).then(res => {
        if (res.isSuccess) {
          this.roleList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getTeamList() {
      this.treeLoading = true
      projectTeamList(this.$route.params.id, this.searchForm).then((res) => {
        this.treeLoading = false
        const orgTree = gainTreeList(res.data)
        this.data = orgTree
        this.checkList = treeToList(res.data, [])
        this.clickNode = this.data[0]
        this.currentNode = this.data[0] && this.data[0]?.id
        this.getTableData()
      })
    },
    reset() {
      this.searchForm = { name: '', leadingBy: '', projectId: this.$route.params.id }
      this.$nextTick(() => {
        this.getTeamList()
        this.visible = false
      })
    },
    searchTree() {
      this.visible = false
      this.getTeamList()
    },
    operatorTeam() {
      this.importVisible = true
    },
    creatTeam() {
      this.addTeamVisible = true
    },
    add(node, data) {
      this.editNode = {}
      this.dialogVisible = true
      this.editTitle = '新增团队'
      this.editNode = data || {}
    },
    edit(node, data) {
      this.dialogVisible = true
      this.editNode = {}
      this.editNode = JSON.parse(JSON.stringify(data))
      this.editTitle = '编辑团队'
    },
    remove(node, data) {
      // if (data.children) {
      //   return this.$message.warning('当前团队下有子级团队,不允许直接删除，请先删除子团队')
      // }
      this.$confirm(`是否删除团队【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      }).then(() => {
        delProjectTeam(this.$route.params.id, [data.id]).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTeamList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }).catch(() => { })
    },
    changeUser(node) {
      this.clickNode = node
      if (this.clickNode.type == 'ProjectTeam') {
        queryDetail(this.clickNode?.id).then(res => {
          if (res.isSuccess) {
            this.clickNode = res.data
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
      this.getTableData()
    },
    async getTableData() {
      if (this.clickNode && this.clickNode?.id) {
        const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
        const params = {
          ...pageObj,
          extra: {
            ...this.extraData
          },
          model: { teamId: this.clickNode?.id, projectId: this.$route.params.id, ...this.formData }
        }
        this.pageLoading = true
        getProjectUserList(params).then(res => {
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.pageLoading = false
          res.data.records.forEach(element => {
            element.allRole = element.echoMap?.projectRoles ? element.echoMap?.projectRoles.map(r => r.name).join('、') : ''
          })
          this.tableData = res.data
        })
      }
    },
    // 添加成员
    addUser() {
      this.userVisible = true
    },
    // 表格勾选
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs['projectTeamUser'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs['projectTeamUser'].getCheckboxRecords()
    },
    // 编辑成员
    editUser(row) {
      this.editRow = row
      this.editForm.role = row.echoMap?.projectRoles?.map(item => item.id)
      this.editVisible = true
    },
    // 删除成员
    deleteUser(row) {
      const ids = []
      if (row.id) {
        ids.push(row.id)
      } else {
        this.selecteTableData.map(item => { ids.push(item.id) })
      }
      if (ids.length == 0) {
        return this.$message.warning('请选择成员')
      }
      this.$confirm(`确认移除【${row.name}】吗？`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      }).then(() => {
        delUserListById(this.$route.params.id, this.clickNode.id, ids).then((res) => {
          if (res.isSuccess) {
            this.selecteTableData = []
            this.$message.success('移除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    close() {
      this.editVisible = false
      // this.editForm.role = []
      this.$refs.editForm.resetFields()
    },
    async save() {
      try {
        await this.$refs.editForm.validate()
      } catch (e) {
        return
      }
      const params = {
        'projectId': this.$route.params.id,
        'roleIds': this.editForm.role,
        'teamId': this.clickNode.id,
        'userId': this.editRow.id
      }
      editRoleByUserId(params).then(res => {
        if (res.isSuccess) {
          this.close()
          this.getTableData()
          this.$message.success('保存成功')
        } else {
          this.$message.warning(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@use "@/styles/variables.scss";
.rightSection {
  background: none;
  padding: 0;
  box-shadow: none;
  height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 58px);
}
.org-search-form .search-main{
  .search-header {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--font-main-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
  .search-form {
    padding: 16px;
    .el-form-item {
      width: 100%;
    }
  }
  .org-footer {
    text-align: right;
    padding: 12px 20px;
    border-top: 1px solid var(--disabled-bg-color);
  }
}

.leftSection {
	width: 240px;
  height: calc(100vh - 48px - 48px - 20px - 10px);
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		span {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
		.search {
			display: inline-block;
			.iconfont {
				cursor: pointer;
				color: var(--font-second-color);
			}
			.iconfont:hover {
				color: var(--main-theme-color);
			}
			.iconfont.active {
				color: var(--main-theme-color);
			}
		}
	}
	.treeContent {
		margin-top:8px;
    height: calc(100vh - 48px - 48px - 20px - 10px - 48px - 8px);
		overflow-y: overlay;
		.custom-tree-node {
			flex: 1;
			width: calc(100% - 90px);
      height: 100%;
      display: flex;
      align-items: center;
		}
		.el-tree-node__content {
      height: 36px;
      color: var(--font-main-color);
			display: inline-block;
			.node-label {
        display: inline-block;
        width: calc(100% - 90px);
				overflow: hidden;
				white-space: nowrap;
				text-overflow:ellipsis;
				.svg-icon {
					width: 16px;
					height: 16px;
				}
      }
			.manager {
        position: absolute;
        right: 16px;
      }
      .operation-tree-icon {
        .el-button {
          padding: 0px;
          height: unset;
          line-height: unset;
          min-width: unset;
          font-size: 16px;
        }
        .el-button.is-disabled {
          background-color: unset;
          border: unset;
        }
        opacity: 0;
				position: absolute;
        right: 16px;
      }

      &:hover {
				.manager {
					display: none
				}
        .operation-tree-icon {
          opacity: 1;
					background: var(--hover-bg-color);
        }
      }
			.svg-icon {
				margin-right: 4px;
			}
		}
  }
}
.rightTopSection {
	border-radius: 4px;
	padding: 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	height: 84px;
	color: var(--font-main-color);
	.title {
		font-weight: 500;
		line-height: 22px;
		display: flex;
		.svg-icon {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}
	}
	.detail {
		margin-top: 8px;
		line-height: 22px;
		span {
			color: var(--font-second-color);
			margin-right: 56px;
			display:inline-flex
		}
		p {
			display: inline-block;
			margin: 0px;
			margin-left:12px;
			color: var(--font-main-color);
		}
	}
}
.rightBottomSection {
	border-radius: 4px;
	padding: 16px 16px 10px 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	margin-top: 10px;
  height: calc(100vh - 48px - 48px - 20px - 10px - 94px);
}
// 44px-分页 48px-筛选 36px-表格头
.vone-vxe-table {
  :deep(.vxe-table--body-wrapper) {
    height: calc(100vh - #{$main-margin} - #{$main-margin} - #{$main-padding} - #{$main-padding} - #{$nav-top-height} - 44px - 48px - 36px - 94px - 58px);
    overflow-y: auto;
  }
}
</style>
