<template>
  <div v-loading="cardLoading" class="pageBox pageContentNoH">
    <el-row type="flex" class="rowLine">
      <el-col :span="12" class="header">
        <span class="back" @click="back"> <i class="iconfont el-icon-direction-back" /></span>
        <strong>配置{{ `【${typeName }】` }}表单</strong>
      </el-col>
      <el-col :span="12" style="text-align:right">
        <el-button type="primary" :loading="saveLoading" :disabled="!workflowNodeId && !selectLineId? true : false" @click="saveInfo">保存</el-button>
      </el-col>
    </el-row>

    <vone-work-flow ref="vone-g" :xml="xml" hide-text-annotation single :properties-props="{ width: 380 }" preview @changeTab="changeTab" @selectLine="selectLine">

      <div slot="properties">

        <el-form-item v-loading="tableLoading" label="字段权限">

          <el-table :data="formItems" style="width: 100%" class="vone-table">
            <el-table-column prop="name" label="字段" width="140" align="center" show-overflow-tooltip />
            <el-table-column prop="isShow" width="70" label="可见" align="center">

              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.isShow" :disabled="scope.row.isUpdate" @change="checkhandle(scope.$index,scope.row,'isShow')" />
              </template>

            </el-table-column>
            <el-table-column prop="isUpdate" label="可编辑" width="90" align="center">

              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.isUpdate" @change="checkhandle(scope.$index,scope.row,'isUpdate')" />
              </template>
            </el-table-column>
          </el-table>

        </el-form-item>

      </div>
      <div slot="connect">
        <div class="vone-border-tabs">
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="必填项" name="fieldAuth" />
            <el-tab-pane label="后处理" name="toDo" />
          </el-tabs>
          <component :is="activeName" v-if="activeName" ref="component" :form-fields="formFields" :line-id="selectLineId" :type-classify="typeClassify" :type-code="typeCode" :active-name="activeName" :is-end-node="isEndNode" />
        </div>
      </div>

    </vone-work-flow>
  </div>
</template>

<script>

import { getFlow, saveProjectWorkflowNodeCustomFormField } from '@/api/vone/base/work-flow'
import { saveRequiredFields, putTodoFields } from '@/api/vone/alm'

import { apiVaBaseCustomProjectFormCheck, apiVaBaseCustomProjectSetting } from '@/api/vone/base/customForm'
import { apiProjectmFindWorkflow } from '@/api/vone/project/index'
import _ from 'lodash'
import { jsonToXml } from './xmlUtils'
import fieldAuth from './tab/fieldAuth'
import toDo from './tab/toDo'
export default {
  components: {
    fieldAuth,
    toDo
  },
  props: {
    workFlowId: {
      type: String,
      default: undefined
    },
    allcustom: {
      type: Object,
      default: () => { }
    },
    typeClassify: {
      type: String,
      default: undefined
    },
    typeCode: {
      type: String,
      default: undefined
    },
    typeName: {
      type: String,
      default: undefined
    }

  },
  data() {
    return {
      cardLoading: false,
      fieldsLoading: false,
      formFields: [],
      allNode: [],
      workflowId: '',
      nodeStyle: {
        width: 200,
        height: 36
      },
      customFormId: '',
      saveLoading: false,
      stateCode: '',
      workflowNodeId: '',
      xml: undefined,
      paletteData: undefined,
      checkAll: false,
      formItems: [],
      isIndeterminate: true,
      address: null,
      tableLoading: false,
      selectLineId: null,
      isNode: false,
      activeName: 'fieldAuth',
      flowLineNodes: [],
      isEndNode: false
    }
  },
  watch: {
    typeCode() {
      this.getFlow()
      this.getFormItems()
    }

  },
  mounted() {
    this.getFlow()
    this.getFormItems()
  },
  methods: {
    handleClick(tab) {
      this.activeName = tab.name
    },

    async changeTab(val) {
      this.isNode = true
      this.workflowNodeId = val.onlyId
      this.stateCode = val.stateCode

      this.getFormAuth(val)
    },
    async saveConfig() {
      try {
        this.saveLoading = true

        const customFormList = this.formItems.filter(v => v.name !== '全选')

        const workflowNodeCustomFormFieldConfigs = customFormList.map(v => {
          return Object.assign({}, { customFormFieldId: v.id, isShow: v.isShow, isUpdate: v.isUpdate, stateCode: this.stateCode, workflowNodeId: this.workflowNodeId })
        })
        const params = {
          customFormId: this.customFormId,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
          projectId: this.$route.params.id,
          workflowId: this.workflowId || this.workFlowId,
          workflowNodeCustomFormFieldConfigs: [...workflowNodeCustomFormFieldConfigs]
        }
        const res = await saveProjectWorkflowNodeCustomFormField(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('配置成功')
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 全选
    checkhandle(index, val, key) {
      if (val.isUpdate) {
        this.formItems[index].isShow = true
      }

      // var arrKey = ['isShow', 'isUpdate']

      if (index == 0) {
        // 全选

        if (val[key]) {
          if (key == 'isUpdate') {
            this.formItems.forEach(element => {
              element.isShow = true
              element.isUpdate = true
            })
            return
          }
          this.formItems.forEach(element => {
            element[key] = true
          })
        } else {
          this.formItems.forEach(element => {
            element[key] = false
          })
        }
        // if (val[key]) {
        //   this.formItems.forEach((item, index) => {
        //     item[key] = true
        //   })
        // } else {
        //   this.formItems.forEach(item => {
        //     if (key == 'isShow') {
        //       item[key] = !!item.isUpdate
        //     } else {
        //       item[key] = false
        //     }
        //   })
        // }
      } else {
        // 节点字段配置table
        const customFormList = this.formItems.filter(v => v.name !== '全选')
        const allCheck = this.formItems.find(v => v.name == '全选')

        this.$set(allCheck, 'isShow', customFormList.every(j => j.isShow))
        this.$set(allCheck, 'isUpdate', customFormList.every(j => j.isUpdate))
      }
    },
    back() {
      this.$emit('back')
    },
    async getFormItems() {
      const res = await apiVaBaseCustomProjectSetting(this.$route.params.id, this.typeClassify, this.typeCode)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.customFormId = res.data.id

      if (res.data && res.data.customFormFields.length) {
        res.data.customFormFields.forEach(element => {
          element.isShow = !element.isShow ? false : element.isShow
          element.isUpdate = !element.isUpdate ? false : element.isUpdate
          element.type = element.type?.code
          element.flowRequired = false
        })

        const allCheckItem = [{
          id: '0',
          name: '全选',
          isShow: false,
          isUpdate: false
        }]
        const requireCheck = [{
          id: '0',
          name: '全选',
          flowRequired: false
        }]

        const filed = res.data.customFormFields.filter(r => r.state)

        this.formItems = allCheckItem.concat(filed)
        this.formFields = requireCheck.concat(filed)
      } else {
        this.formItems = []
        this.formFields = []
      }
    },
    async getFormAuth(val) {
      this.tableLoading = true

      const res = await apiVaBaseCustomProjectFormCheck(this.$route.params.id, this.typeClassify, {
        stateCode: val.stateCode,
        typeCode: this.typeCode
      })
      this.tableLoading = false
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      const data = res.data.filter(r => r.state)
      const checkIsShowData = data.filter(r => r.isShow).map(r => r.id)
      const checkIsupdateData = data.filter(r => r.isUpdate).map(r => r.id)

      this.formItems.forEach(element => {
        element.isShow = element.id == 0 && checkIsShowData.length == this.formItems.length - 1 ? true : checkIsShowData.indexOf(element.id) !== -1
        element.isUpdate = element.id == 0 && checkIsupdateData.length == this.formItems.length - 1 ? true : checkIsupdateData.indexOf(element.id) !== -1
      })

      // const newJson = [...res.data]
      // let d = []
      // const hash = {}
      // d = newJson.reduce((item, next) => {
      //   hash[next.id] ? '' : hash[next.id] = true && item.push(next)

      //   return item
      // }, [])

      // this.formItems = d
      // var i = 0
      // var j = 0
      // this.formItems.map(v => {
      //   v.isShow ? i++ : i
      //   v.isUpdate ? j++ : j
      // })
      // // 加全选
      // this.formItems = this.formItems.concat({
      //   name: '全选',
      //   isShow: checkIsShowData.length == this.formItems.length,
      //   isUpdate: checkIsupdateData.length == this.formItems.length
      // })
    },

    async getFlow() {
      this.cardLoading = true
      let nodes = []; let edges = []

      const resFlow = await apiProjectmFindWorkflow(
        this.$route.params.id, this.typeClassify, this.typeCode
      )
      if (!resFlow.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.workflowId = resFlow.data.workflowId

      const res = await getFlow(
        resFlow.data.workflowId
      )

      this.cardLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.flowLineNodes = res.data.workflowNodes

      if (!res.data.workflowNodes.length) {
        return
      }
      if (!res.data.workflowTransitions.length) {
        return
      }

      nodes = _.cloneDeep(res.data.workflowNodes)
      edges = _.cloneDeep(res.data.workflowTransitions)
      nodes.map(item => {
        item.nodeType = item.nodeType.code
        item.color = item.echoMap.stateCode.color
        item.onlyId = item.id
        item.id = item.code
        delete item.shape
        delete item.size
        delete item.echoMap
      })
      edges.map(item => {
        item.onlyId = item.id
        item.id = item.code
        item.source = item.sourceAnchor
        item.target = item.targetAnchor
        const permissions = []
        res.data.workflowAuthorities.map(itm => {
          // if (itm.transitionId == item.onlyId) {
          //   permissions.push(itm.type.code)
          // }
          if (itm.transitionId == item.onlyId && itm.type.code == 'RESPONSIBLE') {
            permissions.push(itm.type.code)
          }
          if (itm.transitionId == item.onlyId && itm.type.code == 'HANDLER') {
            permissions.push(itm.type.code)
          }
        })
        item.permission = permissions
      })
      console.log(edges, 1111)
      this.xml = jsonToXml({ nodes, edges })
    },
    // 获取线上的必填项
    selectLine(val, data) {
      const lastNode = this.flowLineNodes.find(r => r.nodeType.code == 'END_NODE')
      this.isEndNode = data.target == lastNode.code
      this.isNode = false
      this.selectLineId = val.onlyId
      // this.getLineRequired(val.onlyId)
    },

    // 保存连线上的必填字段
    async saveLineRequiredFields() {
      this.saveLoading = true
      try {
        const list = this.$refs.component.fieldData.filter(v => v.name !== '全选')

        const checkList = list.filter(r => r.flowRequired)

        const checkLists = checkList.map((r) => ({
          field: r.key,
          fieldId: r.id,
          projectId: this.$route.params.id || null,
          rule: 'NOT_NULL',
          transitionId: this.selectLineId,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode
        }))
        const params = {
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
          checkList: checkLists,
          projectId: this.$route.params.id || null
        }
        const { isSuccess, msg } = await saveRequiredFields(this.selectLineId, params)
        this.saveLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        this.$message.success('保存成功')
        // this.getLineRequired(this.selectLineId)
        this.$refs.component.getLineRequired(this.selectLineId)
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 保存
    async saveHandlerFields() {
      this.saveLoading = true
      const list = this.$refs.component.todoData.filter(r => r.name && r.value)
      const state = this.$refs.component.checkboxState
      // const stateMap = this.$refs.component.checkIdMap
      const states = state.map(r => ({
        typeCode: this.typeCode,
        typeClassify: this.typeClassify,
        projectId: this.$route.params.id,
        transitionId: this.selectLineId,
        config: JSON.stringify({ target: r }),
        type: 'UPDATE_STATE'
        // id: stateMap[r.target]
      }))

      const lists = list.map((r) => ({
        config: JSON.stringify(_.pick(r, ['key', 'value', 'name', 'eleType', 'defaultTime', 'message', 'placeholder', 'multiple', 'precision', 'validator'])),
        projectId: this.$route.params.id,
        transitionId: this.selectLineId,
        type: 'UPDATE_FIELD',
        id: r.id,
        typeClassify: this.typeClassify,
        typeCode: this.typeCode
      }))

      const params = {
        projectId: this.$route.params.id,
        transitionId: this.selectLineId,
        typeClassify: this.typeClassify,
        typeCode: this.typeCode,
        handleList: lists.concat(states)
      }

      try {
        const { isSuccess, msg } = await putTodoFields(params)
        this.saveLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        this.$message.success('保存成功')
        // this.getLineRequired(this.selectLineId)
        // this.$refs.component.getLineRequired(this.selectLineId)
      } catch (e) {
        this.saveLoading = false
      }
    },
    saveInfo() {
      if (this.isNode) {
        this.saveConfig()
      } else {
        if (this.activeName == 'toDo') {
          this.saveHandlerFields()
        } else {
          this.saveLineRequiredFields()
        }
      }
    }

  }

}
</script>

<style lang="scss" scoped>
:deep(.bpmn) {
  height: calc(100vh - 200px);
}
.pageBox {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow: hidden;
  flex: 1;
  :deep(.el-card__body) {
    padding: 10px 0;
  }
  :deep(.el-form--label-top .el-form-item__label) {
    margin-left: 20px;
    font-weight: bold;
  }
  .rowLine {
    border-bottom: 1px solid var(--el-divider);
    min-height: 32px;
    line-height: 32px;
    padding: 0 16px 16px;
  }
  .header {
    display: flex;
    align-items: center;
    .back {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 30px;
      height: 30px;
      color: var(--main-theme-color);
      box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
      border-radius: 16px;
    }
  }
}
.vone-border-tabs {
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    border: none;
  }
  :deep(.el-tabs--card > .el-tabs__header) {
    // margin: -10px -16px 16px;
  }
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__item:first-child) {
    border-left: 1px solid #eaecf0;
    margin: 0 10px;
  }
  :deep(.el-tabs__item) {
    // margin: 0px 2px;
    border: 1px solid #eaecf0;
    border-radius: 2px 2px 0px 0px;
    height: 32px;
    line-height: 32px;
  }
  :deep(.el-tabs__item:not(.is-active)) {
    background: #f2f3f5;
    color: #838a99;
  }
  :deep(.is-active) {
    color: #1964ff;
  }
}
</style>
