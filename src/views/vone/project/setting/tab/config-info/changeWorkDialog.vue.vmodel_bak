<template>
  <div>
    <el-dialog title="切换工作流" width="40%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false">
      <el-alert title="切换之后,当前类型的任务除了处于结束状态的任务，都将移动到新的任务流的起始状态,请谨慎操作。" type="warning" :closable="false" style="margin-bottom: 10px;" />
      <el-form ref="ruleForm" v-loading="formLoading" :model="ruleForm" :rules="rules" label-position="top">

        <el-form-item prop="work" label="工作流">
          <el-select v-model="ruleForm.work" style="width: 100%" filterable>
            <el-option v-for="item in workList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item prop="typeCode" label="应用工作项类型">
          <el-checkbox-group v-model="ruleForm.typeCode">
            <el-checkbox v-for="(item, index) in treeData" :key="index" :label="item.code" :disabled="item.code == typeCode ? true : false">{{
              item.name
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="changeFlow">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const TYPE = {
  issue: 1,
  defect: 2,
  task: 3
}
import { apiAlmWorkflowNoPage, apiAlmWorkflowSwitchWorkflow } from '@/api/vone/base/work-flow'

export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    typePermission: {
      type: String,
      default: ''
    },
    tfId: { // 当前工作流
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: 0
    },
    treeData: { // 当前分类下其它的工作项类型
      type: Array,
      default: () => []
    },
    typeCode: { // 当前工作项类型
      type: String,
      default: ''
    },
    type: { // 当前工作项分类
      default: undefined,
      type: String
    }
  },
  data() {
    return {
      TYPE,
      ruleForm: {
        typeCode: [this.typeCode]
      },
      workList: [],
      rules: {
        work: [{ required: true, message: '请选择工作流' }]
      },
      formLoading: false
    }
  },

  mounted() {
    this.getWork()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.ruleForm.resetFields()
    },
    // 查询所有任务流
    async getWork() {
      this.formLoading = true
      if (this.tfId) {
        this.$set(this.ruleForm, 'work', this.tfId)
      }
      const { data, isSuccess, msg } = await apiAlmWorkflowNoPage()
      this.formLoading = false
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }

      this.workList = data
    },
    // 提交工作流选项
    async changeFlow() {
      try {
        await this.$refs.ruleForm.validate()
      } catch (error) {
        return
      }
      const params = {
        typeCode: this.ruleForm.typeCode,
        workflowId: this.ruleForm.work,
        projectId: this.$route.params.id,
        typeClassify: this.type
      }
      if (this.id) {
        params.id = this.id
      }
      const { isSuccess, msg } = await apiAlmWorkflowSwitchWorkflow(params)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('切换工作流成功')
      this.$emit('success')
      this.onClose()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
