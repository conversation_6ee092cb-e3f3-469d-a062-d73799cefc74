
<template>
  <div class="pageBox pageContentNoH">
    <!-- 项目角色 -->
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="p-role-table"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          show-basic
          v-model:extra="extraData"
          @getTableData="getTableData"
        >
          <!-- <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名称" prop="name">
                <el-input v-model="formData.name" clearable placeholder="用户名称" />
              </el-form-item>
            </el-col>
          </el-row> -->
        </vone-search-dynamic>
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" size="small" :disabled="!$permission('project_setting_role_add')" @click="clickAddGroup">
          创建角色</el-button>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 192px - 50px )">
      <vxe-table
        ref="p-role-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="名称" field="name" min-width="150">
          <template #default="{ row }">
            <a @click="showInfo(row)">
              {{ row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="角色编号" field="code" min-width="150" />
        <vxe-column title="角色类型" field="type" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.type" effect="dark">公有</el-tag>
            <el-tag v-else type="warning" effect="dark">私有</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="项目类型" field="typeCode" width="100">
          <template #default="{ row }">
            {{ projectTypeName(row) }}
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="row.type || !$permission('project_setting_role_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="菜单资源" placement="top">
                <el-button type="text" :disabled="row.type || row.code == 'PM' || !$permission('project_setting_role_menu')" icon="iconfont el-icon-application-setting" @click="checkClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="row.type || !$permission('project_setting_role_del')" icon="iconfont el-icon-application-delete icon_click" @click="deleteRow(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    <!-- 创建项目角色 -->
    <el-dialog :title="titile" width="30%" v-model:visible="dialogFormVisible" :close-on-click-modal="false" :before-close="onClose">

      <el-form ref="groupForm" v-loading="formLoading" :model="groupForm" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="名称" />
        </el-form-item>
        <el-form-item label="标识" prop="code">
          <el-input v-model="groupForm.code" placeholder="标识" :disabled="titile == '编辑项目角色' ? true :false" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="groupForm.description" placeholder="描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd">确定</el-button>
      </div>
    </el-dialog>
    <GroupDrawer
      v-if="groupParam.visible"
      v-bind="groupParam"
      v-model:visible="groupParam.visible"
      @success="getTableData"
    />
  </div>
</template>

<script>
import { apiAlmProjectRole, apiAlmProjectRoleAdd, apiAlmProjectRoleInfo, apiAlmProjectRoleDel } from '@/api/vone/project/setting'
import GroupDrawer from './group-drawer.vue'

export default {
  components: { GroupDrawer },
  data() {
    return {
      defaultFileds: [
        {
          key: 'name',
          name: '角色名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入角色名称'
        }
      ],
      formData: {
        projectId: this.$route.params.id
      },
      extraData: {},
      groupForm: {
        projectId: this.$route.params.id,
        state: true,
        name: '',
        code: '',
        description: '',
        type: false
      },
      groupParam: { visible: false },
      pageLoading: false,
      tableData: {},
      selecteTableData: [],

      rules: {
        name: [
          { required: true, message: '请输入名称' }
        ],
        code: [{ required: true, message: '请输入标识' }]
      },
      titile: '创建项目角色',
      userList: [],
      dialogFormVisible: false,
      saveLoading: false,
      formLoading: false,
      checkData: []
    }
  },
  computed: {
    projectTypeName() {
      return function(row) {
        return row.echoMap?.typeCode?.name || ''
      }
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getStatus(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('project_setting_role_edit')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('project_setting_role_del')
    },
    getAuth(index, row) {
      // this.tableData.records[index].code == 'PM' ||
      return row.code == 'PM' || !this.$permission('project_setting_role_config')
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.groupForm.resetFields()
    },
    // 查询列表
    async getTableData() {
      let params = {}
      this.pageLoading = true
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData, projectId: this.$route.params.id, typeCode: this.$route.params.projectTypeCode }
      }
      const res = await apiAlmProjectRole(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    async sureAdd() {
      try {
        await this.$refs.groupForm.validate()
      } catch (e) {
        return
      }
      this.saveLoading = true
      this.$set(this.groupForm, 'projectId', this.$route.params.id)
      this.$set(this.groupForm, 'typeCode', this.$route.params.projectTypeCode)
      const res = await apiAlmProjectRoleAdd(this.groupForm).catch(() => {
        this.saveLoading = false
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.onClose()
      this.getTableData()
    },
    // 新增
    clickAddGroup() {
      this.dialogFormVisible = true
      this.titile = '创建项目角色'
    },
    // 编辑
    editClickRow(row) {
      this.dialogFormVisible = true
      this.getRoleInfo(row.id)
      this.titile = '编辑项目角色'
    },

    // 详情
    async getRoleInfo(val) {
      this.formLoading = true
      const res = await apiAlmProjectRoleInfo(val)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.groupForm = res.data
    },
    // 删除
    async deleteRow(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      })
      const { isSuccess, msg } = await apiAlmProjectRoleDel([row.id])
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableData()
    },
    // 配置资源
    checkClickRow(row) {
      this.groupParam = { visible: true, id: row.id, title: '配置菜单资源' }
    },
    // 查看菜单资源详情
    showInfo(row) {
      this.groupParam = { visible: true, id: row.id, title: '查看菜单资源' }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.pageBox {
  height: calc(100vh - 48px - 48px - 26px - 4px);
  padding: 16px 16px 10px 16px;
  overflow-y: auto;
}
.user{
  text-align: right;
}
// 44px-分页 48px-筛选 36px-表格头
.vone-vxe-table {
  :deep(.vxe-table--body-wrapper) {
    height: calc(100vh - #{$main-margin} - #{$main-margin} - #{$main-padding} - #{$main-padding} - #{$nav-top-height} - 44px - 48px - 36px - 58px);
    overflow-y: auto;
  }
}
</style>
