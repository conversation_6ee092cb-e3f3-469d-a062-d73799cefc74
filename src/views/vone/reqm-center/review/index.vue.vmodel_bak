<template>
  <!-- vone-custom-table -->
  <page-wrapper>
    <el-table
      ref="ieda-table"
      :loading="tableLoading"
      :table-options="tableOptions"
      row-key="id"
      table-key="ieda-table"
      default-expand-all
      :tree-props="{
        children: 'children',
        hasChildren: 'hasChildren',
      }"
      :table-data="tableData"
      @getTableData="getInitTableData"
      @selecteTableData="selecteTableData"
    >
      <vone-search-dynamic slot="search" :default-filter="true" class="search" v-model:model="formData" label-position="top" @getTableData="getInitTableData">

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户需求名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入用户需求名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="用户需求来源" prop="sourceCodes">
              <el-select v-model="formData.sourceCodes" clearable style="width:100%" filterable multiple>
                <el-option v-for="item in sourceList" :key="item.key" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属产品" prop="productIds">
              <el-select v-model="formData.productIds" clearable filterable style="width:100%" multiple @focus="setOptionWidth">
                <el-option v-for="item in productIdList" :key="item.id" :label="item.name" :value="item.id" :style="{width:selectOptionWidth}" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="优先级" prop="priorityCodes">
              <vone-icon-select v-model="formData.priorityCodes" :data="prioritList" filterable clearable style="width:100%" multiple>
                <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                  <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态" prop="stateCodes">
              <el-select v-model="formData.stateCodes" clearable style="width:100%" filterable multiple>
                <el-option v-for="(item,i) in stateList" :key="i" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提出人" prop="putBys">
              <vone-remote-user v-model="formData.putBys" :default-data="putByData" multiple />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="处理人" flex prop="handleBys">
              <vone-remote-user v-model="formData.handleBys" :default-data="handleBytData" multiple />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="负责人" flex prop="leadingBys">
              <vone-remote-user v-model="formData.leadingBys" :default-data="leadingByData" multiple />
            </el-form-item>
          </el-col>
        </el-row>
      </vone-search-dynamic>
      <el-row slot="actions">
        <el-row type="flex" justify="space-between">
          <div>
            <el-button
              icon="iconfont el-icon-tips-plus-circle"
              type="primary"
              @click.stop="newIssue"
            >新建</el-button>
          </div>

        </el-row>
      </el-row>
      <template>
        <el-table-column
          show-overflow-tooltip
          prop="name"
          label="评审工单"
          fixed
          min-width="300"
          class-name="name_col"
        >
          <template slot-scope="{ row }">
            <div class="name_icon">
              <a class="table_title" @click="showInfo(row)">
                <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
                  <i :class="`iconfont ${row.echoMap.typeCode.icon}`" :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}" />
                </span>

                {{ row.code + " " + row.name }}

              </a>

            </div>

          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="截止时间"
          width="110"
          sortable
        >
          <template slot-scope="scope">
            <span v-if="scope.row.createTime">
              {{ dayjs(scope.row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ scope.row.createTime }}</span>

          </template>
        </el-table-column>
        <el-table-column
          prop="stateCode"
          label="状态"
          width="120"
          sortable
        >
          <template slot-scope="scope">
            <ideaStatus
              v-if="scope.row"
              :key="Date.now()"
              :workitem="scope.row"
              :no-permission="!$permission('reqm_center_idea_flow')"
              @changeFlow="getInitTableData"
            />
          </template>
        </el-table-column>

        <el-table-column
          prop="handleBy"
          label="发起人"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.handleBy && scope.row.echoMap && scope.row.echoMap.handleBy">
              <vone-user-avatar
                :avatar-path="scope.row.echoMap.handleBy.avatarPath"
                :name="scope.row.echoMap.handleBy.name"
              />
            </span>

          </template>
        </el-table-column>

        <el-table-column
          prop="expectedTime"
          label="评审规则"
          width="200"
          sortable
        >
          <template slot-scope="scope">
            <span v-if="scope.row.expectedTime">
              {{ dayjs(scope.row.expectedTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ scope.row.expectedTime }}</span>

          </template>
        </el-table-column>

      </template>
    </el-table>

    <!-- 创建完整用户需求 -->
    <edit
      v-if="issueParam.editvisible"
      :key="issueParam.key"
      v-model:visible="issueParam.editvisible"
      v-bind="issueParam"
      @success="getInitTableData"
    />
    <add
      v-if="issueParam.addvisible"
      :key="issueParam.key"
      v-model:visible="issueParam.addvisible"
      v-bind="issueParam"
      @success="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll v-if="editAllParam.visible" v-bind="editAllParam" v-model:visible="editAllParam.visible" :type-code="'IDEA'" @success="getInitTableData" />
  </page-wrapper>
</template>

<script>
const list = [
  {
    id: '-1',
    name: '未设置'
  }
]
import { apiAlmIdeaPage, apiAlmIdeaDel } from '@/api/vone/reqmcenter/idea'
import edit from './function/edit/edit'
import add from './function/add/add'
import ideaStatus from '@/views/vone/project/common/change-status/index.vue'
// 筛选
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { apiAlmProjectNoPage, productListByCondition, apiProgramNoPage } from '@/api/vone/project/index'

// import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { ideaListQuery } from '@/api/vone/reqmcenter/idea'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import _ from 'lodash'

import {
  findByClassify
} from '@/api/vone/reqmcenter/require'
import editAll from '../../project/common/edit-all'
import { apiAlmIdeaAddOrEdit } from '@/api/vone/reqmcenter/idea'
export default {
  components: {
    edit,
    add,
    ideaStatus,
    editAll
  },
  data() {
    return {
      tableList: [], // 用于编辑时切换上一个下一个
      selectOptionWidth: '',
      sourceList: [], // 需求来源
      projectIdList: [], // 归属项目
      productIdList: [], // 归属产品

      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 需求分类
      planIdList: [], // 迭代计划
      programIdList: [], // 项目集
      ideaList: [], // 用户需求
      createSimple: false,
      formData: {
        projectId: this.$route.params.id,
        typeCodes: [],
        sourceCodes: [],
        productIds: [],
        priorityCodes: [],
        stateCodes: []
      },
      tableLoading: false,
      tableData: {},
      tableSelected: [],
      tableOptions: {
        isOperation: true,
        isSelection: true,
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon',
              label: '审批',
              icon: 'el-icon-video-play',
              handler: this.getassessment
            },
            {
              disabled: !this.$permission('reqm_center_idea_edit'),
              type: 'icon',
              label: '编辑',
              icon: 'iconfont el-icon-application-edit',
              handler: this.editIssue
            },
            {
              disabled: !this.$permission('reqm_center_idea_del'),
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteIssue // 操作事件
            }
          ],
          // 更多操作按钮
          moreData: []
        }
      },
      selectData: [],
      issueParam: {
        visible: false
      },
      editAllParam: { visible: false }, // 批量编辑
      putByData: list,
      handleBytData: list,
      leadingByData: list
    }
  },

  mounted() {
    this.getIssueType() // 需求分类
    this.getProjectList() // 归属项目
    this.productList() // 归属产品

    this.getStateList() // 状态
    this.getPlanList() // 迭代计划
    this.getPrioritList() // 优先级
    this.getSourceList() // 来源
    this.getProgramIdList() // 项目集
    this.getList() // 用户需求
    this.getInitTableData()
  },
  created() {
    const params = this.$route.params
    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }
  },
  methods: {
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    async userLists(val) {
      this.$set(val, 'tagId', val.tag)
      const params = _.omit(val, ['tag'])
      const res = await apiAlmIdeaAddOrEdit(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.getInitTableData()
    },
    async getList() {
      const { code, data } = await ideaListQuery()
      if (code == 0) {
        this.ideaList = data
      }
    },
    // 查询需求分类
    async getIssueType() {
      const res = await findByClassify('IDEA')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
    },
    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'IDEA'
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 归属项目集
    async getProgramIdList() {
      const res = await apiProgramNoPage()
      if (!res.isSuccess) {
        return
      }
      this.programIdList = res.data
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.productIdList = res.data
    },
    // 迭代计划
    async getPlanList() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      // this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    // 获取table 选中的行
    selecteTableData(val) {
      this.selectData = val
    },
    // 批量删除
    async deleteTableSelect() {
      if (this.selectData.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        }).then(async() => {
          const deleteArr = []
          this.selectData.map(item => deleteArr.push(item.id))
          const res = await apiAlmIdeaDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 初始化进入页面列表
    async getInitTableData(val) {
      this.tableLoading = true
      let params = {}

      const tableAttr = this.$refs['ieda-table'].exportTableQueryData()
      if (val) {
        if (val == 'reset') {
          this.$refs.searchItem.formData = {}
          this.formData = {}
        }
      }
      if (this.$refs.searchItem) {
        this.formData = this.$refs.searchItem.formData
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiAlmIdeaPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
    },
    // 复制标题到剪贴板
    copy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(function(e) {
        _this.$message.success('复制成功')
      }, function(e) {
        _this.$message.warning(' 该浏览器不支持自动复制')
      })
    },
    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新建评审',
        key: Date.now(),
        infoDisabled: false
      }
    },
    getassessment(row) {
      this.$router.push({
        path: '/reqmcenter/require/requireList/trackingView'
      })
      this.$router.push({
        path: '/reqmcenter/review/list/details'
      })
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑评审',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList
      }
    },
    showInfo(row) {
      this.issueParam = {
        editvisible: true,
        title: '用户需求评审',
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList
      }
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false
      })

      const { isSuccess, msg } = await apiAlmIdeaDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量编辑
    editAll() {
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    }
  }
}
</script>

<style lang="scss" scoped>

:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.search{
  display: inline-block;
  // margin-right:20px;
}
.noSetting{
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.drawer .vone-el-drawer__layout {
  overflow: hidden;
}

</style>
<style >
.userList .el-input__inner{
border: 0;
}
.userList .el-input__icon {
display: none;
}
</style>
