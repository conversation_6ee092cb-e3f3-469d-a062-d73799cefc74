<template>
  <div class="drawerBox">
    <vone-drawer v-model:visible="visible" :before-close="onClose" :modal="false" size="lg" v-on="$listeners">
      <div slot="title" class="drawer-title">
        <div class="drawer-title-text">{{ title }}【{{ issueForm.code }}】
          <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />

          <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
        </div>
        <!-- <div class="drawer-title-operation">
          <el-tooltip content="模版配置" placement="top">
            <i class="iconfont el-icon-application-setting" @click="setting" />
          </el-tooltip>
        </div> -->
      </div>
      <el-form ref="issueForm" v-loading="pageLoading" :disabled="infoDisabled" :model="issueForm" label-position="top" :rules="rules">
        <el-row>
          <el-col :span="18" class="leftForm">
            <el-form-item label="评审工单" prop="name">
              <el-input v-model="issueForm.name" placeholder="请输入评审工单" :disabled="infoDisabled" />
            </el-form-item>
            <el-tabs v-model="tabActive" @tab-click="handleClick">
              <el-tab-pane label="基本信息" name="basic">
                <el-form-item label="用户需求描述" prop="description">
                  <vone-editor ref="editor" v-model="issueForm.description" :preview="infoDisabled" @input.native="eventDisposalRangeChange(issueForm.description)" />
                </el-form-item>
                <el-form-item label="附件" prop="files" class="fileLoad">
                  <vone-upload ref="bugUploadFile" biz-type="IDEA_FILE_UPLOAD" :files-data="id ? issueForm.files : []" />
                  <!-- <vone-file-view :list="bugForm.files" :size="157" from-source="BUG_FILE_UPLOAD" /> -->
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane v-if="id" label="关联需求" name="requiret">
                <idea :id="id" />
              </el-tab-pane>
              <el-tab-pane v-if="id" label="活动" name="active">
                <activeTab v-if="tabActive == 'active'" :form-info="issueForm" :type="'idea'" />
              </el-tab-pane>
              <el-tab-pane v-if="id" label="评论" name="comment">
                <el-form>
                  <vone-comment :source-id="id" source-type="IDEA" />
                </el-form>
              </el-tab-pane>
            </el-tabs>

          </el-col>
          <el-col :span="6" class="rightForm">
            <infoForm :id="id" ref="infoForm" :info-disabled="infoDisabled" @save="save" @changeFlow="changeFlow" />
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="footer">

        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
        <el-button @click="onClose">取消</el-button>
      </div>

    </vone-drawer>
    <demo v-if="issueParam.demoDiolog" :key="issueParam.key" v-model:visible="issueParam.demoDiolog" v-bind="issueParam" @success="getInitTableData" />
  </div>
</template>

<script>

import { apiAlmIdeaAddOrEdit, apiAlmIdeaInfo } from '@/api/vone/reqmcenter/idea'
import demo from '../../../demo/edit'
import activeTab from '@/components/CustomEdit/commonTab/active.vue'
import infoForm from './infoForm.vue'
import idea from './idea.vue'
export default {
  components: {
    infoForm,
    idea,
    demo,
    activeTab
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    tableList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入用户需求名称', trigger: 'blur' }],
        description: [
          { required: false, message: '请输入描述', trigger: 'change' },
          { max: 1000, message: '描述长度限制在1000字符以内', trigger: 'change' }
        ]
      },
      issueParam: {
        demoDiolog: false
      },
      issueForm: {},
      tabActive: 'basic',
      saveNext: false,
      saveSplit: false,
      noEdit: null,
      saveLoading: false,
      pageLoading: false,
      currentIndex: undefined // 当前数据的索引
    }
  },

  mounted() {
    this.currentIndex = this.tableList.findIndex(item => item.id === this.id)
    if (this.id) {
      this.getIdeaInfo()
    }
  },
  methods: {
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor.$el.innerText.replace(/[|]*\n/, '').length
      if (textLength >= 1000) {
        this.$refs.issueForm.validateField(['description'])
      } else {
        this.$refs.issueForm.clearValidate(['description'])
      }
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    },
    setting() {
      this.issueParam = {
        demoDiolog: true,
        title: '编辑配置'
      }
    },
    onClose() {
      this.splitFlag = false
      this.$emit('update:visible', false)

      this.saveSplit = false
      this.saveNext = false
    },
    handleClick(tab) {

    },
    // 保存
    async saveInfo() {
      const textLength = this.$refs.editor.$el.innerText.replace(/[|]*\n/, '').length
      if (textLength >= 1000) {
        this.$refs.issueForm.validate((valid) => {
          if (valid) {
            this.$refs.infoForm.proving()
          } else {
            return
          }
        })
      } else {
        const { name } = this.issueForm
        if (name) {
          this.$refs.infoForm.proving()
          // 执行校验成功的相关操作
        } else {
          this.$refs['issueForm'].validateField(['name'])
        }
      }
      // this.$refs.issueForm.validate((valid) => {
      //   if (valid) {
      //     this.$refs.infoForm.proving()
      //   } else {
      //     return
      //   }
      // })
    },
    async save() {
      this.$set(this.issueForm, 'files', this.$refs['bugUploadFile'].uploadFiles)
      const params = {
        ...this.issueForm,
        ...this.$refs.infoForm.infoForm
      }

      // if (params.projectDocuments && params.projectDocuments.length) {
      //   const spaceIdList = this.$refs.infoForm.documentsList
      //   params.projectDocuments = params.projectDocuments.map(r => ({
      //     documentId: r,
      //     spaceId: spaceIdList.find(j => j.id == r).spaceId
      //   }))
      // } else {
      //   params.projectDocuments = []
      // }
      const res = await apiAlmIdeaAddOrEdit(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('success')
      this.onClose()
    },
    nextChange() { },
    // 详情
    async getIdeaInfo(val) {
      this.pageLoading = true

      const res = await apiAlmIdeaInfo(val || this.id)

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.issueForm = res.data
      await this.$nextTick(() => {
        this.$refs.infoForm.infoForm = res.data
        // if (this.$refs.infoForm.infoForm.projectDocuments.length) {
        //   var arr = []
        //   this.$refs.infoForm.infoForm.projectDocuments.forEach(item => {
        //     arr.push(item.documentId)
        //   })
        //   this.$refs.infoForm.infoForm.projectDocuments = arr
        // } else {
        //   this.$refs.infoForm.infoForm.projectDocuments = []
        // }
      })
      await this.$refs.infoForm.getProjectInfo()
      // this.$refs.infoForm.getSourceList(res.data.typeCode)
      // this.$refs.infoForm.getPrioritList(res.data.typeCode)

      this.pageLoading = false
    },
    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.$bus.$emit('commentChange', this.tableList[this.currentIndex].id)
      this.getIdeaInfo(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.$bus.$emit('commentChange', this.tableList[this.currentIndex].id)
      this.getIdeaInfo(this.tableList[this.currentIndex].id)
    }

  }
}
</script>
<style lang="scss" scoped>
.drawerBox {
  height: calc(100vh - 100px);
  overflow: hidden;
  .leftForm {
    padding: 12px 20px;
    height: calc(100vh - 106px);
    overflow-y: overlay;
    overflow-x: hidden;
  }
  .drawer-title {
    display: flex;
    align-content: center;
  }
  .drawer-title-text {
    flex: 1;
    height: 24px;
    line-height: 24px;
  }
  .drawer-title-operation {
    height: 24px;
    line-height: 22px;
    cursor: pointer;
  }
  .drawer .vone-el-drawer__layout {
    overflow: hidden;
  }
  .rightForm {
    border-left: 1px solid var(--disabled-bg-color, #ebeef5);
    padding: 12px 20px;
    height: calc(100vh - 106px);
    overflow-y: overlay;
    overflow-x: hidden;
  }
}
.fileLoad {
  :deep(.el-form-item__content) {
    width: 90%;
    float: right;
  }
}
</style>

