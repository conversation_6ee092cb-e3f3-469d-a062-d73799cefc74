<template>
  <!-- 新增自定义属性 -->
  <el-dialog :title="title + '导出'" width="40%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false">
    <div v-loading="pageLoading">

      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="checkAllChange">全选</el-checkbox>
      <div style="margin: 15px 0" />

      <el-checkbox-group v-model="checkedList" @change="checkChange">
        <el-row>
          <template v-for="(item, index) in chooseList">
            <el-col :key="index" :span="6">
              <el-checkbox :label="item.key">{{ item.name }}</el-checkbox>
            </el-col>
          </template>
        </el-row>
      </el-checkbox-group>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { apiBaseFormProperty } from '@/api/vone/base/index'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    typeCode: {
      type: String,
      default: undefined
    },
    title: {
      type: String,
      default: undefined
    },
    formData: { // 筛选条件
      type: Object,
      default: () => {}
    },
    url: { // 路由
      type: String,
      default: undefined
    }

  },
  data() {
    return {
      rules: {
      },
      pageLoading: false,
      addForm: {
      },
      checkAll: false,
      checkedList: [],
      chooseList: [],
      saveLoading: false,
      isIndeterminate: true
    }
  },
  mounted() {
    this.getProtery()
  },
  methods: {

    onClose() {
      this.$emit('update:visible', false)
    },
    checkAllChange(val) {
      this.checkedList = val ? this.groupOption : []

      this.isIndeterminate = false
    },
    checkChange(value) {
      const checkedList = value.length

      this.checkAll = checkedList === this.groupOption.length
      this.isIndeterminate =
        checkedList > 0 && checkedList < this.groupOption.length
    },
    async getProtery() {
      this.pageLoading = true
      const res = await apiBaseFormProperty(this.typeCode)
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.chooseList = res.data && res.data.customFormFields.length ? res.data.customFormFields : []
      this.groupOption = this.chooseList.map(r => r.key)
    },

    // 保存
    async saveInfo() {
      this.saveLoading = true
      download(`${this.title}信息.xls`, await apiBaseFileLoad(
        this.url, this.formData
      ))
      this.onClose()

      this.saveLoading = false
    }

  }

}
</script>

<style lang="scss" scoped>
:deep(.el-col-6) {
  height: 25px;
}
</style>

