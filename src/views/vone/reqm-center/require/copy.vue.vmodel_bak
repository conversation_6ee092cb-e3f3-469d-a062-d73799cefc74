<template>
  <page-wrapper ref="lane">
    <vone-search-wrapper v-if="showView!=='billboard'">
      <template slot="search">
        <div class="vone-tabs">
          <el-tabs v-model="showView">
            <el-tab-pane name="table" label="表格视图" />
            <el-tab-pane name="billboard" label="看板视图" />
            <el-tab-pane name="tree" label="层级视图" />
          </el-tabs>
        </div>

        <vone-search-dynamic
          v-if="showView=='table'"
          ref="searchForm"
          table-search-key="reqm-require-table"
          v-model:model="formData"
          show-basic
          :show-column-sort="true"
          v-model:extra="extraData"
          v-model:default-fileds="defaultFileds"
          :table-ref="$refs['reqm-req-table']"
          @getTableData="getInitTableData"
          @showPopovers="showPopovers"
        />
      </template>

      <template v-if="showView=='table'" slot="actions">
        <el-row type="flex" align="middle">

          <simpleAddIssue v-if="createSimple" style="margin-right:16px;" @success="getInitTableData" @cancel="createSimple = false" />

          <el-button-group>

            <el-tooltip content="快速新增" placement="top">
              <el-button
                :disabled="!$permission('reqm-center-require-add')"
                class="subBtton"
                :icon="`iconfont  ${
                  createSimple ? 'el-icon-direction-double-left' : 'el-icon-direction-double-down'
                }`"
                type="primary"
                @click.stop="createSimple = !createSimple"
              />
            </el-tooltip>
            <el-button :disabled="!$permission('reqm-center-require-add')" icon="iconfont el-icon-tips-plus-circle" type="primary" @click.stop="newIssue">新增</el-button>
          </el-button-group>
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>

    <div v-if="showView=='table'" :style="{height: $tableHeight}">
      <vxe-table
        ref="reqm-req-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="标题" field="name" min-width="480" fixed="left" class-name="name_col" tree-node>
          <template #default="{ row }">
            <div class="name_icon">
              <a class="mx-1 table_title" @click="showInfo(row)">
                <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">

                  <i :class="`iconfont ${row.echoMap.typeCode.icon}`" :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}" />
                </span>
                {{ row.code + " " + row.name }}</a>
              <!-- 是否延期 -->
              <span v-if="row.delay" style="position:absolute;left:-5px">
                <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top-start">
                  <i class="el-icon-warning-outline color-danger ml-2" />
                </el-tooltip>
              </span>

            </div>

          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="100" sortable>
          <template #default="{ row, rowIndex }">
            <issueStatus
              v-if="row"
              :workitem="row"
              :no-permission="!$permission('reqm_center_require_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.handleBy" class="remoteuser" :default-data="[row.echoMap.handleBy]" :disabled="!$permission('reqm-center-require-edit')" @change="workitemChange(row,$event, 'handleBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.putBy" class="remoteuser" :default-data="[row.echoMap.putBy]" :disabled="!$permission('reqm_center_idea_edit')" @change="workitemChange(row,$event, 'putBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="120" show-overflow-tooltip sortable>
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.leadingBy" class="remoteuser" :default-data="[row.echoMap.leadingBy]" :disabled="!$permission('reqm_center_idea_edit')" @change="workitemChange(row,$event, 'leadingBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="planEtime" title="计划完成时间" show-overflow-tooltip width="135" sortable>
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planEtime }}</span>

          </template>
        </vxe-column>
        <vxe-column field="rateProgress" title="进度" width="80" sortable>
          <template slot-scope="{ row }">
            <el-tooltip placement="top" :content="`${row.rateProgress}%`">
              <el-progress :percentage="row.rateProgress ? parseInt(row.rateProgress) :0" :color="'var(--main-theme-color,#3e7bfa)'" :show-text="false" />
            </el-tooltip>

          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="100" sortable>
          <template #default="{ row }">
            <vone-icon-select v-model="row.priorityCode" :data="prioritList" filterable clearable style="width:100%" class="userList" :no-permission="!$permission('reqm_center_require_priority')" @change="workitemChange(row,$event,'priorityCode')">
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column field="sourceCode" title="需求来源" show-overflow-tooltip width="100">
          <template #default="{ row }">
            <span
              v-if="
                row.sourceCode &&
                  row.sourceCode &&
                  row.echoMap.sourceCode
              "
            >
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column show-overflow-tooltip field="projectId" title="归属项目" width="90">
          <template #default="{ row }">
            <span
              v-if="
                row.projectId &&
                  row.echoMap &&
                  row.echoMap.projectId
              "
            >
              {{ row.echoMap.projectId.name }}
            </span>
            <span v-else>{{ row.projectId }}</span>
          </template>
        </vxe-column>
        <vxe-column show-overflow-tooltip field="planId" title="归属计划" width="90">
          <template #default="{ row }">
            <span v-if=" row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item,index) in row.tag" :key="index">
              <el-tag style="margin-right:6px" type="success">
                {{ item }}
              </el-tag>
            </span>

          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('reqm-center-require-edit')" icon="iconfont el-icon-application-edit" @click="editIssue(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="!$permission('reqm-center-require-del')" icon="iconfont el-icon-application-delete" @click="deleteIssue(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more icon_click" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in moreOpear" :key="item.name" :command="() =>item.handler(row)" :disabled="item.disabled">
                    <i :class=" item.icon " />
                    <span>{{ item.label }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>

    </div>
    <vone-pagination v-if="showView=='table'" ref="pagination" :total="tableData.total" @update="getInitTableData" />
    <!-- 看板视图 -->
    <div v-if="showView=='billboard'">
      <billboard ref="billboard" @changeView="changeView" />
    </div>
    <!-- 层级视图 -->
    <div v-if="showView=='tree'">

      <tree :show-view="showView" :type-code-list="typeCodeList" :source-list="sourceList" :priorit-list="prioritList" :state-list="stateList" @change="changeView" />
    </div>
    <!-- 编辑 -->
    <vone-custom-edit v-if="issueParam.editvisible" :key="issueParam.key" v-model:visible="issueParam.editvisible" v-bind="issueParam" :type-code="'ISSUE'" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData" @hideTab="hideTab" />

    <!-- 新增 -->
    <vone-custom-add v-if="issueParam.addvisible" :key="issueParam.key" v-model:visible="issueParam.addvisible" v-bind="issueParam" :title="'新增需求'" :type-code="'ISSUE'" @success="getInitTableData" />

    <!-- 详情 -->
    <vone-custom-info v-if="issueInfoParam.editvisible" v-model:visible="issueInfoParam.editvisible" v-bind="issueInfoParam" :type-code="'ISSUE'" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData" @hideTab="hideTab" />

    <!-- 批量编辑 -->
    <editAll v-if="editAllParam.visible" v-bind="editAllParam" v-model:visible="editAllParam.visible" :type-code="'ISSUE'" is-req @success="getInitTableData" />

    <!-- 导入 -->
    <vone-import-file v-if="importParam.visible" v-bind="importParam" v-model:visible="importParam.visible" @success="getInitTableData" />

    <!-- 批量编辑 -->
    <editAll v-if="editAllParam.visible" v-bind="editAllParam" v-model:visible="editAllParam.visible" :type-code="'ISSUE'" is-req @success="getInitTableData" />

  </page-wrapper>
</template>

<script>
const list = [
  {
    id: '-1',
    name: '未设置'
  }
]
import { mapState } from 'vuex'
import { requirementDel, findByClassify } from '@/api/vone/reqmcenter/require'

import { apiAlmIssueInfo } from '@/api/vone/project/issue'

import { apiAlmPriorityNoPage, almReqmList } from '@/api/vone/alm/index'
import { apiBaseFileLoad } from '@/api/vone/base/file'

import { download, catchErr } from '@/utils'

// import billboard from './billboard/index'
// import tree from './table-tree.vue'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import simpleAddIssue from './function/simple-add-issue.vue'
import editAll from '../../project/common/edit-all'
import { editById, getWorkItemState } from '@/api/vone/project/index'

import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'

export default {
  components: {

    // billboard,
    issueStatus,
    // tree,
    simpleAddIssue,
    editAll
  },
  data() {
    return {
      extraData: {}, defaultFileds: [
        {
          key: 'name',
          name: '标题',
          type: {
            code: 'INPUT'
          },
          isBasic: true,
          placeholder: '请输入标题'
        },
        {
          key: 'sourceCode',
          name: '来源',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择来源',
          multiple: true

        },
        {
          key: 'productId',
          name: '所属产品',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择所属产品',
          multiple: true,
          valueType: 'id'
        },
        {
          key: 'priorityCode',
          name: '优先级',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择优先级',
          multiple: true
        },
        {
          key: 'stateCode',
          name: '状态',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择状态',
          multiple: true
        },
        {
          key: 'putBy',
          name: '提出人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择提出人',
          multiple: true
        },
        {
          key: 'handleBy',
          name: '处理人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择处理人',
          multiple: true
        },
        {
          key: 'leadingBy',
          name: '负责人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择负责人',
          multiple: true
        },
        {
          key: 'tagId',
          name: '标签',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择标签',
          multiple: true
        }
      ],
      moreOpear: [
        {
          type: 'icon', // 为icon则是图标
          label: '复制标题', // 功能名称
          icon: 'iconfont el-icon-application-copy', // icon class
          handler: this.copy // 操作事件
        }, {
          disabled: !this.$permission('reqm_center_graph'),
          type: 'icon', // 为icon则是图标
          label: '拓扑', // 功能名称
          icon: 'iconfont el-icon-application-topology', // icon class
          handler: this.gotoTuoPu // 操作事件
        }
      ],
      issueInfoParam: { // 详情
        visible: false
      },
      tableList: [], // 用于编辑时切换上一个下一个
      formData: {},
      sourceList: [], // 需求来源
      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 需求分类
      createSimple: false,
      view: '表格视图',
      selectData: [],
      showView: 'table',
      tableLoading: false,
      tableData: {},
      issueParam: {
        visible: false,
        demoDiolog: false
      },
      actions: [
        {
          name: '批量删除',

          fn: this.deleteTableSelect,
          disabled: !this.$permission('reqm-center-require-del')
        },
        {
          name: '批量编辑',

          fn: this.editAll,
          disabled: !this.$permission('reqm-center-require-edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('reqm_center_issue_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('reqm_center_issue_export')
        }

      ],
      importParam: { visible: false }, // 用户导入
      exportLoading: false,
      editAllParam: { visible: false }, // 批量编辑
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [
        {
          label: '需求',
          name: 'IssueToIssue'
        },
        {
          label: '测试用例',
          name: 'TestCase'
        },
        {
          label: '关联代码',
          name: 'DevelopTab'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      requierName: state => state.project.requierName
    })
  },
  watch: {
    showView(val) {
      if (val == 'table') {
        // this.showBorder = false
        this.$nextTick(() => {
          this.getInitTableData()
        })
      }
    },
    '$route.params.id': {
      handler(val) {
        if (this.$route.params.type == 'comment') {
          this.showInfo({ id: this.$route.params.businessId })
        }
      },
      immediate: true,
      deep: true
    }
  },
  async mounted() {
    this.getIssueType() // 需求分类
    this.getStateList() // 状态
    this.getPrioritList() // 优先级

    this.getSourceList()
    this.productList()
    // 如果是从别的页面跳转过来，例如用户需求拆分需求
    if (this.$route.query.showDialog) {
      this.issueInfoParam = {
        editvisible: true,
        title: '需求详情',
        id: this.$route.query.queryId,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: this.$route.query.rowTypeCode,
        stateCode: this.$route.query.stateCode,
        rowProjectId: this.$route.query.projectId
      }
    }
  },
  beforeDestroy() {
    this.$store.dispatch('project/getName', '')
  },
  created() {
    const params = this.$route.params
    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }
  },
  methods: {
    // 数据源接口返回数据以后,把值塞到筛选器模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('requirement', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    showPopovers() {

    },
    changeView(val) {
      this.showView = val
    },
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIssueInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(res.data, 'tag', res.data.echoMap.tagId ? res.data.echoMap.tagId.map(r => r.name) : [])
      }
      this.tableData.records.splice(index, 1, res.data)
    },

    // 导入
    imPort() {
      this.importParam = { visible: true, title: '需求', url: '/api/alm/alm/requirement/excel/downloadImportTemplate', importUrl: '/api/alm/alm/requirement/excel/import' }
    },

    // 导出
    async exportFlie() {
      try {
        this.exportLoading = true
        download(`需求信息.xls`, await apiBaseFileLoad(
          '/api/alm/alm/requirement/excel/export', this.formData
        ))
        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
        return
      }
    },

    // 查询需求分类
    async getIssueType() {
      const res = await findByClassify('ISSUE')
      if (!res.isSuccess) {
        return
      }

      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },

    // 查状态
    async getStateList() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      // this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },
    // 批量删除
    async deleteTableSelect() {
      this.selectData = this.getVxeTableSelectData('reqm-req-table')
      if (this.selectData.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        }).then(async() => {
          const deleteArr = []
          this.selectData.map(item => deleteArr.push(item.id))
          const res = await requirementDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },

    // 复制标题到剪贴板
    copy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(function(e) {
        _this.$message.success('复制成功')
      }, function(e) {
        _this.$message.warning(' 该浏览器不支持自动复制')
      })
    },

    // 初始化进入页面列表
    async getInitTableData() {
      this.tableLoading = true
      let params = {}
      if (this.requierName) {
        this.$set(this.formData, 'name', this.requierName)
      }
      const tableAttr = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...tableAttr,
        ...sortObj,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await almReqmList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(element => {
        element.tag = element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId ? element.echoMap.tagId.map(r => r.name) : []
      })
      this.tableData = res.data
      this.tableData.total = res.data.total
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
    },
    gotoTuoPu(row) {
      this.$router.push({
        path: '/reqmcenter/require/requireList/graph/' + row.id,
        query: {
          code: row.code
        }
      })
    },
    getTracking(row) {
      this.$router.push({
        path: '/reqmcenter/require/requireList/trackingView'
      })
    },
    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新增需求',
        key: Date.now(),
        infoDisabled: false
      }
    },
    hideTab(type) {
      if (type == 0) {
        this.rightTabs = [
          {
            active: true,
            label: '评论',
            name: 'comment'
          },
          {
            label: '活动',
            name: 'active'
          }
        ]
      } else {
        this.rightTabs = [
          {
            active: true,
            label: '评论',
            name: 'comment'
          },
          {
            label: '活动',
            name: 'active'
          },
          {
            label: '工时',
            name: 'workTime'
          }
        ]
      }
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑需求',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
    },
    showInfo(row) {
      this.issueInfoParam = {
        key: Date.now(),
        editvisible: true,
        title: '需求详情',
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
      this.projectId = row.projectId
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })

      const { isSuccess, msg } = await requirementDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量编辑
    editAll() {
      this.selectData = this.getVxeTableSelectData('reqm-req-table')
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    },
    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.defaultFileds, 'sourceCode', res.data)
      this.sourceList = res.data
    },

    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.productIdList = res.data
      this.setData(this.defaultFileds, 'productId', res.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-main) {
  padding: 0;
  margin-left: 12px;
}
:deep(.vone-tabs .el-tabs__item) {
  padding: 0 10px !important;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-main) {
  padding: 0;
  margin-left: 12px;
}

:deep(.name_col) {

  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.search {
  display: inline-block;
  // margin-right:20px;
}
.vone-tabs:deep(.el-tabs__header) {
  margin: 0 !important;
}

.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
<style >
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}

</style>
