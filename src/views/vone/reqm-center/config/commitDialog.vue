<template>
  <div>
    <el-dialog :title="title" :append-to-body="true" width="35%" v-model="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      1111111
    </el-dialog>
  </div>

</template>

<script>

export default {
  name: 'CommitDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {

    onClose() {
      this.$emit('update:visible', false)
      // this.$refs.worktimeForm.resetFields()
    }

  }
}
</script>
<style lang="scss" scoped>

</style>

