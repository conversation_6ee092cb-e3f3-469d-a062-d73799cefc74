<template>
  <div>
    <el-dialog
      class="dialog"
      title="编辑配置"
      v-model:visible="visible"
      width="100%"
      top="0"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form v-loading="pageLoading" :model="issueForm" label-position="left">
        <el-row>
          <el-col class="leftForm" :span="17">
            <el-form-item label="标题">
              <el-input v-model="issueForm.name" placeholder="请输入需求名称" :disabled="infoDisabled" style="width:80%" />
            </el-form-item>
            <el-tabs v-model="tabActive" style="margin-top:30px" @tab-click="handleClick">

              <el-tab-pane label="基本信息" name="basic">
                <infoForm :id="id" ref="infoForm" :info-disabled="infoDisabled" :part-info="infos" @changeFlow="changeFlow" />
              </el-tab-pane>
            </el-tabs>

          </el-col>
          <el-col :span="6" :offset="1">
            <setting @callBackInfo="handleInfo" />

          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { requirementAddOrEdit, requirementInfo } from '@/api/vone/reqmcenter/require'

import infoForm from './infoForm.vue'
import setting from './setting.vue'
export default {
  components: {
    infoForm,
    setting
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: '1468846298550501376'
    },
    infoDisabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      infos: {},
      issueForm: {},
      tabActive: 'basic',
      saveNext: false,
      saveSplit: false,
      noEdit: null,
      saveLoading: false,
      pageLoading: false

    }
  },

  mounted() {
    if (this.id) {
      this.getIssueInfo()
    }
  },
  methods: {
    handleInfo(data) {
      this.infos = data
    },
    onClose() {
      this.splitFlag = false
      this.$emit('update:visible', false)

      this.saveSplit = false
      this.saveNext = false
    },
    handleClick(tab) {
    },
    // 保存
    async saveInfo() {
      const params = {
        ...this.issueForm,
        ...this.$refs.infoForm.infoForm
      }
      const res = await requirementAddOrEdit(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('success')
      this.onClose()
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    },
    nextChange() { },
    // 详情
    async getIssueInfo() {
      this.pageLoading = true
      const res = await requirementInfo(this.id)

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.$refs.infoForm.getSourceList(res.data.typeCode)
      this.$refs.infoForm.getPrioritList(res.data.typeCode)

      this.pageLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>

  .leftForm {
  padding: 20px;
  border: 1px solid #ccc;
}
.rightForm {
  padding: 10px 10px 20px 10px;
  overflow-y: auto;
  overflow-x: hidden;
}
.dialog{
  margin: 0;
}

</style>

