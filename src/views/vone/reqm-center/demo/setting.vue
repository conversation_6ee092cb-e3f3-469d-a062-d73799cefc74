<template>
  <div>
    <el-form :model="infoForm" :disabled="infoDisabled">
      <el-row>
        <el-col style="border-bottom:1px solid #ccc;text-align:center">
          <h3>模板配置</h3>
        </el-col>
      </el-row>
      <el-row v-for="item in list" :key="item.name" style="margin-top:20px">
        <el-form-item :label="item.name" prop="typeCode">
          <el-col :span="20">
            <el-input placeholder="请输入" style="width: 80%" />
          </el-col>
          <el-col :span="4">
            <el-switch
              v-model="item.voneSwitch"
              class="switchStyle"
              active-color="#13ce66"
              active-text="开启"
              inactive-text="关闭"
              @change="switchOpe(item)"
            />
          </el-col>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { apiAlmProjectNoPage, apiProductNoPage, apiProgramNoPage } from '@/api/vone/project/index'

import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { ideaListQuery } from '@/api/vone/reqmcenter/idea'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'

import {
  requirementType
} from '@/api/vone/reqmcenter/require'

export default {
  props: {
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      infoForm: {},
      sourceList: [], // 需求来源
      projectIdList: [], // 归属项目
      productIdList: [], // 归属产品

      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 需求分类
      planIdList: [], // 迭代计划
      programIdList: [], // 项目集
      ideaList: [], // 用户需求
      list: [
        { name: '需求分析', voneSwitch: false, type: 'array' },
        { name: '概要分析', voneSwitch: false, type: 'array' }
      ]
    }
  },
  mounted() {
    this.getIssueType() // 需求分类
    this.getProjectList() // 归属项目
    this.productList() // 归属产品
    this.getStateList() // 状态
    this.getPlanList() // 迭代计划
    this.getPrioritList() // 优先级
    this.getSourceList() // 来源
    this.getProgramIdList() // 项目集
    this.getList() // 用户需求
  },
  methods: {
    switchOpe(item) {
      if (item.voneSwitch) {
        this.list.splice(item, 1)
        this.$emit('callBackInfo', item)
      }
    },
    async getList() {
      const { code, data } = await ideaListQuery()
      if (code == 0) {
        this.ideaList = data
      }
    },
    // 查询需求分类
    async getIssueType() {
      const res = await requirementType({
        code: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
    },
    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 归属项目集
    async getProgramIdList() {
      const res = await apiProgramNoPage()
      if (!res.isSuccess) {
        return
      }
      this.programIdList = res.data
    },
    // 归属产品
    async productList() {
      const res = await apiProductNoPage()

      if (!res.isSuccess) {
        return
      }
      this.productIdList = res.data
    },
    // 迭代计划
    async getPlanList() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },

    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    changeFlow() {
      this.$emit('changeFlow')
    }

  }

}
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input),
.el-date-editor.el-input__inner {
  width: 100%;
}
:deep(.el-form-item) {
  height: 55px;
}
.switchStyle  :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}
.switchStyle  :deep(.el-switch__label--left) {
  z-index: 9;
  left: 18px;
}
.switchStyle  :deep(.el-switch__label--right) {
  z-index: 9;
  left: -5px;
}
.switchStyle  :deep(.el-switch__label.is-active) {
  display: block;
}
.switchStyle :deep(.el-switch__core)
 {
  width: 54px !important;
}
.switchStyle :deep(.el-switch__label) {
 width: 54px !important;
}
</style>
