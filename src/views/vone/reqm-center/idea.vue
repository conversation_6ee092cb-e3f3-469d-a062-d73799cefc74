<template>
  <el-card :header="`共有${total}个关联的需求`" shadow="never">
    <el-table
      v-if="tableData.length>0"
      class="vone-table"
      :data="tableData"
    >
      <el-table-column
        prop="name"
        label="需求名称"
      >
        <template slot-scope="{ row }">
          <div class="name_icon">
            <a class="table_title">
              {{ row.code + " " + row.name }}</a>
          </div>
        </el-table-column>
      <el-table-column
        label="负责人"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.leadingBy && scope.row.echoMap && scope.row.echoMap.leadingBy">
            <vone-user-avatar
              :avatar-path="scope.row.echoMap.leadingBy.avatarPath"
              :name="scope.row.echoMap.leadingBy.name"
            />
          </span>

        </template>
      </el-table-column>
      <el-table-column
        prop="typeCode"
        label="分类"
      />
    </el-table>
    <vone-empty v-else />
  </el-card>

</template>
<script>

import { ideaParent } from '@/api/vone/reqmcenter/idea.js'

export default {
  props: {
    id: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      tableData: [],
      total: ''
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await ideaParent({ id: this.id })
      this.tableData = res.data
      this.total = res.data.length
    }

  }

}
</script>
<style lang="scss" scoped>
.tableC{
  max-height: 329px;
  overflow-y: auto;
  overflow-x: hidden;
}
:deep(.el-card__header) {
  border-left: 3px solid var(--main-theme-color,#3e7bfa);
  font-weight: bold;
  & > div span:first-child {
    font-weight: bold;
  }
  & > div span:nth-child(2) {
    font-size: 12px;
    font-weight: normal;
  }
  & > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>

