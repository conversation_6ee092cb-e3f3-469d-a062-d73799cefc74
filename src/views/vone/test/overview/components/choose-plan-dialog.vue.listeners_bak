<template>
  <div>
    <el-dialog title="测试计划维度统计" width="40%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" destroy-on-close v-on="$listeners">
      <el-form ref="planForm" :model="planForm" :rules="planFormRules">
        <el-form-item label="测试计划" prop="plan">
          <vone-tree-select v-model="planForm.plan" search-nested :tree-data="treeData" placeholder="请选择测试计划" multiple :value-consists-of="'ALL_WITH_INDETERMINATE'" :flat="true" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="onClose">取 消</el-button>&nbsp;
        <el-button :loading="saveLoading" type="primary" @click="sureCopy">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { list2Tree } from '@/utils/list2Tree'
import { TestPlanTree } from '@/api/vone/testplan'

import { testPlanTreeView } from '@/api/vone/testmanage/index'
import { gainTreeList } from '@/utils'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    isAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      planForm: {
        plan: []
      },
      planFormRules: {

      },
      saveLoading: false,
      treeData: []
    }
  },
  watch: {
    visible(v) {
      if (!v) return
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs['planForm'].resetFields()
    },
    async sureCopy() {
      this.$emit('update:visible', false)
      this.$emit('success', this.planForm.plan)
    },
    // 查询用例节点树
    async getTreeData() {
      if (this.isAll) {
        const res = await testPlanTreeView()
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        const plan = res.data.testPlanList.length ? res.data.testPlanList.map(r => ({
          id: r.id,
          name: r.name,
          parentId: r.treeId
        })) : []

        const allData = res.data.treeList.concat(plan)

        const tree = list2Tree(allData, { parentKey: 'parentId' })
        this.treeData = gainTreeList(tree)
      } else {
        const res = await TestPlanTree()

        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        const tree = list2Tree(res.data, { parentKey: 'parentId' })
        this.treeData = gainTreeList(tree)
      }
    }

  }
}
</script>
