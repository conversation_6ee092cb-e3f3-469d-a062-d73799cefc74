<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic ref="searchForm" table-search-key="testm-tag-table" :model="formData" :table-ref="$refs['testm-tag-table']" @getTableData="getList">
          <el-row>
            <el-col :span="12">
              <el-form-item label="标签名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入标签名称" style="width:100%" />
              </el-form-item>
            </el-col>
          </el-row>

        </vone-search-dynamic>

        <el-select
          v-model="tagSelectName"
          style="width:280px;margin-left:10px"
          filterable
          remote
          clearable
          placeholder="请输入标签名称查询"
          :remote-method="remoteMethod"
          loading-text="正在查询..."
          no-match-text="暂未查询到匹配数据,请重新输入"
          :loading="tagLoading"
          popper-class="remote-user"
          @change="getList"
        >
          <template slot="prefix">
            <i class="el-input__icon el-icon-search" />
          </template>
          <el-option v-for="item in selectData" :key="item.id" :label="item.name" :value="item.name" />
        </el-select>
      </template>
      <template slot="actions">
        <el-button
          style="float:right;text-align: right"
          icon="iconfont el-icon-tips-plus-circle"
          type="primary"
          :disabled="!$permission('testm_test_tag_add')"
          @click.stop="add"
        >新增</el-button>
      </template>
    </vone-search-wrapper>

    <el-row>
      <div class="tableHead">
        <span>标签名称</span>
        <span style="position:absolute;left:33.5%">创建人</span>
        <span style="position:absolute;left:65.5%">用例数</span>
        <span>操作</span>
      </div>
      <vone-tree
        ref="orgTree"
        v-loading="treeLoading"
        height="calc(100vh - 215px)"
        class="treeTag"
        :data="data"
        node-key="id"
        default-expand-all
        check-strictly
        draggable
        :allow-drop="collapse"
        :expand-on-click-node="false"
        @node-drag-start="handleDragStart"
        @node-drop="handleDrop"
      >
        <span slot-scope="{ node, data }" class="custom-tree-nodes">

          <span style="width:33%" class="text-over">{{ data.name }}</span>
          <span style="position:absolute;left:33.5%">

            <span>
              <vone-user-avatar :avatar-path="data.userData ? data.userData.avatarPath :''" :name="data.userData ? data.userData.name :''" />
            </span>

          </span>
          <span style="position:absolute;left:65.5%">{{ data.echoMap.caseNum }}</span>

          <span class="operation-icon">
            <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
              <el-button type="text" size="mini" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('testm_test_tag_add')||node.level==3" @click.stop="() => add(node, data)" />

            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
              <el-button type="text" size="mini" icon="iconfont el-icon-application-edit" :disabled="!$permission('testm_test_tag_edit')" @click.stop="() => nodeClick(node, data)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
              <el-button type="text" size="mini" icon="iconfont el-icon-application-delete" :disabled="!$permission('testm_test_tag_del')" @click.stop="() => remove(node, data)" />
            </el-tooltip>
          </span>
        </span>
      </vone-tree>
    </el-row>
    <el-dialog v-if="visible" :title="title" v-model="visible" width="30%" :before-close="onClose" :close-on-click-modal="false">
      <el-form ref="treeFormRef" :model="treeForm" :rules="rulesFormRules" label-width="80px">
        <el-form-item label="层级" class="nameCls">
          <el-input v-model.trim="level" style="margin-right: 10px" disabled />
        </el-form-item>
        <el-form-item label="名称" prop="name" class="nameCls">
          <el-input v-model.trim="treeForm.name" style="margin-right: 10px" placeholder="请输入名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </page-wrapper>
</template>

<script>
import { debounce } from 'lodash'

import { getUserById } from '@/api/vone/base/user'
import { queryCaseTree, editTestTabs, delTestTabs, detailsTestTabs, testCaseTreeSwap, queryCase } from '@/api/vone/testTab'

import { list2Tree } from '@/utils/list2Tree'
import { catchErr } from '@/utils'

export default {
  data() {
    return {
      level: '',
      title: '',
      visible: false,
      treeLoading: false,
      tagLoading: false,
      treeForm: {},
      tagSelectName: '', // 标签选择名称
      rulesFormRules: {
        name: [
          { required: true, message: '请输入标签', trigger: 'blur' },
          {
            pattern: '[^ ]+',
            message: '标签不能为空格'
          },
          {
            pattern: '^([a-zA-Z0-9\u4e00-\u9fa5\-.]){1,10}$',
            message: '请输入不超过10个字母、数字或横线(-)组成的标识'
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾'
          }
        ]
      },
      timer: null,
      userMap: {},
      tagsList: [], // 表格数据
      data: [],
      copyData: [],
      selectData: [],
      formData: {}
    }
  },
  mounted() {
    this.remoteMethod()
    this.getList()
  },
  methods: {
    // 查人员
    async getUserList(userId, tableIdxs = []) {
      if (this.userMap[userId]) {
        tableIdxs.forEach(idx => {
          this.$set(this.tagsList[idx], 'userData', this.userMap[userId])
        })
        return
      }
      const [res, err] = await catchErr(getUserById(userId))
      if (err) return
      if (res.isSuccess) {
        this.userMap[userId] = res.data
        tableIdxs.forEach(idx => {
          this.$set(this.tagsList[idx], 'userData', this.userMap[userId])
        })
      }
    },
    // debounce函数去抖
    remoteMethod: debounce(function(query) {
      this.tagLoading = true
      const name = query != '' ? query : ''
      queryCase({ name: name }).then(res => {
        this.selectData = res.data
        this.tagLoading = false
      })
    }, 1000),
    // 查询标签列表
    async getList() {
      let params = []
      if (this.tagSelectName) {
        if (this.tagSelectName.indexOf('/') != -1) { // 包含
          params = this.tagSelectName.split('/')
        } else {
          params = [this.tagSelectName]
        }
      }
      try {
        this.treeLoading = true
        const res = await queryCaseTree(params)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          this.treeLoading = false
          return
        }
        this.tagsList = res.data.filter(item => item.id !== '0')
        // 保存表格相同用户index
        const tableUserMap = {}
        this.tagsList.forEach((ele, i) => {
          if (ele.createdBy) {
            const userIds = tableUserMap[ele.createdBy] ?? []
            tableUserMap[ele.createdBy] = [...userIds, i]
          }
        })
        const usersList = Object.entries(tableUserMap)
        if (usersList.length > 0) {
          for (const [key, idxs] of usersList) {
            // 请求用户信息并更新表格
            await this.getUserList(key, idxs)
            // 保证更新表格方法最后执行
            clearTimeout(this.timer)
            this.timer = setTimeout(() => {
              this.getSortFun(this.tagsList)
            }, 500)
          }
        } else {
          // 保证更新表格方法最后执行
          clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            this.getSortFun(this.tagsList)
          }, 500)
        }
      } catch (error) {
        this.treeLoading = false
      }
    },
    // 排序
    getSortFun(val) {
      val.sort((a, b) => a.sort - b.sort)
      this.data = list2Tree(val, { parentKey: 'parentId' })
      this.treeLoading = false
    },
    onClose() {
      this.visible = false
    },
    handleDragStart() {
      this.copyData = JSON.parse(
        JSON.stringify(this.data)
      )
    },
    collapse(draggingNode, dropNode, type) {
      if (draggingNode.data.name == dropNode.data.name) {
        return type === 'inner'
      } else {
        return true
      }
    },
    getDepth(obj, k) {
      let depth = k
      if (obj.childNodes && obj.childNodes.length > 0) {
        obj.childNodes.forEach((v) => {
          if (v.childNodes && v.childNodes.length > 0) {
            depth = this.getDepth(v, k + 1)
          }

          // if(!v.childNodes)return
        })
      }
      return depth
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        id: draggingNode.data.id,
        parentId: draggingNode.data.parentId,
        newParentId: dropType == 'inner' ? dropNode.data.id : dropNode.parent.data.id || 0,
        sort: this.getChange(dropNode.parent.childNodes, draggingNode.data.id)

      }
      try {
        const res = await testCaseTreeSwap(obj)

        if (!res.isSuccess) {
          this.$message.success(res.msg)
          return
        }
        this.$message.success('移动成功')
      } catch (error) {
        this.data = this.copyData
      }
    },
    getChange(arr, id) {
      var proOrgId = 0
      arr.forEach((item, i) => {
        if (item.data.id == id) {
          proOrgId = i
        }
      })
      return proOrgId
    },
    add(node, data) {
      this.title = '新增标签'
      this.treeForm = {
        name: '',
        parentId: data ? data.id : 0
      }
      this.visible = true

      this.level = data ? this.getName(node) : '/'
    },
    // 获取name
    getName(val) {
      const tags = []
      let node = val
      while (node) {
        node.data.name && tags.unshift(node.data.name)
        node = node.parent
      }

      return tags.join('/')
    },
    async nodeClick(node, data) {
      this.title = '编辑标签'
      const res = await detailsTestTabs(data.id)
      if (res.isSuccess) {
        this.treeForm = res.data
        this.level = this.getName(node)
        // this.getList()
      } else {
        this.$message.warning(res.msg)
      }

      this.visible = true
    },
    remove(node, data) {
      this.$confirm(`确定删除【${data.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
        .then(() => {
          delTestTabs([data.id]).then((res) => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.getList()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => { })
    },
    async save() {
      try {
        await this.$refs.treeFormRef.validate()
        const res = await editTestTabs(this.treeForm)
        if (res.isSuccess) {
          this.visible = false
          this.$message.success('保存成功')
          this.getList()
        } else {
          this.$message.warning(res.msg)
        }
      } catch (error) {
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tree-nodes {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 16px;

  .operation-icon {
    opacity: 1;
    .is-disabled {
      color: var(--input-border-color);
    }
  }
}
.tableHead {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 76px;
  padding-left: 28px;
  height: 38px;
  line-height: 38px;
  color: var(--main-font-color);
  background-color: var(
    --content-bg-color
  );
 border-bottom: 1px solid var(--disabled-bg-color);
}
</style>
<style lang='scss'>
.treeTag {
  height: calc(100vh - 215px);
  .el-tree-node:focus > .el-tree-node__content {
    background-color: inherit;
    &:hover {
      background-color: var(--hover-bg-color);
    }
  }
  .el-tree-node__content {
    position: relative;
    height: 42px;
    color: var(--main-font-color);
    border-bottom: 1px solid var(--disabled-bg-color);
    & > .el-tree-node__expand-icon {
      font-size: 16px;
      color: var(--main-second-color);
    }
  }
  .el-tree-node__children {
    background-color: var(--node-cildren-bg-color);
  }

  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
}
</style>
