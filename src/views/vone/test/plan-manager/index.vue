<!-- 测试计划 -->
<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>测试计划</span>
      </div>
      <div class="menubox">
        <a :class="type == 'archiveTree'? 'is-active' : ''" @click="e=>changeTable('archiveTree')">已归档</a>
        <a :class="type == 'catchTree'? 'is-active' : ''" @click="e=>changeTable('catchTree')">回收站</a>
      </div>
      <div class="titlebox">计划树</div>
      <div class="treebox">
        <caseTree ref="groupTree" :type="type" :status="status" :active-nodes="activeNodes" @treeDrop="treeDrop" @submit="submit" @changeType="changeType" />
        <!-- <archiveTree v-else :key="type" :type="type" :status="status" @treeDrop="treeDrop" @submit="submit" /> -->
      </div>
    </div>
    <div v-if="type=='archiveTree' &&!status" class="rightSection">
      <archiveRecords />
    </div>
    <div v-else class="rightSection">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="testm-plan-table"
            v-model:model="formData"
            :table-ref="$refs['testm-plan-table']"
            v-model:default-fileds="defaultFileds"
            show-basic
            v-model:extra="extraData"
            @getTableData="getNodeCaseDetail"
          />
        </template>
        <template v-if="type!=='archiveTree'" slot="actions">
          <el-button v-if="type!=='catchTree'&&menuNode.parentId!=='0'&& menuNode.id!==0" type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('testm_plan_add')" @click="openAddCard">新增</el-button>
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in (type !== 'catchTree' ? actions :actionAll)" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                <span>{{ item.name }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="fliter">
          <vone-search-filter
            v-model:extra="extraData"
            v-model:model="formData"
            v-model:default-fileds="defaultFileds"
            @getTableData="getNodeCaseDetail"
          />
        </template>
      </vone-search-wrapper>

      <div style="height: calc(100vh - 200px)">
        <vxe-table
          ref="testm-plan-table"
          class="vone-vxe-table draggTable"
          border
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth:'120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="50" fixed="left" align="center" />
          <vxe-column title="测试计划" field="name" min-width="300px" show-overflow-tooltip>
            <template v-slot="{ row }">
              <a @click="openPlanDetail(row)">
                <i class="iconfont el-icon-testcase" style="color: var(--main-theme-color,#3e7bfa);margin-right:4px;" />
                <span>{{ row.name }}</span>
              </a>
            </template>
          </vxe-column>
          <vxe-column title="状态" field="stateId" width="150">
            <template v-slot="{ row }">
              <el-dropdown trigger="click" :disabled="row.stateId==2||!$permission('testm_plan_status_edit')">
                <span
                  class="el-dropdown-link stateTag"
                  :style="{
                    '--tagColor':row.stateId == 1 ? '#f7cd55' :row.stateId == 2 ? '#6fc38a':'#3e7bfa',
                  }"
                >{{ row.stateId == 0 ? '未开始': row.stateId == 1 ? '进行中': '已完成' }}<i class="iconfont el-icon-direction-down" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :disabled="row.stateId == 0" @click.native="changeStatus(row, 0)">未开始</el-dropdown-item>
                  <el-dropdown-item :disabled="row.stateId == 1" @click.native="changeStatus(row, 1)">进行中</el-dropdown-item>
                  <el-dropdown-item :disabled="row.stateId == 2" @click.native="changeStatus(row, 2)">已完成</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </vxe-column>
          <vxe-column title="类型" field="type" width="150">
            <template v-slot="{ row }">
              {{ row.type=='system'?'系统测试':row.type=='smoke'?'冒烟测试':'回归测试' }}
            </template>
          </vxe-column>
          <vxe-column title="维护人" field="leadingBy" width="150" show-overflow-tooltip>
            <template v-slot="{ row }">
              <span v-if="row.leadingBy && row.userData">
                <vone-user-avatar :avatar-path="row.userData.avatarPath" :name="row.userData.name" />
              </span>
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" align="left" width="120">
            <template #default="{ row }">

              <span v-for="(item,i) in type=='catchTree'?tableOptionsRelData :tableOptionData" :key="item.label">
                <el-tooltip class="item" :content="item.label" placement="top">
                  <el-button type="text" :disabled="item.disabled" :icon="item.icon" @click="item.handler(row)" />
                </el-tooltip>

                <el-divider v-if="i!==tableOptionsRelData.length" direction="vertical" />
              </span>

              <el-dropdown v-if="type=='caseTree'" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more icon_click" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in moreData" :key="item.name" :command="() =>item.handler(row)" :disabled="item.disabled(row)">
                    <i :class=" item.icon " />
                    <span>{{ item.label }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

            </template>
          </vxe-column>
        </vxe-table>
        <vone-pagination ref="pagination" :total="tableData.total" @update="getNodeCaseDetail" />
      </div>
    </div>
    <!-- 新增测试计划 -->
    <planDrawer v-if="addVisible" :id="planId" v-model="addVisible" :plan-type="dialogType" :type="type" :plan-data="menuNode" @success="success" />
  </div>
  <!-- <el-row :gutter="12" class="planTe">
    <el-col class="left_aside">

      <el-tabs v-model="type" class="testTabs">
        <el-tab-pane v-for="item in types" :key="item.key" style="flex:1" :label="item.tab" :name="item.key" :disabled="item.disabled" />
      </el-tabs>

      <div>
        <caseTree v-if="type==='caseTree'" ref="groupTree" :type="type" :status="status" :active-nodes="activeNodes" @treeDrop="treeDrop" @submit="submit" />
        <archiveTree v-else :key="type" :type="type" :status="status" @treeDrop="treeDrop" @submit="submit" />
      </div>

    </el-col>

    <section class="right_aside"> -->

  <!-- 查看归档记录 -->
  <!-- <archiveRecords v-if="type=='archiveTree' &&!status" /> -->
  <!-- 列表 -->
  <!-- <main v-else>

      </main>
    </section> -->

  <!-- 新增测试计划 -->
  <!-- <planDrawer v-if="addVisible" :id="planId" v-model="addVisible" :plan-type="dialogType" :type="type" :plan-data="menuNode" @success="success" />

  </el-row> -->
</template>

<script>
import Sortable, { MultiDrag } from 'sortablejs'
import Storage from 'store'
import { deleteCase } from '@/api/vone/testmanage/case'

import { getTestPlanCase, removePlan, updateProductPlan, updateTestPlanGroup, planArchive, recallPlanArchive, getNoGroupCase, getRecycle, recallRecy, deltestPlan, findPlanArchiveByTreeId } from '@/api/vone/testplan'

import { catchErr } from '@/utils'
import archiveRecords from './archive/archive-table.vue'
import planDrawer from './components/plan-drawer.vue'

import archiveTree from './archive-tree.vue'
import caseTree from './tree.vue'

// 多选拖拽
Sortable.mount(new MultiDrag())

// 清除拖拽进入节点高亮显示样式
function clearDragClass() {
  const moveList = document.querySelectorAll('.dragg2Tree')
  if (moveList.length > 0) {
    moveList.forEach(item => item.classList.remove('dragg2Tree'))
  }
}

export default {
  components: {
    archiveRecords,
    caseTree,
    archiveTree,
    planDrawer
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入计划名称'
        }
      ],
      tableOptionsRelData: [
        {
          disabled: !this.$permission('testm_plan_call'),
          type: 'icon', // 为icon则是图标
          label: '还原', // 功能名称
          icon: 'iconfont el-icon-application-undo', // icon class
          handler: this.reduction // 操作事件
        },
        {
          type: 'icon', // 为icon则是图标
          label: '删除', // 功能名称
          icon: 'iconfont el-icon-application-delete', // icon class
          handler: this.delRow,
          disabled: !this.$permission('testm_plan_del')
        }
      ],
      tableOptionData: [
        {
          type: 'icon', // 为icon则是图标
          label: '编辑', // 功能名称
          icon: 'iconfont el-icon-application-edit', // icon class
          handler: this.editRow,
          disabled: !this.$permission('testm_plan_edit')
        },
        {
          type: 'icon', // 为icon则是图标
          label: '删除', // 功能名称
          icon: 'iconfont el-icon-application-delete', // icon class
          handler: this.deleteRow,
          disabled: !this.$permission('testm_plan_del')
        }
      ],
      moreData: [
        {
          type: 'icon', // 为icon则是图标
          label: '执行', // 功能名称
          icon: 'el-icon-video-play', // icon class
          disabled: this.isDisabled,
          handler: this.showTestDetails // 操作事件
        },
        {
          type: 'icon', // 为icon则是图标
          label: '概览', // 功能名称
          icon: 'iconfont el-icon-application-overview-dimension', // icon class
          handler: this.showOverview,
          disabled: this.isOverDisabled
        }
      ],
      type: 'caseTree',
      types: [
        {
          key: 'caseTree',
          tab: '计划树'
        },
        {
          key: 'archiveTree',
          tab: '已归档'
        }, {
          key: 'catchTree',
          tab: '回收站'
        }
      ],
      activeNodes: [],
      status: false,
      planId: '',
      formData: {},
      tableLoading: false, // 表格加载
      tableData: {
        records: []
      },
      actions: [
        {
          name: '批量删除',

          fn: () => this.batchDel('init'),
          disabled: !this.$permission('testm_plan_file')
        },
        {
          name: '计划归档',
          icon: 'iconfont el-icon-application-archive',
          fn: this.caseFile,
          disabled: !this.$permission('testm_plan_file')
        }
      ],
      actionAll: [
        {
          name: '批量删除',

          fn: this.batchDel,
          disabled: !this.$permission('testm_plan_file')
        },
        {
          name: '批量还原',

          fn: this.batchReduction,
          disabled: !this.$permission('testm_plan_file')
        }
      ],
      selectedTable: [], // 勾选数据
      dialogType: 'add', // 新增或编辑用例
      addVisible: false, // 新增用例卡片显示
      menuNode: {}, // 选中节点数据
      dragItems: {} // 拖拽选中的数据

    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name === 'testm_plan_detail') {
        // 获取保存的节点
        vm.activeNodes = Storage.get('nodeMap')
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    if (to.name === 'testm_plan_detail') {
      // 查询需保存的节点
     this.$refs.groupTree?.storeNodeMap()
    }
    next()
  },
  mounted() {
    this.getNodeCaseDetail()
    this.rowDrop()
    this.initTreeSettings()
  },
  methods: {
    changeTable(type) {
      this.type = type
      if (type == 'catchTree') {
        this.submit()
      }
    },
    isOverDisabled() {
      return !this.$permission('testm_plan_overview')
    },
    isDisabled(row) {
      return row.stateId != 1 || !this.$permission('testm_plan_excute')
    },
    selectAllEvent({ checked }) {
      const list = this.$refs['testm-plan-table'].getCheckboxRecords()
      this.selectChange(list)
    },
    selectChangeEvent({ checked }) {
      const list = this.$refs['testm-plan-table'].getCheckboxRecords()
      this.selectChange(list)
    },
    // 设置已归档显示树级设置方法
    initTreeSettings() {
      this.$nextTick(() => {
        const archiveTab = document.querySelector('#tab-archiveTree')
        const box = document.querySelector('.box')
        let timer
        const showBox = () => {
          if (!box) return
          clearTimeout(timer)
          box.style.opacity = 1
          box.style.visibility = 'visible'
        }
        const hideBox = () => {
          if (this.type !== 'archiveTree') return
          if (!box) return
          timer = setTimeout(() => {
            box.style.opacity = 0
            box.style.visibility = 'hidden'
          }, 200)
        }
        archiveTab?.addEventListener('mouseenter', showBox)
        archiveTab?.addEventListener('mouseleave', hideBox)
        box?.addEventListener('mouseenter', showBox)
        box?.addEventListener('mouseleave', hideBox)
      })
    },
    submit(data) {
      this.menuNode = data || {}
      this.formData.treeId = data?.id
      this.$nextTick(() => {
        this.getNodeCaseDetail()
      })
    },
    // 批量删除
    async batchDel(type) {
      this.selectedTable = this.getVxeTableSelectData('testm-plan-table')
      if (this.selectedTable.length > 0) {
        if (type) {
          const confirmed = await catchErr(this.$confirm('确定删除吗，删除后该节点下计划将移入到回收站？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            customClass: 'delConfirm',
            showClose: false,
            type: 'warning'
          }))
          if (confirmed[1]) return
          const [res, err] = await catchErr(removePlan(this.selectedTable.map(item => item.id)))
          if (err) return
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getNodeCaseDetail()
          return
        }
        await this.$confirm(`是否进行删除`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        }).then(async() => {
          const deleteArr = []
          this.selectedTable.map(item => deleteArr.push(item.id))
          const [res, err] = await catchErr(deltestPlan(deleteArr))
          if (err) return
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getNodeCaseDetail()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 批量还原
    async batchReduction() {
      this.selectedTable = this.getVxeTableSelectData('testm-plan-table')
      if (this.selectedTable.length > 0) {
        await this.$confirm(`是否进行还原`, '提示', {
          type: 'warning'
        }).then(async() => {
          const deleteArr = []
          this.selectedTable.map(item => deleteArr.push(item.id))
          const [res, err] = await catchErr(recallRecy(deleteArr))
          if (err) return
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getNodeCaseDetail()
        })
      } else {
        this.$message.warning('请勾选要还原的信息')
      }
    },

    // 计划还原
    async reduction(row) {
      const res = this.type == 'catchTree' ? await recallRecy([row.id]) : await recallPlanArchive([row.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.getNodeCaseDetail()
    },
    // 计划归档
    async caseFile() {
      this.selectedTable = this.getVxeTableSelectData('testm-plan-table')
      if (this.selectedTable.length > 0) {
        this.$confirm(`是否进行归档`, '提示', {
          type: 'warning'
        }).then(async() => {
          const deleteArr = []
          this.selectedTable.map(item => deleteArr.push(item.id))
          const [res, err] = await catchErr(planArchive(deleteArr))
          if (err) return
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getNodeCaseDetail()
        })
      } else {
        this.$message.warning('请勾选要归档的信息')
      }
    },
    // 树拖拽
    treeDrop(val) {
      const that = this
      const treeContent = val

      if (treeContent?.length > 0) {
        for (let i = 0; i < treeContent.length; i++) {
          new Sortable(treeContent[i], {
            group: { name: 'caseList', pull: true, push: false },
            animation: 150,
            swapThreshold: 0.65,
            onAdd(evt) {
              const { oldIndicies, oldIndex, to, items } = evt
              // 清除设置的拖拽进入节点高亮样式
              clearDragClass()
              // 拖入的节点
              const insertNode = to.__vue__?.node?.data ?? {}
              let params = []
              const treeId = insertNode.id
              // 多选节点拖入
              if (oldIndicies.length > 0) {
                // 获取右侧表格数据
                that.dragItems = oldIndicies.sort((a, b) => a.index - b.index).reduce((acc, item) => {
                  const cur = that.tableData.records[item.index]
                  params.push(cur.id)
                  acc[item.index] = cur
                  return acc
                }, {})
                // 删除拖入的表格trDOM
                items.forEach(item => {
                  to.contains(item) && to.removeChild(item)
                })
              } else {
                // 拖入树菜单，添加表格行数据
                // 单条表格数据
                const single = that.tableData.records[oldIndex]

                params = [single.id]
              }
              if (to.__vue__?.node?.level <= 1) {
                that.$message.warning('根节点不能添加')
                that.tableData = {
                  records: []
                }
                that.getNodeCaseDetail()
                return
              }
              // 用例导入左侧分组
              updateTestPlanGroup(treeId, params).then(res => {
                if (res.isSuccess) {
                  that.$message.success('用例拖入成功')
                  // 刷新表格
                  that.getNodeCaseDetail()
                } else {
                  that.$message.error(res.msg)
                }
              })
            }
          })
        }
      }
    },
    // 表格行拖拽
    rowDrop() {
      const tbody = document.querySelector('.draggTable .vxe-table--body-wrapper tbody')
      var that = this
      Sortable.create(tbody, {
        group: 'caseList',
        multiDrag: true,
        selectedClass: 'selectedDrag', // 多选选中类名
        handle: '.handleDrag',
        animation: 150,
        avoidImplicitDeselect: true, // 外部点击不能取消选中
        removeCloneOnHide: true,
        dragClass: 'draggingRow', // 拖动中的dom类名
        onChoose: function(/** Event*/evt) {
          that.$nextTick(() => {
            that.sorTree = []
            var node
            if (that.type == 'caseTree' && that.allTreeStatus) {
              node = that.$refs['allTree']?.getTreeNode()
            } else {
              node = that.$refs['tree']?.getTreeNode()
            }
            that.treeDrop(node)
          })
          // evt.oldIndex
        },
        // 拖拽移动回调
        onMove: function(evt) {
          // 清除拖拽进入节点高亮样式
          clearDragClass()
          evt.dragged.style.display = 'none'
          // 添加拖入样式
          evt.to.classList.add('dragg2Tree')
        },
        // 拖拽结束回调，处理添加到左侧分组业务逻辑
        onEnd: (evt) => {
          const { from, to, item, items, newIndex, oldIndicies } = evt
          // 清除添加的样式
          evt.item.style = ''
          // 右侧表格拖入左侧树
          if (from != to) {
            // 单项拖拽
            if (items.length === 0) {
              // 删除拖入的表格trDOM
              to.contains(item) && to.removeChild(item)
            }
          } else {
            // 拖拽的数据索引
            const oldOrders = oldIndicies.map(ele => ele.index)
            // 未拖拽和拖拽数据
            const draggedList = []
            const unDraggedList = []
            this.tableData.records.filter((ele, i) => {
              oldOrders.includes(i) ? draggedList.push(ele) : unDraggedList.push(ele)
            })
            // 排序后数据
            unDraggedList.splice(newIndex, 0, ...draggedList)
            this.tableData.records = unDraggedList

            // 设置拖拽后默认选中
            this.$nextTick(() => {
              draggedList.forEach(ele => {
                this.$refs['testm-plan-table']
                  .$refs['vxe-table'].toggleRowSelection(ele, true)
              })
            })
          }
          that.$nextTick(() => {
            var node
            if (that.type == 'caseTree' && that.allTreeStatus) {
              node = that.$refs['allTree']?.getTreeNode()
            } else {
              node = that.$refs['tree']?.getTreeNode()
            }
            that.destroy(node)
          })
        }
      })
    },
    destroy(val) {
      this.sorTree.map(e => {
        e.destroy()
      })
    },
    // 修改计划状态
    async changeStatus(row, status) {
      this.$set(row, 'stateId', status)
      const params = {
        ...row,
        stateId: status
      }
      const [res, err] = await catchErr(updateProductPlan(params))
      if (err) return
      if (res.isSuccess) {
        this.$message.success('修改成功')
      }
    },
    // 执行用例
    showTestDetails(row) {
      this.$router.push({
        path: `/test/plan/excute/${row.id}`,
        query: {
          planName: row.name,
          bug: row.bug
        }
      })
    },
    // 概览
    showOverview(row) {
      this.$router.push({
        path: `/test/plan/overview/${row.id}`,
        query: {
          planName: row.name,
          bug: row.bug
        }
      })
    },
    // 批量删除
    async deleteTableSelect() {
      this.selectedTable = this.getVxeTableSelectData('testm-plan-table')
      if (this.selectedTable.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        }).then(async() => {
          const deleteArr = []
          this.selectedTable.map(item => deleteArr.push(item.id))
          const [res, err] = await catchErr(deleteCase(deleteArr))
          if (err) return
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 表格选择
    selectChange(value) {
      this.selecteTableData = value
      const list = []
      // 获取所有表格行
      const drageRows = document.querySelectorAll('.draggTable .vxe-table--body-wrapper tbody tr')
      // 获取表格选中的索引
      this.tableData.records.map((ele, i) => {
        if (value.includes(ele)) {
          list.push(i)
        } else {
          // 未选中行，移除拖拽标志
          const el = drageRows[i]
          if (el) {
            const checkBox = el.firstChild
            const handleDrag = checkBox.querySelector('.handleDrag')
            if (handleDrag) {
              checkBox.removeChild(handleDrag)
            }
          }
        }
      })

      // 清除拖拽选中
      drageRows.forEach(el => {
        Sortable.utils.deselect(el)
      })
      // 获取表格行的元素
      const multiDrags = list.map(v => drageRows[v])
      // 手动设置选中表格行拖拽
      multiDrags.map(item => {
        const checkBox = item.firstChild
        const handleDrag = checkBox.querySelector('.handleDrag')
        // 不存在拖拽,添加拖拽样式
        if (!handleDrag) {
          const handleGrab = document.createElement('i')
          handleGrab.classList.add('iconfont', 'el-icon-icon-line-tuozhuai', 'handleDrag')
          item.firstChild.insertBefore(handleGrab, item.firstChild.childNodes[0])
        }

        Sortable.utils.select(item)
      })
    },
    // 查人员
    async getUserList(userId, row) {
      const res = await this.$store.dispatch('user/getUserData', userId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, 'userData', res.data)
    },
    // 查询当前节点详细计划
    async getNodeCaseDetail() {
      // 默认情况下搜索组件查询的数据不对
      if (this.type === 'caseTree' && !this.menuNode.id) return
      const tableAttr = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        extra: {
          ...this.extraData
        },
        model: {
          ...this.formData,
          treeId: this.menuNode?.id
        }
      }
      const fetchFn = this.type == 'catchTree' ? getRecycle : this.type == 'archiveTree' ? findPlanArchiveByTreeId : this.menuNode.name == '未分组' ? getNoGroupCase : getTestPlanCase
      this.tableLoading = true
      const [res, err] = await catchErr(fetchFn(params))
      this.tableLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      // 查询人员信息
      Array.isArray(res.data.records) && res.data.records.forEach(item => {
        item.leadingBy && this.getUserList(item.leadingBy, item)
      })
      this.tableData = res.data
      this.tableData.total = res.data.total
    },
    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 编辑测试用例
    async editRow(row) {
      this.planId = row.id
      this.addVisible = true
      this.dialogType = 'edit'
    },
    async detailRow(row) {
      this.planId = row.id
      this.addVisible = true
      this.dialogType = 'detail'
    },
    // 移动到回收站
    async deleteRow(row) {
      await this.$confirm(`确定将【${row.name}】移动到回收站吗？`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
      const [res, err] = await catchErr(removePlan([row.id]))
      if (err) return
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getNodeCaseDetail()
    },
    // 删除测试计划
    async delRow(row) {
      await this.$confirm(`确定删除${row.name}吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false
      })
      const [res, err] = await catchErr(deltestPlan([row.id]))
      if (err) return
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getNodeCaseDetail()
    },
    // 新增测试计划
    openAddCard() {
      this.addVisible = true
      this.dialogType = 'add'
    },
    success() {
      this.getNodeCaseDetail()
    },
    openPlanDetail(row) {
      if (!this.$permission('testm_plan_file_detail')) return
      if (this.type !== 'caseTree') {
        this.detailRow(row)
      } else {
        this.$router.push({
          path: `/test/plan/detail/${row.id}/${row.libraryId}`,
          query: {
            bug: row.bug,
            stateId: row.stateId
          }
        })
      }
    },
    changeType() {
      this.type = 'caseTree'
    }
  }
}
</script>
<style lang='scss' scoped>
.leftSection {
	width: 330px;
  overflow: hidden;
  // overflow-y: hidden;
  // overflow-x: auto;
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		span {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
  }
  .menubox {
    border-bottom: 1px solid var(--solid-border-color);
    padding:0 16px;
    a {
      margin:0px -16px;
      line-height: 36px;
      display: block;
      padding: 0px 28px;
      color: var(--font-main-color);
      &:hover{
      background: var(--content-bg-hover-color);
      }
    }
    .is-active {

      background-color: var(--main-hover-page-color);
      color: var(--main-theme-color);

    }
  }
  .titlebox {
    padding: 0px 16px;
    line-height: 36px;
    font-weight: 500;
    border-bottom: 1px solid var(--solid-border-color);
  }
}

.planTe{
  display: flex;
  overflow: hidden;

}
.testTabs {
  width: 100%;
  justify-content: center;
  align-items: center;
  display: -webkit-flex;
  :deep() {
    .is-active {
      color: #3e7bfa;
      background-color: var(--main-bg-color);
      border-radius: 2px;
      border: none;
    }
    .el-tabs__nav {
      margin: 16px 9px;
      width: 94%;
      display: flex;
      border-radius: 2px;
      border: 4px solid var(--tab-bg-color);
      background-color: var(--tab-bg-color);
    }
    .el-tabs__active-bar {
      display: none;
    }
  }
}
:deep(.el-tabs__header) {
  width: 100%;
  text-align: center;
  border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);
}

:deep(.el-tabs__item) {
  border: 0;
  flex: 1;
  padding: 0;
  height: 30px;
  line-height: 30px;
  color: var(--tab-font-color);
}
:deep(.table-view) {
  margin: 0;
  padding: 0;
}
// 显示全部树级样式
.box {
  position: absolute;
  top: 53px;
  right: 85px;
  background: var(--main-bg-color);
  z-index: 2;
  text-align: center;
  border: 1px solid var(--disabled-bg-color);
  width: 132px;
  height: 54px;
  line-height: 54px;
  box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
  border-radius: 2px;
}
:deep() {
  .el-collapse-item__content {
    padding: 0;
  }
  .el-collapse-item__header {
    height: 32px;
    line-height: 32px;
    padding: 0 8px 0 16px;
    font-weight: normal;
  }
}
.left_aside {
  position: relative;
  height: calc(100vh - 70px);
  width: 330px;
  background-color: var(--main-bg-color);

}

// 右侧样式
.right_aside {
  padding: 16px;
  flex: 1;
  margin-left: 10px;
  background-color: var(--main-bg-color)
}

.stateTag {
  min-width: 54px;
  font-weight: 500;
  font-size: 12px;
  padding: 0 12px;
  color: var(--tagColor, #3e7bfa);
  border: 1px solid var(--tagColor, #3e7bfa);
  box-sizing: border-box;
  border-radius: 2px;
  display: inline-block;
}

:deep(.level-select) {
  width: 90px;
  .el-input__prefix {
    display: flex;
    align-items: center;
  }
}
.caseText {
  height: 32px;
  line-height: 32px;
  padding-left: 16px;
  &:hover {
    cursor: pointer;
  }
}
.treebox{
  width: 330px;
  overflow: auto;
}
</style>
