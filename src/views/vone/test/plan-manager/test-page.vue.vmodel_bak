<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <div class="title">测试计划</div>
        <div class="search">
          <el-popover v-model="searchPopover" placement="bottom-start" width="400" popper-class="table-search-form" trigger="click">
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">筛选机构</span>
              </div>
              <div class="search-form">
                <el-form ref="searchForm" inline :model="formData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="用例执行状态" prop="status">
                        <el-select v-model="formData.status" multiple placeholder="请选择" class="select">
                          <el-option v-for="item in statusAllList" :key="item.value" :label="item.label" :value="item.value">
                            <i :class="item.class" :style="{color: item.color}" />
                            <span style="color: #595959">{{ item.label }}</span>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="用例执行人" prop="execBy">
                        <vone-remote-user v-model="formData.execBy" :default-data="defaultData" :disabled="owner" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="用例优先级" prop="priority">
                        <vone-icon-select v-model="formData.priority" :data="prioritList" filterable clearable style="width:100%">
                          <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                            <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                            {{ item.name }}
                          </el-option>
                        </vone-icon-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              <div class="footer">
                <el-button plain @click="cancel">重置</el-button>
                <el-button type="primary" @click="searchData">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="筛选" placement="top">
                <i class="iconfont el-icon-application-filter" />
              </el-tooltip>
            </span>
          </el-popover>
          <el-divider style="margin: 0 10px 0 6px" direction="vertical" />
          <el-switch v-model="owner" class="openSwitch" :active-value="true" :inactive-value="false" active-text="我的" active-color="var(--main-theme-color)" @change="changeSwitch" />
        </div>
      </div>
      <div class="searchInput">
        <el-input v-model="filterText" placeholder="搜索" prefix-icon="el-icon-search" />
        <el-divider direction="vertical" />
        <el-button type="primary" style="margin-left:12px;min-width:60px;" @click="copyExcutedPlan">复用</el-button>
      </div>
      <div class="countContainer">
        <div class="item system" @click="getStatus('system')">
          <span>通过</span>
          <span>{{ progressData.systemNum }}</span>
        </div>
        <el-divider direction="vertical" />
        <div class="item smoke" @click="getStatus('smoke')">
          <span>失败</span>
          <span>{{ progressData.smokeNum }}</span>
        </div>
        <el-divider direction="vertical" />
        <div class="item block" @click="getStatus('block')">
          <span>阻塞</span>
          <span>{{ progressData.blockingNum }}</span>
        </div>
        <el-divider direction="vertical" />
        <div class="item skip" @click="getStatus('skip')">
          <span>跳过</span>
          <span>{{ progressData.skipNum }}</span>
        </div>
        <el-divider direction="vertical" />
        <div class="item" @click="getStatus('undo')">
          <span>未测</span>
          <span>{{ progressData.undoNum }}</span>
        </div>
      </div>
      <div class="treeContent">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          node-key="id"
          accordion
          :expand-on-click-node="false"
          :data="lastPlanList"
          :props="defaultProps"
          default-expand-all
          class="filter-tree"
          highlight-current
          :filter-node-method="filterNode"
          @node-click="treeNodeClick"
        >
          <div slot-scope="{ node, data }" class="custom-tree-node">
            <div v-if="data.type=== 'planCase'" class="tree-case-state" style="margin-right: 4px">
              <el-tag v-if="data.status === 'system'" class="systembox">成功</el-tag>
              <el-tag v-else-if="data.status === 'smoke'" class="smokebox">失败</el-tag>
              <el-tag v-else-if="data.status === 'skip'" class="skipbox">跳过</el-tag>
              <el-tag v-else-if="data.status === 'block'" class="blockbox">阻塞</el-tag>
              <el-tag v-else class="unbeginbox">未测</el-tag>
            </div>
            <div class="text-over" :class="{ 'custom-tree-box': !data.children, 'custom-tree-child': data.children }">

              <span class="custom-tree-title text-over" :title="data.name">
                <!-- <svg v-if="node.level !== 1&&data.type === 'tree'" class="icon iconCls" aria-hidden="true">
                  <use xlink:href="#el-application-filejia2" />
                </svg>
                <i v-else-if="data.type === 'planCase'" class="el-icon-document" style="color:var(--main-theme-color,#3e7bfa)" /> -->
                <span v-if="node.level === 1" class="treeNode  rootNode">{{ data.name }}</span>
                <span v-else class="treeNode ">{{ data.name }}</span>
              </span>
              <span v-if="data.children &&data.children.length>0" class="tag-num">
                <el-tooltip placement="right">
                  <template slot="content">
                    <div>通过：{{ data.system }}</div>
                    <div>失败：{{ data.smoke }}</div>
                    <div>跳过：{{ data.skip }}</div>
                    <div>阻塞：{{ data.block }}</div>
                    <div>未测：{{ data.undo }}</div>
                  </template>
                  <el-tag size="mini" type="info">{{ data.count }} </el-tag>
                </el-tooltip>
              </span>
            </div>

          </div>
        </el-tree>
      </div>
    </div>
    <div class="rightSection">
      <div class="header">
        <span class="back" @click="backPage">
          <i class="iconfont el-icon-direction-back" />
        </span>
        <span class="title">所属测试计划：{{ planData.name }}</span>
        <div v-if="!caseData.children && caseData.id" class="btnbox">
          <el-checkbox v-if="checkedShow" v-model="checked" label="继续执行下一条" tyle="color:var(--font-second-color)" />
          <el-divider direction="vertical" />
          <el-button :loading="excuting" class="system" @click="saveCaseExecuteResult('system')">
            通过
          </el-button>
          <el-button :loading="excuting" class="smoke" @click="saveCaseExecuteResult('smoke')">
            失败
          </el-button>
          <el-button :loading="excuting" class="block" @click="saveCaseExecuteResult('block')">
            阻塞
          </el-button>
          <el-button :loading="excuting" class="skip" @click="saveCaseExecuteResult('skip')">
            跳过
          </el-button>
          <el-button type="info" :loading="excuting" class="undo" @click="resetCaseExcuted">
            撤销
          </el-button>
        </div>
      </div>
      <div class="descbox">
        <div class="topbox">
          <el-tag v-if="caseData.caseKey" effect="dark" type="info" size="mini">{{ caseData.caseKey }} </el-tag>
          <h2>{{ caseData ? caseData.name : "" }}</h2>
        </div>
        <div v-if="caseData.id" class="detail ">
          <span>优先级
            <p v-if="caseData.priority === 1">
              <i class="iconfont el-icon-icon-dengji-zuidi2" style="color:#4ECF95;" />
              最低
            </p>
            <p v-else-if="caseData.priority === 2">
              <i class="iconfont el-icon-icon-dengji-jiaodi2" style="color:#5ACC5E;" />
              较低
            </p>
            <p v-else-if="caseData.priority === 5">
              <i class="iconfont el-icon-icon-dengji-zuigao2" style="color:#FA6A69;" />
              最高
            </p>
            <p v-else-if="caseData.priority === 4">
              <i class="iconfont el-icon-icon-dengji-jiaogao2" style="color:#FA8669;" />
              较高
            </p>
            <p v-else>
              <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color,#3e7bfa);" />
              普通
            </p>
          </span>
          <span>版本号:<p>
            <el-tag size="mini" effect="dark">{{ caseData.version || '1.0' }}</el-tag>
          </p></span>
          <span>执行人:<p>
            <vone-user-avatar :avatar-path="caseExctBy.avatarPath" :name="caseExctBy.name" />
          </p></span>
          <span>上次执行时间:<p>
            {{ dayjs(caseData.latestTime).format('YYYY-MM-DD HH:mm') }}
            <el-divider direction="vertical" />
            <a>
              <el-popover
                placement="bottom"
                width="480"
                title=" "
                trigger="click"
                :tabindex="-1"
                @show="getHistoryList"
              >
                <div class="popoverTitle">
                  历史记录
                  <el-tooltip class="item" effect="dark" content="仅显示当前用例的历史记录" placement="top">
                    <i class="iconfont el-icon-tips-info-circle" />
                  </el-tooltip>
                </div>
                <div style="height: 300px">
                  <vxe-table
                    ref="defectTable"
                    class="vone-vxe-table"
                    border
                    resizable
                    height="auto"
                    show-overflow="tooltip"
                    :empty-render="{ name: 'empty' }"
                    :data="historyData"
                    :column-config="{ minWidth:'120px' }"
                    :checkbox-config="{ reserve: true }"
                    row-id="id"
                  >
                    <vxe-column title="状态" field="status" show-overflow-tooltip>
                      <template v-slot="{ row }">
                        <el-tag v-if="row.status === 'system'" class="systembox">成功</el-tag>
                        <el-tag v-else-if="row.status === 'smoke'" class="smokebox">失败</el-tag>
                        <el-tag v-else-if="row.status === 'block'" class="blockbox">阻塞</el-tag>
                        <el-tag v-else-if="row.status === 'skip'" class="skipbox">跳过</el-tag>
                        <el-tag v-else class="unbeginbox">未测</el-tag>
                      </template>
                    </vxe-column>
                    <vxe-column title="版本" field="version" show-overflow-tooltip>
                      <template v-slot="{ row }">
                        <span> {{ row.version }}</span>
                      </template>
                    </vxe-column>
                    <vxe-column title="优先级" field="priority" show-overflow-tooltip>
                      <template v-slot="{ row }">
                        <span v-if="row.priority === 1">
                          <i class="iconfont el-icon-icon-dengji-zuidi2" style="color:#4ECF95;" />
                          最低
                        </span>
                        <span v-else-if="row.priority === 2">
                          <i class="iconfont el-icon-icon-dengji-jiaodi2" style="color:#5ACC5E;" />
                          较低
                        </span>
                        <span v-else-if="row.priority === 5">
                          <i class="iconfont el-icon-icon-dengji-zuigao2" style="color:#FA6A69;" />
                          最高
                        </span>
                        <span v-else-if="row.priority === 4">
                          <i class="iconfont el-icon-icon-dengji-jiaogao2" style="color:#FA8669;" />
                          较高
                        </span>
                        <span v-else>
                          <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color,#3e7bfa);" />
                          普通
                        </span>
                      </template>
                    </vxe-column>
                    <vxe-column title="执行人" field="execBy" show-overflow-tooltip>
                      <template v-slot="{ row }">
                        <span v-if="row.execBy && row.echoMap && row.echoMap.execBy">
                          <vone-user-avatar :avatar-path="row.echoMap.execBy.avatarPath" :name="row.echoMap.execBy.name" />
                        </span>
                        <span v-else> {{ row.execBy }}</span>
                      </template>
                    </vxe-column>
                    <vxe-column title="上次执行时间" field="createTime" show-overflow-tooltip>
                      <template v-slot="{ row }">
                        {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm') }}
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
                <i slot="reference" class="iconfont el-icon-application-run-history" />
              </el-popover>
            </a>
          </p>
          </span>
        </div>
      </div>
      <div v-if="caseData.id" class="case-detail">
        <!-- 测试计划不显示 -->
        <template v-if="caseData.parentId !== '0'">

          <el-row>
            <div class="detailTitle">前置条件</div>
            <div :class="{textInfo:!caseData.prerequisite}">{{ caseData.prerequisite || '无前置条件' }}</div>
          </el-row>

          <!-- 测试步骤 -->
          <el-row v-if="caseData.stepType === 'subclause'">
            <div class="detailTitle">测试步骤</div>
            <el-table class="vone-table" :data="stepData" row-key="id" hidden-search style="width: 100%">
              <el-table-column prop="caseStepNum" label="编号" fixed width="70" />
              <el-table-column prop="caseStepDes" label="步骤描述" />
              <el-table-column prop="expectResult" label="预期结果" />
              <el-table-column prop="result" label="实际结果">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.actualResult" placeholder="请输入实际结果" :rows="1" type="textarea" :autosize="{ minRows: 1, maxRows: 6 }" />
                </template>
              </el-table-column>
              <el-table-column prop="result" label="执行结果" width="110">
                <template slot-scope="scope">
                  <el-select ref="statusSelect" v-model="scope.row.status" style="width:100%" class="userList" @change="ExcuteStatus(scope.row,$event)">
                    <div v-if="scope.row.status && statusMap[scope.row.status]" slot="prefix" :style="{color:statusMap[scope.row.status].color}" class="select-header">
                      <i :class=" statusMap[scope.row.status].class" />
                      <span class="ml-6">
                        {{ statusMap[scope.row.status].label }}
                      </span>

                    </div>
                    <el-option v-for="item in statusAllList" :key="item.value" :label="item.label" :value="item.value">
                      <i :class="item.class" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                      {{ item.label }}
                    </el-option>
                  </el-select>

                </template>
              </el-table-column>
            </el-table>
          </el-row>
          <!-- 文本描述 -->
          <template v-else>
            <el-row>
              <div class="detailTitle">文本描述</div>
              <div :class="{textInfo:!caseData.testStep}">{{ caseData.testStep || '无文本描述' }}</div>
            </el-row>
            <el-row>
              <div class="detailTitle">预期结果</div>
              <div :class="{textInfo: !caseData.expectedResult}">{{ caseData.expectedResult || '无预期结果' }}</div>
            </el-row>
            <el-row style="padding-right:12px;">
              <div class="detailTitle">实际结果</div>
              <el-input v-model="actualResult" type="textarea" placeholder="请输入实际结果" :autosize="{ minRows: 3, maxRows: 20 }" show-word-limit maxlength="255" size="mini" />
            </el-row>
          </template>
        </template>
        <el-row v-if="caseData.parentId !== '0'">
          <div class="detailTitle">用例实际执行时长</div>

          <el-form ref="casetruexecTime" inline :rules="caseDataRules" :model="caseData">
            <el-form-item label="" prop="truexecTime">
              <el-input v-model.trim="caseData.truexecTime" placeholder="请输入数字">
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </el-form>

        </el-row>
        <el-row class="defectBox">
          <div class="detailTitle" :class="{titleTop: caseData && caseData.parentId !== '0'}" style="display:inline-block">缺陷</div>
          <div style="float: right; margin-top:16px; display:flex">

            <el-button-group>
              <el-tooltip content="快速新增" placement="top">
                <el-button
                  class="subBtton"
                  :icon="`iconfont  ${
                    defectAdd ? 'el-icon-direction-double-left' : 'el-icon-direction-double-down'
                  }`"
                  type="primary"
                  @click.stop="createFatst"
                />
              </el-tooltip>
              <el-button icon="iconfont el-icon-tips-plus-circle" type="primary" @click.stop="addDedectDialog">新增</el-button>
            </el-button-group>
            <el-dropdown trigger="click" @command="(e) => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
              <el-dropdown-menu slot="dropdown">

                <el-dropdown-item :command="connentProject">
                  关联缺陷
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div style="text-align:right;margin: 10px 0 16px 0">
            <addDefect v-if="defectAdd" style="margin-right:10px;" :defect-type="typeCodeList" :case-data="caseData" :env-list="envList" @createDetail="openCreateDefect" @success="addSuccess(!caseData.id)" @cancel="() => defectAdd = false" />
          </div>
          <main style="height:758px;margin-top: 12px">
            <vxe-table
              ref="defectTable"
              class="vone-vxe-table"
              height="auto"
              border
              resizable
              show-overflow="tooltip"
              :empty-render="{ name: 'empty' }"
              :data="defectData.records"
              :column-config="{ minWidth:'120px' }"
              :checkbox-config="{ reserve: true }"
              row-id="id"
            >
              <vxe-column title="缺陷标题" field="name">
                <template #default="{ row }">
                  <div class="name_icon">
                    <a class="mx-1 table_title" @click="showInfo(row)">
                      <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
                        <i :class="`iconfont ${row.echoMap.typeCode.icon}`" :style="{ color:`${row.echoMap.priorityCode ? row.echoMap.priorityCode.color : '#ccc'}`}" />
                      </span>
                      {{ row.code + " " + row.name }}</a>
                    <!-- 是否延期 -->
                    <span v-if="row.delay">
                      <i class="el-icon-warning-outline color-danger" />
                    </span>

                  </div>
                </template>
              </vxe-column>
              <vxe-column title="状态" field="stateCode" width="120">
                <template #default="{ row }">
                  <defectStatus v-if="row" :key="Date.now()" :workitem="row" @changeFlow="getInitTableData" />
                </template>
              </vxe-column>
              <vxe-column title="处理人" field="handleBy">
                <template #default="{ row }">
                  <span v-if="row.handleBy && row.echoMap && row.echoMap.handleBy">
                    <vone-user-avatar :avatar-path="row.echoMap.handleBy.avatarPath" :name="row.echoMap.handleBy.name" />
                  </span>

                </template>
              </vxe-column>
              <vxe-column title="缺陷类型" field="typeCode">
                <template #default="{ row }">
                  <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
                    {{ row.echoMap.typeCode.name }}
                  </span>
                  <span v-else>{{ row.typeCode }}</span>
                </template>
              </vxe-column>
              <vxe-column title="优先级" field="priorityCode" width="80">
                <template #default="{ row }">
                  <div v-if="row.priorityCode && row.echoMap && row.echoMap.priorityCode">
                    <i :class="`iconfont ${ row.echoMap.priorityCode.icon}`" :style="{ color:`${ row.echoMap.priorityCode ? row.echoMap.priorityCode.color : '#ccc'}`}" />
                    {{ row.echoMap.priorityCode.name }}
                  </div>
                </template>
              </vxe-column>
              <vxe-column title="操作" fixed="right" align="left" width="120">
                <template #default="{ row }">
                  <span class="operation-icon-main">
                    <el-tooltip class="item" content="编辑" placement="top">
                      <el-button size="mini" type="text" icon="iconfont el-icon-application-edit" @click="defectEditMaintenance(row)" />
                    </el-tooltip>
                    <el-divider direction="vertical" />
                    <el-tooltip class="item" content="删除" placement="top">
                      <el-button type="text" icon="iconfont el-icon-application-delete" @click="deleteDefect(row)" />
                    </el-tooltip>
                  </span>
                </template>
              </vxe-column>
            </vxe-table>
          </main>
          <vone-pagination ref="pagination" :total="defectData.total" @update="getInitTableData" />
        </el-row>
      </div>

      <!-- 是否升级为最新版本，提示框 -->
      <!-- <div v-if="showUpdate" class="notification">
        <el-row type="flex">
          <el-col :span="18">
            <strong>{{ updateMessage }}</strong>
          </el-col>
          <el-col :span="6" style="text-align:right">
            <a @click="showUpdate = false">
              <i class="iconfont el-icon-tips-close" />
            </a>
          </el-col>
        </el-row>

        <p v-if="showUpdate">是否升级为最新版本?</p>
        <el-button v-if="showUpdate" :loading="updateLoading" @click="update">
          升级
        </el-button>
      </div> -->

    </div>
    <!-- 复用测试计划 -->
    <CopyPlan v-if="planParams.copyVisible" v-bind="planParams" v-model:visible="planParams.copyVisible" plan-type="filter" :plan-data="planData" :tree-data="filterCases" :form-data="formData" />
    <!-- 关联缺陷 -->
    <connentBug v-if="projectParam.visible" v-model:visible="projectParam.visible" :plan-data="planData" :case-data="caseData" :defect-data="defectData" @success="getInitTableData(!caseData.id)" />
    <!-- 新增完整缺陷 -->
    <vone-custom-add v-if="defectParamAdd.visible" :key="defectParamAdd.key" v-model:visible="defectParamAdd.visible" type-code="BUG" v-bind="defectParamAdd" :form-data="defectParamAdd.data" title="新增缺陷" @success="getInitTableData(!caseData.id)" />
    <!-- 编辑完整缺陷 -->
    <vone-custom-edit v-if="defectParam.visible" :key="defectParam.key" v-model:visible="defectParam.visible" v-bind="defectParam" type-code="BUG" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData(!caseData.id)" />
    <!-- 缺陷详情 -->
    <vone-custom-info v-if="defectInfoParam.visible" :key="defectInfoParam.key" v-model:visible="defectInfoParam.visible" v-bind="defectInfoParam" type-code="BUG" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData(!caseData.id)" />
  </div>
</template>

<script>

import storage from 'store'
import { apiAlmBugDel } from '@/api/vone/project/defect'
import { getDefectTypeList } from '@/api/vone/alm'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import { planUpgrade, testProductCaseIsNewVersion, getPlanCaseTree } from '@/api/vone/testmanage/index'
import { excutePlanCaseResult, getPlanCaseResult, getPlanExcuteDetail, revertTestCaseResult } from '@/api/vone/testmanage/plan'
import { productCaseDetail, getTestCaseStateList } from '@/api/vone/testmanage/case'
import { queryPlanById, queryBugPlan } from '@/api/vone/testplan'
import { getTestHistoryList } from '@/api/vone/project/test'
import addDefect from './components/add-defect.vue'
import defectStatus from '@/views/vone/project/common/change-status/index.vue'
import CopyPlan from './components/copy-plan.vue'
import connentBug from './components/connent-bug.vue'

import { catchErr } from '@/utils'

export default {
  components: {
    addDefect,
    defectStatus,
    CopyPlan,
    connentBug
  },
  data() {
    const validateDepth = (rule, value, callBack) => {
      if (value && !/^[1-9]\d*$/.test(value)) {
        callBack(new Error('请输入不以0开头的数字'))
      } else {
        callBack()
      }
    }
    return {
      statusMap: {},
      checkedShow: true,
      caseDataRules: {
        truexecTime: [
          { validator: validateDepth, trigger: 'blur' }
        ]
      },
      projectParam: {
        visible: false
      },
      execMap: {},
      searchPopover: false, // 搜索弹窗是否显示
      updateMessage: '',
      treeLoading: false,
      owner: false,
      defaultData: [],
      formData: { status: null, priority: null, execBy: '' },
      visible: false,
      updateLoading: false,
      showUpdate: false,
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      statusAllList: [
        // 下拉框状态
        { value: 'system', label: '成功', class: 'iconfont el-icon-tips-check-circle-fill', color: 'var(--Green)' },
        { value: 'smoke', label: '失败', class: 'iconfont el-icon-tips-close-circle-fill', color: 'var(--Red)' },
        { value: 'block', label: '阻塞', class: 'iconfont el-icon-tips-back-circle-fill', color: 'var(--Yellow)' },
        { value: 'skip', label: '跳过', class: 'iconfont el-icon-tips-minus-circle-fill', color: 'var(--Purple)' }
        // { value: 'undo', label: '未测', class: 'el-icon-question', color: '#ADB0B8' }
      ],
      planId: this.$route.params.planId, // 当前计划id
      actualResult: '', // 用例执行结果对象,表单对象
      defectData: { records: [], total: 0 }, // 用例缺陷表格数据
      caseData: {}, // 用例数据
      stepData: [], // 测试步骤数据
      lastPlanList: [{ children: [] }], // 测试计划用例列表
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      progressData: {}, // 计划状态数据
      filterText: '', // 模糊搜索
      planData: {}, // 计划数据
      filterCases: [], // 筛选的用例
      allCaseList: [], // 全部用例
      statusList: {
        '1': '成功',
        '-1': '失败',
        '3': '阻塞',
        '2': '跳过'
      },
      planParams: {
        copyVisible: false
      },

      checked: false,
      excuting: false,
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        operation: {
          isFixed: false, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.defectEditMaintenance // 操作事件
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteDefect // 操作事件
            }
          ],
          moreData: []
        }
      },
      rules: {
        name: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        typeCode: [{ required: true, message: '请选择分类', trigger: 'blur' }],
        user: [{ required: true, message: '请选择人员', trigger: 'blur' }]
      },
      caseExctBy: {}, // 用例执行人数据
      typeCodeList: [], // 缺陷分类列表
      envList: [], // 环境列表
      defectAdd: false,
      defectParamAdd: { visible: false }, // 新增
      defectParam: { visible: false },
      defectInfoParam: { visible: false },
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [],
      historyData: []
    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    }
  },
  watch: {
    filterText(val) {
      this.filterCases = val ? this.allCaseList.filter(item => item.name.indexOf(val) > -1) : this.allCaseList
      // 监听的是左侧的计划列表,和下边filterNode方法配合
      this.$refs.tree.filter(val)
    }
  },
  async mounted() {
    this.getPlanCaseStatus()
    this.getPlanDetail()
    await this.getExcuteTree()
    this.fetchCaseData()
    this.statusMap = this.statusAllList.reduce((r, v) => (r[v.value] = v) && r, {})
  },
  methods: {
    // 点击状态筛选用例
    getStatus(val) {
      this.formData.status = val
      this.getExcuteTree()
      this.fetchCaseData()
    },
    addDedectDialog() {
      this.defectParamAdd = {
        visible: true,
        data: {
          testPlanId: this.$route.params.planId,
          testcaseId: this.caseData.id
        },
        key: Date.now(),
        infoDisabled: false
      }
    },
    connentProject() {
      this.projectParam.visible = true
    },
    backPage() {
      this.$router.back()
    },
    // 预期步骤的执行结果
    ExcuteStatus(row, e, t) {
      this.saveCaseExecuteResult(this.caseData.status || 'undo', 'noding')
    },
    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true
      if (data.name?.indexOf(value) > -1) {
        return true
      } else {
        return false
      }
    },

    // 查询测试计划数据
    async getPlanDetail() {
      const [res, err] = await catchErr(queryPlanById(this.planId))
      if (err) return
      if (res.isSuccess) {
        this.planData = res.data
      }
    },
    // 查询用例执行树
    async getExcuteTree() {
      const params = {
        planId: this.planId,
        execBy: this.formData.execBy || '',
        priority: this.formData.priority,
        status: this.formData.status ? this.formData.status.toString() : ''
      }
      this.treeLoading = true
      const [res, err] = await catchErr(getPlanCaseTree(params))
      this.treeLoading = false
      if (err) return
      if (res.isSuccess) {
        this.lastPlanList = res.data
        // 筛选所有用例
        this.allCaseList = this.filCaseQueues(res.data)
        // 筛选的用例
        this.filterCases = [...res.data]
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.allCaseList[0]?.id)
        })
      }
    },
    fetchCaseData(nextIndex) {
      if (!this.caseData.id) {
        this.caseData = this.allCaseList[0]
      }
      // 是否勾选下一用例或当前未选中用例，默认显示第一个
      if (nextIndex) {
        this.caseData = this.allCaseList.length ? this.allCaseList[nextIndex] : this.lastPlanList[0]
      }
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.caseData.id)
      })
      this.checkedShow = this.caseData.id !== this.allCaseList[this.allCaseList.length - 1]?.id

      // 查询用例详情
      this.getPlanCaseDetail()
      // 查询用例缺陷
      this.getInitTableData()
      // 查询用例执行时间
      this.queryCaseTime()
    },
    // 筛选出所有用例
    filCaseQueues(ary) {
      const result = ary.filter(node => node.type === 'planCase')
      let queue = [...ary]
      while (queue.length > 0) {
        const node = queue.shift()
        if (node.type === 'planCase') {
          result.push(node)
        }
        if (node.type === 'tree') {
          queue = [...node.children, ...queue]
        }
      }
      return result
    },
    //  查询用例执行时间
    async queryCaseTime() {
      const params = {
        planId: this.planId,
        testcaseId: this.caseData.id
      }
      getPlanCaseResult(params).then(res => {
        if (res.isSuccess) {
          this.caseData.latestTime = res.data.latestTime
          this.caseExctBy.avatarPath = res.data.execAvatarPath
          this.caseExctBy.name = res.data.execName
          this.caseData.truexecTime = res.data.truexecTime
          if (this.caseData.stepType === 'subclause') {
            this.stepData = JSON.parse(res.data.result)
          }
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 查询计划用例状态
    async getPlanCaseStatus() {
      const params = {
        planId: this.planId
      }
      const [res, err] = await catchErr(getPlanExcuteDetail(params))
      if (err) return
      if (res.isSuccess) {
        this.progressData = res.data
        const { blockingNum, skipNum, smokeNum, systemNum, undoNum } = res.data
        // 关联用例总数
        this.progressData.sum = Number(blockingNum) + Number(skipNum) + Number(smokeNum) + Number(systemNum) + Number(undoNum)
        // 成功百分比
        this.progressData.systemPercent = (systemNum / this.progressData.sum * 100).toFixed(2)
        // 阻塞百分比
        this.progressData.blockPercent = (blockingNum / this.progressData.sum * 100).toFixed(2)
        // 跳过百分比
        this.progressData.skipPercent = (skipNum / this.progressData.sum * 100).toFixed(2)
        // 失败百分比
        this.progressData.smokePercent = (smokeNum / this.progressData.sum * 100).toFixed(2)
        // 未测试百分比
        this.progressData.undoPercent = (undoNum / this.progressData.sum * 100).toFixed(2)
      }
    },
    // 显示测试计划详情抽屉
    async copyExcutedPlan() {
      this.planParams = { copyVisible: true }
    },
    // 点击左侧树切换节点
    async treeNodeClick(val, node) {
      if (val.parentId === '0') {
        this.caseData = val
        this.getInitTableData([])
        return
      }
      if (val.parentId !== '0' && node.data.type === 'tree') {
        this.caseData = {}
        return
      }
      this.caseData = val
      this.actualResult = ''

      this.getPlanCaseDetail()
      this.queryCaseTime() // 查询用例执行时间（执行时间回显）
      // 查询用例关联缺陷
      this.getInitTableData()
      // 用来显示是否需要显示继续执行
      if (this.caseData.status) {
        this.getHistoryList()
      }
      this.checkedShow = val.id !== this.allCaseList[this.allCaseList.length - 1]?.id
    },
    getHistoryList() {
      getTestHistoryList({
        planId: this.planId,
        testcaseId: this.caseData.id
      }).then(res => {
        if (res.isSuccess) {
          this.historyData = res.data || []
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 查询用例详情
    async getPlanCaseDetail() {
      const [res, err] = await catchErr(productCaseDetail(this.caseData.id))
      if (err) return
      if (res.isSuccess) {
        const version = this.caseData.version
        const priority = this.caseData.priority // 测试计划下的用例优先级
        this.caseData = { ...this.caseData, ...res.data, version, priority }
        if (this.caseData.stepType === 'subclause') {
          this.stepData = this.caseData.testStep && JSON.parse(this.caseData.testStep)
          this.stepData?.length > 0 && this.stepData.forEach(ele => {
            !ele.actualResult && this.$set(ele, 'actualResult', '')
            !ele.status && this.$set(ele, 'status', '')
          })
        }
      }
      // 查询用例执行结果
      this.getCurrentCaseResult()
      // 检验当前用例版本
      this.checkVersion()
      if (this.caseData.status) {
        this.getHistoryList()
      } else {
        this.historyData = []
      }
    },
    // 查询用例执行结果
    async getCurrentCaseResult() {
      const params = {
        planId: this.planId,
        testcaseId: this.caseData.id
      }
      const [res, err] = await catchErr(getPlanCaseResult(params))
      if (err) return
      if (res.isSuccess) {
        if (this.caseData.stepType === 'subclause') {
          res.data.result && (this.stepData = JSON.parse(res.data.result))
          this.stepData?.length > 0 && this.stepData.forEach(ele => {
            !ele.actualResult && this.$set(ele, 'actualResult', '')
            !ele.status && this.$set(ele, 'status', '')
          })
        } else {
          this.actualResult = res.data.result
        }
      }
    },
    // 撤销用例执行结果
    async resetCaseExcuted() {
      const confirm = await catchErr(this.$confirm(`是否撤销用例当前执行结果？`, {
        type: 'warning'
      }))
      if (confirm[1]) return
      const currentNode = this.$refs.tree.getNode(this.caseData.id)
      this.excuting = true
      const [res, err] = await catchErr(revertTestCaseResult({
        planId: this.planId,
        testCaseId: this.caseData.id
      }))
      if (err) {
        this.excuting = false
        return
      }
      if (res.isSuccess) {
        // 修改用户
        this.caseData.execBy = this.loginUser.id
        // 修改父级用例执行数据
        this.updateParentCounts(currentNode, res.data.status || 'undo', currentNode.data.status || 'undo')
        currentNode.data.status = res.data.status
        // 刷新测试计划用例里程碑
        this.getPlanCaseStatus()
        const nextIndex = this.nextExcute()

        this.fetchCaseData(nextIndex > 0 ? nextIndex - 1 : nextIndex)
        this.$message.success('撤销成功')
      } else {
        this.$message.error(res.msg)
      }
      this.excuting = false
    },
    // 更新父级节点状态数
    updateParentCounts(node, plusStatus, minusStatus) {
      // 状态相同直接返回
      if (plusStatus === minusStatus) return
      let parent = node.parent
      while (parent) {
        parent.data[plusStatus] >= 0 && parent.data[plusStatus]++
        parent.data[minusStatus] >= 0 && parent.data[minusStatus]--
        if (parent.data[minusStatus] < 0) parent.data[minusStatus] = 0
        parent = parent.parent
      }
    },
    // 创建用例执行结果
    async saveCaseExecuteResult(val, text) {
      this.$refs.casetruexecTime.validate(valid => {
        if (valid) {
          this.saveResult(val, text)
        }
      })
    },
    async saveResult(val, text) {
      let result = this.actualResult
      if (this.caseData.stepType === 'subclause') {
        result = JSON.stringify(this.stepData)
      }
      if (val == 'smoke' && (!this.defectData.records || this.defectData.records.length === 0)) {
        if (this.$route.query.bug) {
          this.$message.warning('请填写缺陷')
          return
        }
      }
      const currentNode = this.$refs.tree.getNode(this.caseData.id)
      const params = {
        planIdString: this.planId,
        result: result,
        status: val,
        caseId: this.caseData.id,
        version: this.caseData.version,
        truexecTime: this.caseData.truexecTime
      }
      this.excuting = true
      const [res, err] = await catchErr(excutePlanCaseResult(params))
      if (err) {
        this.excuting = false
        return
      }
      if (text) {
        this.getCurrentCaseResult()
        this.$message.success('执行成功')
        this.excuting = false

        return
      }
      if (res.isSuccess) {
        // 修改用户
        this.caseData.execBy = this.loginUser.id
        // 是否执行下一条
        const nextIndex = this.checked && this.nextExcute()

        this.updateParentCounts(currentNode, val, currentNode.data.status || 'undo')
        currentNode.data.status = val
        this.$message.success('执行成功')
        // 刷新状态
        // 刷新测试计划用例里程碑
        this.getPlanCaseStatus()

        this.fetchCaseData(nextIndex)

        if (this.checked) {
          this.actualResult = ''
        }
        // 查询用例执行结果
        await this.getCurrentCaseResult()
      }
      this.excuting = false
    },
    // 切换到下一用例
    nextExcute() {
      const list = this.allCaseList
      let nextIndex = 0
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === this.caseData.id) {
          if (list.length === i + 1) {
            nextIndex = i
          } else {
            nextIndex = i + 1
            this.$refs.tree.setCurrentKey(this.caseData.id)
            return nextIndex
          }
        }
      }
      return nextIndex
    },
    // 删除缺陷
    async deleteDefect({ id, name }) {
      this.$confirm(`确定删除 ${name}吗？`, '删除', {
        type: 'warning',
        customClass: 'delConfirm'
      })
        .then(async(actions) => {
          const [{ isSuccess, msg }, err] = await catchErr(apiAlmBugDel([id]))
          if (err) return
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success('删除成功')
          // 是否查询计划关联缺陷
          this.getInitTableData(!this.caseData.id)
        })
        .catch(() => { })
    },
    // 编辑缺陷
    defectEditMaintenance(row) {
      this.defectParam = {
        visible: true,
        title: '编辑缺陷',
        key: Date.now(),
        id: row.id,
        infoDisabled: false
      }
    },
    // 查看用例
    showInfo(row) {
      this.defectInfoParam = {
        visible: true,
        title: '缺陷详情',
        key: Date.now(),
        id: row.id,
        infoDisabled: true
      }
    },
    openCreateDefect() {
      this.defectParam = {
        visible: true,
        title: '新增缺陷',
        key: Date.now(),
        infoDisabled: false
      }
    },
    // 查询缺陷分类
    async getIssueType() {
      const [res, err] = await catchErr(getDefectTypeList())
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.typeCodeList = res.data
    },
    // 查询环境
    async getEnvList() {
      const [res, err] = await catchErr(apiBaseDictNoPage({
        type: 'ENVIRONMENT',
        state: true
      }))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.envList = res.data
    },
    // 查询用例缺陷列表
    async getInitTableData(root) {
      const { caseId } = this.$route.query
      // 获取查询参数
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        extra: {},
        model: {
          projectId: ['0'],
          testPlanId: [this.planId], // 计划id
          testcaseId: this.caseData.type == 'tree' ? [] : root ? [] : [this.caseData.id] || [caseId]
        }
      }
      const [res, err] = await catchErr(queryBugPlan(params))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.defectData = res.data
    },
    async addSuccess(val) {
      if (this.caseData.type == 'tree') {
        this.getInitTableData([])
        return
      }
      this.getInitTableData(val)
    },
    createFatst() {
      this.defectAdd = !this.defectAdd
      this.openCreateDialog()
    },
    // 新增缺陷
    openCreateDialog() {
      this.getIssueType()
      this.getEnvList()
      this.defectAdd = true
    },

    // 检查当前用例是否是最新版本
    async checkVersion() {
      const [res, err] = await catchErr(testProductCaseIsNewVersion({
        id: this.caseData.id,
        version: this.caseData.version
      }))
      if (err) return
      if (res.isSuccess) {
        if (!res.data) {
          this.showUpdate = true
          this.updateMessage = '当前用例不是最新版本'
        } else {
          this.showUpdate = false
        }
      }
      if (!res.isSuccess) {
        // 如果接口失败
        this.showUpdate = true
        this.updateMessage = res.msg
      }
    },
    // 升级
    async update() {
      this.updateLoading = true
      const params = {
        planId: this.planId,
        testcaseId: this.caseData.id
      }
      const [res, err] = await catchErr(planUpgrade(params))
      this.updateLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('升级成功')
      const newVersion = (Number(this.caseData.version?.substring(1, 10)) + 1).toString()
      this.$set(this.caseData, 'version', `v${newVersion}.0`)
      this.version = `v${newVersion}.0`
      this.getExcuteTree()
      this.fetchCaseData()
      this.checkVersion()
    },
    // 属于我的
    changeSwitch(val) {
      this.$set(this.formData, 'execBy', val ? this.loginUser.id : '')
      this.getExcuteTree()
      this.fetchCaseData()
    },
    cancel() {
      this.searchPopover = false
      this.owner = false
      this.formData = {}
      this.getExcuteTree()
      this.fetchCaseData()
    },
    // 筛选数据
    searchData() {
      this.searchPopover = false
      this.getExcuteTree()
      this.fetchCaseData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftSection {
  width: 320px;
  position: relative;
  .iconfont {
    font-weight: normal;
    color: var(--font-second-color);
  }
  .header {
    line-height: 48px;
    height: 47px;
    border-bottom: 1px solid var(--el-divider);
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-items: center;
    .title {
      flex: 1;
      font-weight: 500;
    }

  }
}
.rightSection {
  .header {
    line-height: 48px;
    height: 47px;
    border-bottom: 1px solid var(--el-divider);
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-items: center;
    margin:-16px -16px 0px -16px;
    .title {
      color: var(--font-second-color);
      margin-left: 16px;
      font-weight: 400;
    }
    .btnbox {
      flex: 1;
      text-align: right;
      .el-button + .el-button {
        margin-left: 12px;
        border: none;
      }
      .system {
        background: var(--Green);
        color: #fff;
        border: none;
      }
      .smoke {
        background: var(--Red);
        color: #fff;
      }
      .block {
        background: var(--Yellow);
        color: #fff;
      }
      .skip {
        background: var(--Purple);
        color: #fff;
      }
      .undo {
         background: var(--Gray);
        color: #fff;
      }
    }
  }
  .descbox {
    border-bottom: 1px solid var(--el-divider);
    padding: 16px;
    margin: 0px -16px;
    h2 {
      display:inline-block;
      font-size: 16px;
      line-height: 24px;
      margin:0px;
      margin-left: 6px;
    }
    .topbox {
      margin-bottom: 16px;
      line-height: 24px;
    }
  }
  .detail {
		line-height: 24px;
    height: 24px;
		span {
			color: var(--font-second-color);
			margin-right: 16px;
			display:inline-flex;
		}
    .el-tag {
      color: var(--main-theme-color);
    }
		p {
			display: inline-block;
			margin: 0px;
			margin-left:12px;
      line-height: 24px;
			color: var(--font-main-color);
		}
    :deep(.avatar span) {
      margin-left: 4px;
    }
    a {
      height: 24px;
      width: 24px;
      line-height: 24px;
      text-align: center;
      background: var(--Orange--50);
      display: inline-block;
      .el-icon-application-run-history {
        color: var(--Orange);
        padding:0px 4px
      }
    }
	}
}
:deep(.openSwitch .el-switch__core) {
  min-width: 50px;
  overflow: hidden;
}
.searchInput {
  display: flex;
  margin: 12px;
  align-items: center;
  :deep(.el-divider) {
    height: 24px;
    margin: 0px 8px;
  }
}
.countContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 4px 10px;
  background-color: var(--content-bg-color);
  border-top: 1px solid var(--solid-border-color);
  border-bottom: 1px solid var(--solid-border-color);
  span {
    line-height: 18px;
  }
  .item {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 12px;
    font-weight: 600;
    width: 48px;
    color: var(--Gray);
  }
  .system {
    color: var(--Green);
  }
  .smoke {
    color: var(--Red);
  }
  .block {
    color: var(--Yellow);
  }
  .skip {
    color: var(--Purple);
  }
  .el-divider--vertical {
    height: 16px;
    margin: 0 6px;
  }
}
.system {
  color: var(--Green);
}
.smoke {
  color: var(--Red);
}
.block {
  color: var(--Yellow);
}
.skip {
  color: var(--Purple);
}
.treeContent {
    height: calc(100vh - 232px);

    overflow: auto;
    :deep(.el-tree-node__content) {
    height: 36px;
  }
  :deep(.el-tree-node>.el-tree-node__children) {
    overflow: unset;
  }
  .icon {
    font-size: 16px;
    margin:0px 4px
  }
}
.custom-tree-node {
  padding-right: 6px;
  flex: 1;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  .custom-tree-box {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    & > div,
    .custom-tree-title {
      flex: 1 1 auto;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
    }
  }
  .custom-tree-child {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    height: 100%;
    position: relative;
    .custom-tree-poz {
      position: absolute;
      top: 0px;
      left: 0px;
      height: 100%;
      max-width: 100%;
      display: flex;
      align-items: center;
    }
    .custom-tree-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      justify-items: center;
      justify-content: center;
      line-height: 18px;
    }
    .tag-num {
      min-width: 24px;
      flex: 0 0 auto;
    }
  }
  :deep(.tree-case-state) {
    i {
      font-size: 18px;
      line-height: 18px;
      display: flex;
      justify-items: center;
      span {
        font-size: 12px;
        color: #595959;
      }
    }
    .el-icon-question {
      color: #dcdfe6;
      span {
        color: #bfbfbf;
      }
    }
  }

  .el-tag.el-tag--info {
    background-color: var(--content-bg-hover-color);
    color: var(--font-second-color);
    font-weight: 500;
    border: none;
    width: 27px;
    height: 18px;
    border-radius: 100px;
    text-align: center;
    margin-left: 4px;
  }
}
.systembox {
  color: var(--Green);
  background: var(--Green--50);
  border-color: var(--Green--50);
}
.smokebox {
  color: var(--Red);
  background: var(--Red--50);
  border-color: var(--Red--50);
}
.blockbox {
  color: var(--Yellow);
  background: var(--Yellow--50);
  border-color: var(--Yellow--50);
}
.skipbox {
  color: var(--Purple);
  background: var(--Purple--50);
  border-color: var(--Purple--50);
}
.unbeginbox {
  color: var(--Gray);
  background: var(--Gray-3);
  border-color: var(--Gray-3);
}
.back {
  width: 32px;
  height: 32px;
  line-height: 32px;
  background: #fff;
  text-align: center;
  border-radius: 50%;
  color: var(--main-theme-color);
  box-shadow: var(--dialog-shadow);
}
.detailTitle {
  color: var(--font-main-color);
  font-size: 16px;
  line-height: 32px;
  font-weight: 500;
  margin: 16px 0px 4px 0px;
}
.textInfo {
  line-height: 22px;
  color: var(--font-second-color);
}
.basicBox {
  display: flex;
  justify-items: center;
  justify-content: center;
  cursor: pointer;
  .basicIcon {
    color: #bfbfbf;
    span {
      margin-left: 4px;
      font-size: 14px;
    }
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 20px;
    left: 46px;
    height: 36px;
    z-index: 99;
    width: 90px;
    cursor: pointer;
    :deep(.el-input) {
      height: 15px;
    }
  }
}
.tableInput {
  :deep(.el-textarea__inner) {
    border:none
  }
}
// .popover
.popoverTitle {
  color: var(--font-main-color);
  border-bottom: 1px solid var(--solid-border-color);
  line-height: 40px;
  margin: -19px -12px 12px -12px;
  padding: 0px 12px;
  font-weight: 500;
  .iconfont {
    color: var(--font-second-color);
  }
}
.userList{

:deep() {
.el-input__inner {
  padding: 0;
  color: #fff;
  border: 0;
  padding-left: 25px;
}
.select-header {
    cursor: pointer;
		min-width: 28px;
		height: 100%;
		display: flex;
		align-items: center;
		text-align: center;
		font-size: 14px;
		color: #4983F3;
	}
}}
.ml-6{
  margin-left: 6px;
}

</style>
