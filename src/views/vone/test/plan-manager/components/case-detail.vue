<!-- 用例详情 -->
<template>
  <el-drawer
    v-loading="caseCardLoading"
    :title="'用例详情'"
    v-model="visible"
    :modal="false"
    size="40%"
    :append-to-body="false"
    :modal-append-to-body="false"
    :before-close="onClose"
    :wrapper-closable="false"
  >

    <div class="caseForm">
      <el-form ref="caseFormRef" :key="id" :model="caseForm" :rules="addRules" label-position="top" disabled>
        <div class="fixhe">
          <el-row class="form_title">

            <el-form-item prop="name">
              <el-input v-model="caseForm.name" placeholder="请输入用例标题" @input="changeInput" />
            </el-form-item>

          </el-row>

          <el-row>
            <el-col :span="10" :offset="1">
              <div>
                <el-form-item label="维护人" prop="leadingBy">
                  <vone-remote-user v-model="caseForm.leadingBy" :default-data="defaultUser" @change="changeInput" />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="10" :offset="1">
              <div class="priority">
                <el-form-item label="优先级" prop="priority">
                  <vone-icon-select v-model="caseForm.priority" :data="prioritList" placeholder="请选择优先级" filterable clearable class="select level-select" :no-permission="noPermission" @change="changeInput">
                    <el-option v-for="item in prioritList" :key="item.code" :label="item.name" :value="item.code">
                      <div style="display: flex; align-items: center">
                        <i :class="`iconfont ${item.icon}`" :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }" />
                        <span>{{ item.name }}</span>
                      </div>
                    </el-option>
                  </vone-icon-select>
                </el-form-item>
              </div>

            </el-col>
          </el-row>
        </div>
        <div class="attribute">
          <h4>属性</h4>
          <el-row>
            <el-col :span="12">
              <el-form-item label="所属分组" style="width:95%">
                <el-popover
                  ref="groupPopover"
                  class="groupPopper"
                  placement="bottom"
                  title="选择分组"
                  trigger="click"
                  disabled
                >
                  <div class="popoverMain">
                    <el-tree
                      v-if="treeData.length>0"
                      ref="tree"
                      class="filter-tree"
                      :data="treeData"
                      node-key="id"
                      highlight-current
                      :expand-on-click-node="false"
                      @node-click="nodeClick"
                    >
                      <div slot-scope="{ node, data }" class="custom-tree-node">
                        <span :title="data.name">
                          <!-- 文件图标 -->
                          <i v-if="node.level === 1" class="iconfont el-icon-cangku" style="color: var(--main-theme-color,#3e7bfa)" />
                          <svg v-else class="icon" aria-hidden="true">
                            <use xlink:href="#el-application-filejia2" />
                          </svg>
                          <el-button style="padding:0;min-width:0;color:#202124" :disabled="data.parentId=='0'" type="text" class="treeNode">{{ data.name }}</el-button>
                        </span>
                      </div>
                    </el-tree>
                    <el-tree
                      v-else
                      ref="tree"
                      class="filter-tree"
                      :load="loadNode"
                      lazy
                      node-key="id"
                      :highlight-current="true"
                      :expand-on-click-node="false"
                      @node-click="nodeClick"
                    >
                      <div slot-scope="{ node, data }" class="custom-tree-node">
                        <span :title="data.name">
                          <!-- 文件图标 -->
                          <i
                            v-if="node.level === 1"
                            class="iconfont el-icon-cangku"
                            style="color: var(--main-theme-color,#3e7bfa)"
                          />
                          <svg v-else class="icon" aria-hidden="true">
                            <use xlink:href="#el-application-filejia2" />
                          </svg>
                          <el-button style="padding:0;min-width:0;color:#202124" :disabled="data.parentId=='0'" type="text" class="treeNode">{{ data.name }}</el-button>
                        </span>
                      </div>
                    </el-tree>
                  </div>
                  <div slot="reference" class="referenceEmit" style="background: #ebeef5;">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#el-application-filejia2" />
                    </svg>
                    <span class="treeName" :title="newTreeName">{{ newTreeName }}</span>

                  </div>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12" class="execTime">
              <el-form-item label="用例预估执行时长" prop="execTime" style="width:95%">
                <el-input v-model.trim="caseForm.execTime" placeholder="请输入数字" @input="changeInput">
                  <template slot="append">分钟</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="关联需求">
                <el-select
                  v-model="caseForm.requirementId"
                  placeholder="请输入需求名称"
                  clearable
                  filterable
                  multiple
                  remote
                  :remote-method="getRequirementList"
                  :loading="requireLoading"
                  class="requireSelect"
                  @focus="setOptionWidth"
                  @change="changeInput"
                >
                  <el-option v-for="ele in requirementList" :key="ele.id" :value="ele.id" :label="ele.code+' '+ele.name" :style="{ width: selectOptionWidth }" :title="ele.name">
                    {{ `${ele.code}   ${ele.name}` }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="编号">
                <el-input v-model="caseForm.caseKey" disabled style="width:95%" placeholder="请输入英文标识" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本">
                <div class="versionBackground">
                  {{ caseForm.version || "--" }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row />
          <el-row>
            <el-form-item label="附件" prop="files" class="fileLoad">
              <div key="id">
                <vone-upload ref="caseUploadFile" storage-type="LOCAL" biz-type="TEST_PRODUCT_CASE_FILE_UPLOAD" :files-data="fileList" @change="changeInput" />
              </div>

            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="测试意图" prop="intent">
              <vone-upload ref="intentFilesUploadFile" style="margin-bottom:10px" storage-type="LOCAL" biz-type="TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD" :files-data="intentFiles" @change="changeInput" />
              <el-input v-model="caseForm.intent" placeholder="请输入测试意图" type="textarea" :autosize="{ minRows: 2}" maxlength="300" show-word-limit @change="changeInput" />
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="标签" prop="tabInfo">
              <tagSelect v-model="caseForm.tabInfo" @getVailtab="getVailtab" @resetTab="resetTab" @change="changeInput" />
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="前置条件" prop="prerequisite">
              <vone-upload ref="prerequisiteFilesUploadFile" style="margin-bottom:10px" storage-type="LOCAL" biz-type="TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD" :files-data="prerequisiteFiles" @change="changeInput" />
              <el-input v-model="caseForm.prerequisite" placeholder="请输入前置条件" type="textarea" :autosize="{ minRows: 2}" maxlength="300" show-word-limit @input="changeInput" />
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item prop="stepType" style="position:relative;">
              <template slot="label">
                <span>测试步骤：</span>
                <div class="basicBox">
                  <i v-if="caseForm.stepType === 'text'" class="el-icon-document basicIcon" style="color: var(--main-theme-color,#3e7bfa)" />
                  <i v-else class="iconfont el-icon-application-view-list" style="color: #37cdde" />
                  <div class="basicText">
                    {{ caseForm.stepType === "text" ? "文本描述" : "步骤描述" }}
                  </div>
                  <i class="el-icon-caret-bottom basicIcon" />
                  <el-select v-model="caseForm.stepType" placeholder="请选择更改类型" class="select" popper-class="typeSelect">
                    <el-option v-for="item in stepList" :key="item.value" :label="item.label" :value="item.value">
                      <div class="selectItem">
                        <div class="selectLabel">
                          <i :class="item.icon" :style="{ color: item.color }" />
                          <span>{{ item.label }}</span>
                        </div>
                        <div class="selectDesc" v-html="item.desc" />
                      </div>
                    </el-option>
                  </el-select>
                </div>
              </template>
              <vone-upload ref="testStepFilesUploadFile" style="margin-bottom:10px" storage-type="LOCAL" :disabled="dialogType==='detail'" biz-type="TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD" :files-data="testStepFiles" @change="changeInput" />
              <el-input v-if="caseForm.stepType === 'text'" key="text" v-model="caseForm.testStep" placeholder="请输入描述" type="textarea" :autosize="{ minRows: 2}" maxlength="800" show-word-limit @input="changeInput" />

              <template v-else>
                <el-table ref="stepTable" class="vone-table stepTable" :data="stepData">
                  <el-table-column label="步骤" class-name="my-cell">
                    <template v-slot="{ row }">
                      <el-input v-model="row.caseStepDes" placeholder="请输入步骤" class="stepList" type="textarea" maxlength="800" show-word-limit :autosize="{ minRows: 2}" @input="changeInput" />
                    </template>
                  </el-table-column>
                  <el-table-column label="预期" class-name="my-cell">
                    <template v-slot="{ row }">
                      <el-input v-model="row.expectResult" placeholder="请输入预期" class="stepList" type="textarea" maxlength="800" show-word-limit :autosize="{ minRows: 2}" @input="changeInput" />
                    </template>
                  </el-table-column>

                </el-table>

              </template>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.stepType=='text'">
            <el-form-item label="预期结果" prop="expectedResult">
              <el-input v-model="caseForm.expectedResult" placeholder="请输入预期结果" type="textarea" :autosize="{ minRows: 2}" maxlength="800" show-word-limit @input="changeInput" />
            </el-form-item>
          </el-row>
        </div>

      </el-form>
    </div>

  </el-drawer>
</template>

<script>
import storage from 'store'

import { getTreeByLibrary, getFiveRequirement, productCaseDetail } from '@/api/vone/testmanage/case'

import _, { debounce } from 'lodash'
import Sortable from 'sortablejs'
import tagSelect from '@/views/vone/test/components/tag-select.vue'
// 重置表单属性
const defaultForm = () => {
  return {
    version: 'v1.0',
    treeId: '',
    name: '', // 用例名称
    caseKey: '', // 用例标识
    leadingBy: '', // 负责人
    requirementId: [],
    intent: '', // 测试意图
    prerequisite: '', // 前置条件
    stepType: 'text', // 步骤类型 text/subclause
    testStep: '', // 测试步骤
    expectedResult: '', // 预期结果
    negative: 'true', // 用例性质
    priority: null, // 用例级别
    tabInfo: [],
    files: [],
    intentFiles: [], // 意图附件
    prerequisiteFiles: [], // 条件
    testStepFiles: [] // 描述
  }
}
export default {
  components: { tagSelect },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    libraryId: {
      type: String,
      default: ''
    },
    // 弹窗类型
    dialogType: {
      type: String,
      default: 'add'
    },
    // 选中的用例数据
    // infoData: {
    //   type: Object,
    //   default: () => ({})
    // },
    treeData: {
      type: Array,
      default: () => ([])
    },
    // 选中节点数据
    nodeData: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateDepth = (rule, value, callBack) => {
      if (value && !/^[1-9]\d*$/.test(value)) {
        callBack(new Error('请输入不以0开头的数字'))
      } else {
        callBack()
      }
    }
    return {
      caseCardLoading: false,
      changeValue: true,

      noPermission: false,
      fileList: [],
      intentFiles: [], // 意图附件
      prerequisiteFiles: [],
      testStepFiles: [],
      selectOptionWidth: '',
      saveLoading: false,
      updateLoading: false, // 升级
      requireLoading: false,

      newTreeName: '', // 新增右侧分组名称
      currentTree: {
        name: '',
        id: ''
      }, // 选中节点数据
      caseForm: defaultForm(),
      addRules: {
        name: [
          { required: true, message: '请输入用例标题', trigger: 'blur' },
          {
            pattern: '^([^ ]){1,128}$',
            message: '请输入不超过128个除空格外的字符',
            trigger: 'blur'
          }
        ],
        execTime: [
          { validator: validateDepth, trigger: 'blur' }
        ],
        leadingBy: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        negative: [
          { required: true, message: '请选择用例性质', trigger: 'change' }
        ],
        priority: [
          { required: true, message: '请选择用例级别', trigger: 'change' }
        ]
      },
      validateTab: true,
      defaultUser: [], // 人员列表
      requirementList: [], // 需求列表
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      // 步骤列表
      stepList: [
        {
          label: '文本描述',
          value: 'text',
          icon: 'el-icon-document',
          color: 'var(--main-theme-color,#3e7bfa)',
          desc: `适用于简单的测试场景，没有明确测<br>试步骤。`
        },
        {
          label: '步骤描述',
          value: 'subclause',
          icon: 'iconfont el-icon-application-view-list',
          color: '#37CDDE',
          desc: `适用于需要每一个步骤进行测试的场<br>景，有明确的测试步骤、预期结果。`
        }
      ],
      // 测试步骤
      stepData: [
        {
          caseStepNum: 1,
          caseStepDes: '',
          expectResult: ''
        }
      ],
      infoData: {}
    }
  },
  watch: {
    visible(val) {
      if (val) {
        const userInfo = storage.get('user')
        this.$set(this.caseForm, 'leadingBy', userInfo.id)
        this.setCaseForm()
      }
    }
    // infoData(val, old) {
    //   if (this.visible && val?.id != old?.id) {
    //     this.setCaseForm()
    //   }
    // }
  },
  mounted() {
    this.setCaseForm()
  },
  methods: {
    changeInput(val) {
      if (val) {
        this.changeValue = false
      }
    },
    getVailtab() {
      this.validateTab = false
      this.$refs.caseFormRef.fields[6].validateMessage = '标签格式错误'
      this.$refs.caseFormRef.fields[6].validateState = 'error'
    },
    resetTab() {
      this.validateTab = true
      this.$refs.caseFormRef.fields[6].validateState = 'success'
    },
    async loadNode(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id

      const res = await getTreeByLibrary(this.$route.params.caseId || this.libraryId, rootId)
      resolve(res.data)
    },
    changeStepType() {
      this.changeValue = false

      if (this.caseForm.stepType != 'text') {
        this.$nextTick(() => {
          this.rowDrop()
        })
      }
    },
    // 行拖拽
    rowDrop() {
      var _this = this
      const tbody = document.querySelector('.left_side .stepTable  tbody')

      if (tbody) {
        Sortable.create(tbody, {
          group: 'name',
          animation: 150,
          onEnd({ newIndex, oldIndex }) {
            // 未拖拽和拖拽数据
            const dragged = _this.stepData[oldIndex]
            const unDraggedList = _this.stepData.filter((ele, i) => i !== oldIndex)
            // 排序后数据
            unDraggedList.splice(newIndex, 0, dragged)
            // 清空表格数据，重新赋值，防止拖拽后渲染错误
            _this.stepData = []
            _this.$nextTick(() => {
              _this.stepData = unDraggedList
            })
          }
        })
      }
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 编辑时数据回显
    async setCaseForm() {
      // this.getRequirementList()

      const res = await productCaseDetail(this.id)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.tabInfo = res.data.tabInfo || res.data.tabs?.map(item => item.name) || []
      this.caseForm = res.data

      this.changeValue = true

      // 查询需求列表
      // this.caseForm = { ...this.caseForm, ...this.infoData }
      this.infoData = this.caseForm
      this.newTreeName = '未分组'
      // this.caseForm.requirementId = this.caseForm.requirementId ? this.caseForm.requirementId.split(',') : ''
      this.currentTree = { id: this.caseForm.treeId }
      if (this.caseForm.echoMap && this.caseForm.echoMap.treeId) {
        this.newTreeName = this.caseForm.echoMap.treeId.name
        this.currentTree = this.caseForm.echoMap.treeId
      }
      if (this.caseForm.requirementId?.length > 0) {
        this.requirementList = [...this.caseForm.echoMap.requirement]
      }
      if (this.caseForm?.echoMap?.leadingBy) {
        this.defaultUser = [this.caseForm.echoMap.leadingBy]
      }

      // 测试步骤显示
      if (this.caseForm.stepType === 'subclause') {
        this.stepData = this.infoData.testStep && JSON.parse(this.infoData.testStep)
        this.caseForm.testStep = ''

        this.$nextTick(() => {
          this.rowDrop()
        })
      }

      this.$nextTick(() => {
        this.fileList = this.infoData.files || []
        this.intentFiles = this.infoData.intentFiles || []
        this.prerequisiteFiles = this.infoData.prerequisiteFiles || []
        this.testStepFiles = this.infoData.testStepFiles || []
        this.$refs.tree?.setCurrentKey([this.nodeData.id])
      })
    },

    // 查需求列表
    getRequirementList: debounce(async function(query, requireId) {
      try {
        this.requireLoading = true
        const params = {}
        if (this.$route.params.id) {
          params.projectId = [this.$route.params.id]
        } else {
          params.productId = this.$route.params.productInfoIds && this.$route.params.productInfoIds?.split(',')
        }
        const res = await getFiveRequirement({ ...params, name: query, parentId: requireId })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.requirementList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 分组树点击
    nodeClick(val) {
      if (val.parentId == '0') return
      this.currentTree = val
      this.newTreeName = val.name
      this.changeValue = false
      // 关闭分组弹窗
      this.$refs['groupPopover'].doClose()
    },

    // 恢复初始状态
    onClear() {
      this.stepData = [
        {
          caseStepNum: 1,
          caseStepDes: '',
          expectResult: ''
        }
      ]
    },

    onClose() {
      this.caseForm = defaultForm()

      this.$emit('update:visible', false)
      this.$nextTick(() => {
        this.onClear()
      })
    }

  }
}
</script>
<style lang="scss" scoped>
@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep(.el-drawer__header) {
  padding: 12px 16px;
}
.caseForm {
  // height: calc(100vh - 158px);
  overflow-y: auto;
  overflow-x: hidden;

  .fixhe {
    padding-top: 10px;
    background-color: #fafafa;
    .el-col-10 {
      & :hover {
        background: var(--col-hover-bg);
      }

      :deep(div .el-input--small .el-input__inner) {
        border: none;
        background: none;
      }
      :deep(.el-input__suffix) {
        display: none;
      }
      :deep(.el-input--small) {
        font-size: 14px;
      }
      // :deep(.is-disabled .el-input--small) {
      //   font-size: 14px;
      //   color: #000;
      // }
      :deep(.el-input.is-disabled .el-input__inner) {
        color: #000 !important;
        font-size: 14px;
      }
    }

    .form_title {
      padding-left: 16px;
      :deep(.el-input--small .el-input__inner) {
        width: 100%;
        border: none;
        background: none;
        font-weight: bold;
        font-size: 16px;
        padding-left: 0;
      }
      :deep(.el-form-item) {
        margin-bottom: 14px;
      }

      :deep(.el-form-item__label:before) {
        display: none;
      }

      :deep(.el-input__inner:focus) {
        background: var(--col-hover-bg);
      }
      :deep(.el-form-item__content) {
        display: inline-block;
        width: 95%;
      }
      & :hover {
        background: var(--col-hover-bg);
      }
    }
    .el-col-10 {
      :deep(.avatar) {
        img {
          width: 30px;
          height: 30px;
          margin-top: -30px;
        }
      }
      .priority {
        :deep(.iconfont) {
          font-size: 29px !important;
          height: 30px;
          margin-top: -30px;
          padding-left: 0 !important;
        }
      }
      :deep(.el-input__inner) {
        margin-left: 10px;
      }
      :deep(.el-form-item__label) {
        margin-left: 39px;
      }
      :deep(.el-form-item__label:before) {
        display: none;
      }
    }
  }
  .attribute {
    padding: 0 16px;
    .execTime {
      :deep(.el-form-item__content) {
        border: 1px solid #c1c8d6;
        border-radius: 2px;
        .el-input__inner {
          border: 0;
        }
        .el-input-group {
          border: 0;
          border-radius: 0;
        }
        .el-input-group__append {
          border: 0;
          background-color: #fff;
        }
      }
    }
    :deep(.el-popover__reference-wrapper) {
      line-height: 30px;
    }
    :deep(.referenceEmit) {
      border: 1px solid #c1c8d6;
      border-radius: 2px;
    }
  }
}

header.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    display: flex;
    align-items: center;
    gap: 0 18px;
    font-size: 14px;
  }
  .gap {
    gap: 0 18px;
  }
}
.btnBox {
  position: relative;
  display: flex;
  align-items: center;
  height: 45px;
  text-align: right;
}
// 节点树样式
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    // flex: 1 1 auto;
    display: flex;
    align-items: center;
  }

  .treeNode {
    margin-left: 6px;
    flex: 1 1 auto;
    @include ellipsis;
  }
}

.stepTable {
  :deep() {
    .el-table__cell {
      border-right: 1px solid var(--disabled-bg-color, #ebeef5);
    }
    th.el-table__cell {
      background-color: var(--bottom-bg-color);
    }
    td div.cell {
      padding: 0; // 清除单元格内容的padding
    }
    tr:hover {
      .el-textarea__inner {
        background-color: var(--hover-bg-color);
      }
    }
  }
}
// 步骤测试样式
.stepList {
  :deep() {
    .el-textarea__inner {
      padding-bottom: 20px;
      border: none;
      resize: vertical;
    }
  }
}

.stepBtn {
  text-align: center;
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
  border-right: 1px solid var(--disabled-bg-color, #ebeef5);
  border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);
}
.basicBox {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 75px;
  cursor: pointer;
  .basicText {
    margin: 0 6px;
    white-space: nowrap;
  }
  .basicIcon {
    color: var(--main-font-color);
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    height: 30px;
    z-index: 99;
    width: 90px;
    cursor: pointer;
  }
}

.popoverMain {
  min-width: 300px;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}
.referenceEmit {
  display: flex;
  align-items: center;
  .groupIcon {
    margin-left: 6px;
  }
  .treeName {
    height: 30px;
    max-width: 200px;
    @include ellipsis;
  }
}

// 测试步骤选择样式
.typeSelect {
  :deep(.el-select-dropdown__list) {
    padding: 0;
  }
  .el-select-dropdown__item {
    height: auto;
    padding: 0 16px 12px;

    &:first-child {
      padding-top: 16px;
    }
    &:last-child {
      padding-bottom: 16px;
    }
    &:not(.selected) {
      color: var(--main-font-color);
    }
  }
  .selectLabel {
    display: flex;
    gap: 14px 0;
    align-items: center;
  }
  .selectDesc {
    font-weight: normal;
    line-height: 20px;
    color: var(--auxiliary-font-color);
  }
}
:deep(.level-select) {
  .el-input__prefix {
    display: flex;
    align-items: center;
  }
}
.requireSelect {
  :deep(.el-input__inner) {
    padding-right: 30px;
    @include ellipsis;
  }
}
</style>
<style lang="scss">
.typeSelect {
  .el-select-dropdown__list {
    padding: 0;
  }
}
.btnBox {
  .el-button.is-disabled {
    border-color: #e6e9f0;
  }
}
.custom-theme-dark {
  ::-webkit-scrollbar-corner,
  ::-webkit-resizer {
    color: #e6e9f0;
    background-color: #252933;
  }
}
</style>
