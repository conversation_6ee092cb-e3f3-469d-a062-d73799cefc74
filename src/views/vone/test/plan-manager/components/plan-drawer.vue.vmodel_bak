<!-- 测试计划 -->
<template>
  <div>
    <vone-drawer :title="planType=== 'add'?'新建测试计划':planType=== 'edit'?'编辑测试计划':'测试计划详情'" :visible="visible" direction="rtl" size="lg" :show-close="false" :before-close="onClose" :wrapper-closable="false">
      <!-- 主体部分 -->
      <el-row type="flex" class="ctx">
        <el-col class="leftForm">
          <el-form ref="taskFormLeft" :model="taskForm" :rules="taskRules" label-position="top">
            <el-form-item label="标题" prop="name">
              <el-input v-model="taskForm.name" placeholder="输入计划标题" :disabled="planType=== 'detail'" />
            </el-form-item>
            <el-form-item label="测试类型">
              <el-radio-group v-model="taskForm.type" :disabled="planType=== 'detail'">
                <el-radio label="system">系统测试</el-radio>
                <el-radio label="smoke">冒烟测试</el-radio>
                <el-radio label="regression">回归测试</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <vone-editor ref="editor" v-model="taskForm.description" :disabled="planType=== 'detail'" />
            </el-form-item>
            <el-form-item v-if="planType=='add'&&taskForm.libraryId">
              <el-radio-group v-model="taskForm.addCase" class="pt-radio" :disabled="planType=== 'detail'">
                <el-radio label="1" disabled class="radio-label">包含全部用例</el-radio>
                <div>覆盖本用例库全部可用用例，如果用例库有新增的用例，不会被同步到本计划中。
                </div>
                <el-radio label="2" class="radio-label">手动圈选用例</el-radio>
                <div>手动从用例库中圈选用例，如果用例库有新创建的用例，不会被同步到本计划。</div>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="taskForm.libraryId&&taskForm.addCase == 2">
              <el-card class="box-card">
                <div class="text-item">
                  <span>{{ taskForm.relatedCases.length }}</span>
                  {{ '条用例已选' }}
                  <a class="selectButton" @click="checkScope">圈选范围<i class="el-icon-right" /></a>
                </div>
              </el-card>
            </el-form-item>
            <el-form-item v-if="planType!='add'">
              <el-card class="box-card">
                <div class="text-item">
                  <span>{{ taskForm.relatedCases.length }}</span>
                  {{ '条用例已选' }}

                </div>
              </el-card>
            </el-form-item>
          </el-form>
        </el-col>
        <div class="ctx-right">
          <el-form ref="taskForm" :model="taskForm" :rules="taskRules" label-position="left">
            <el-form-item label="维护人" prop="leadingBy">
              <vone-remote-user v-model="taskForm.leadingBy" :disabled="planType=== 'detail'" />
            </el-form-item>
            <el-form-item v-if="planType=== 'add'" label="执行人" prop="execBy">
              <vone-remote-user v-model="taskForm.execBy" :disabled="planType=== 'detail'" />
            </el-form-item>
            <el-form-item label="开始时间" prop="planStime">
              <el-date-picker v-model="taskForm.planStime" type="datetime" placeholder="开始时间" :disabled="planType=== 'detail'" format="MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsStart" default-time="['9:00:00']" style="width:100%;" />
            </el-form-item>
            <el-form-item label="结束时间" prop="planEtime">
              <el-date-picker v-model="taskForm.planEtime" type="datetime" placeholder="结束时间" :disabled="planType=== 'detail'" format="MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsEnd" default-time="['18:00:00']" style="width:100%;" />
            </el-form-item>
            <el-form-item label="产品/场景用例库" prop="libraryId">
              <el-select v-model="taskForm.libraryId" placeholder="请选择" style="width: 100%" :disabled="planType!== 'add'" filterable @change="getLibrary">
                <el-option v-for="item in productList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="测试环境" prop="envId">
              <el-select v-model="taskForm.envId" filterable :disabled="planType=== 'detail'" clearable placeholder="请选择" style="width: 100%">
                <el-option v-for="item in envList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="用例执行失败必须新增缺陷" prop="bug">
              <el-switch v-model="taskForm.bug" :disabled="planType=== 'detail'" class="switchStyle" active-color="#13ce66" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-form>
        </div>
      </el-row>
      <span slot="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button v-if="planType!== 'detail'" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>

      </span>
    </vone-drawer>
    <relateCase v-model:visible="addCaseVisible" :library-id="taskForm.libraryId" :connect-tree-id="taskForm.connectTreeId" :case-list="taskForm.relatedCases" @success="saveProCases" />

  </div>
</template>

<script>
import { saveProductPlan } from '@/api/vone/testplan'
import { apiBaseDictPage } from '@/api/vone/base/dict'

import relateCase from './relate-case.vue'
import { getAllConnectByLibraryId, queryPlanById, getLibrary, updateProductPlan, getOne } from '@/api/vone/testplan'
import { findProductCaseTreeByLibrary } from '@/api/vone/testmanage/plan'

export default {
  components: { relateCase },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    planType: {
      type: String,
      default: 'add'
    },
    id: {
      type: String,
      default: ''
    },
    // 测试计划数据
    planData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    // const validateDescription = (rule, value, callBack) => {
    //   const isHtml = /<\/?.+?\/?>/g
    //   const isImg = /<img.*?(?:>|\/>)/gi
    //   const textLength = this.$refs.editor.$el.innerText.replace(isHtml, '1').replace(isImg, '1').replace(/[|]*\n/, '').length
    //
    //   if (textLength >= 255) {
    //     callBack(new Error('请输入不以0开头的数字'))
    //   } else {
    //     callBack()
    //   }
    // }
    return {
      addCaseVisible: false,
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.taskForm.planEtime) {
            return (
              time.getTime() < Date.now() - 8.64e7 ||
              time.getTime() > new Date(this.taskForm.planEtime).getTime()
            )
          }
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          return (
            time.getTime() < Date.now() - 8.64e7 ||
            time.getTime() < new Date(this.taskForm.planStime).getTime()
          )
        }
      },
      saveLoading: false,
      taskForm: {
        bug: true,
        addCase: '2',
        relatedCases: [], // 已关联用例
        connectTreeId: [], // 已关联用例树id
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        systemId: '',
        envId: '',
        execBy: ''
      },
      statusList: [
        {
          stateId: '0',
          label: '未开始'
        },
        {
          stateId: '1',
          label: '进行中'
        },
        {
          stateId: '2',
          label: '已完成'
        }
      ],
      issuuTypeList: [
        {
          id: 1,
          label: '测试计划'
        },
        {
          id: 2,
          label: '开发计划'
        },
        {
          id: 3,
          label: '里程碑'
        }
      ],
      originalTree: [], // 原始树数据
      issuuTypeMap: {},
      taskRules: {
        name: [
          {
            required: true,
            message: '请输入标题'
          },
          {
            pattern: '^[^ ]+$',
            message: '不能输入空格'
          },
          {

            pattern: '^([^ ]){1,128}$',
            message: '请输入不超过128个除空格外的字符',
            trigger: 'blur'
          }

        ],
        leadingBy: [{ required: true, message: '请选择维护人', trigger: 'change' }],
        libraryId: [{ required: true, message: '请选择产品/场景用例库', trigger: 'change' }],
        planStime: [{ required: true, message: '请选择计划开始时间', trigger: 'blur' }],
        planEtime: [{ required: true, message: '请选择计划结束时间', trigger: 'blur' }],
        description: [
          { required: false, message: '请输入描述', trigger: 'change' },
          // { validator: validateDescription, trigger: 'change' }
          { max: 255, message: '描述长度限制在255字符以内', trigger: 'change' }
        ]
      },
      envList: [], // 环境列表
      productList: [] // 产品列表

    }
  },
  computed: {

  },

  mounted() {
    if (this.visible) {
      this.getEnvList()
      this.getAllProductList()
      if (this.planType !== 'add') {
        this.getdetail()
      } else {
        this.$set(this.taskForm, 'addCase', '2')
      }
    }
  },
  methods: {
    getLibrary() {
      this.taskForm.relatedCases = []
    },
    eventDisposalRangeChange(value) {
      const isHtml = /<\/?.+?\/?>/g
      const isImg = /<img.*?(?:>|\/>)/gi
      const textLength = this.$refs.editor.$el.innerText.replace(isHtml, '1').replace(isImg, '1').replace(/[|]*\n/, '').length

      if (textLength >= 1000) {
        this.$refs.taskFormLeft.validateField(['description'])
      } else {
        this.$refs.taskFormLeft.clearValidate(['description'])
      }
    },
    async getdetail() {
      const res = this.type !== 'fileTree' ? await queryPlanById(this.id) : await getOne(this.id)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.taskForm = res.data
      if (res.data.echoMap && res.data.echoMap.testProductCase) {
        this.taskForm.relatedCases = res.data.echoMap.testProductCase.map(v => v.testcaseId || v.id)
      }
    },
    // 选择用例
    checkScope() {
      this.addCaseVisible = true
    },
    // 查询环境列表
    async getEnvList() {
      const params = {
        current: 1,
        size: 9999,
        extra: {},
        model: { type: 'ENVIRONMENT' }
      }
      const res = await apiBaseDictPage(params)
      if (res.isSuccess) {
        this.envList = res.data.records
      }
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await getLibrary()
      if (res.isSuccess) {
        this.productList = res.data
      }
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.taskFormLeft.resetFields()
      this.$refs.taskForm.resetFields()
      this.taskForm = {
        relatedCases: [],
        connectTreeId: [],
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        envId: ''
      }
    },
    // 保存选择用例
    saveProCases({ caseIds, treeMap }) {
      this.taskForm.relatedCases = caseIds
      this.taskForm.connectTreeId = []
      this.taskForm.caseAndTreeReturnVo = treeMap
    },
    // 保存
    async saveInfo() {
      try {
        await this.$refs['taskFormLeft'].validate()
        await this.$refs['taskForm'].validate()
        // 查询关联用例
        this.getConnectCase()
      } catch (error) {
        return false
      }
    },
    async getConnectCase() {
      this.saveLoading = true
      let caseIds = []
      // 默认全选用例
      if (this.taskForm.addCase == 1) {
        // 查询所有用例id
        const res = await getAllConnectByLibraryId(this.taskForm.libraryId)
        if (res.isSuccess) {
          caseIds = res.data
        }
        // 查询所有分组树id
        const treeRes = await findProductCaseTreeByLibrary(this.taskForm.libraryId)
        if (treeRes.isSuccess) {
          // this.taskForm.connectTreeId = treeRes.data.map(item => item.id)
          this.taskForm.caseAndTreeReturnVo = treeRes.data
        }
      } else {
        // 手动选择用例
        caseIds = this.taskForm.relatedCases
      }
      this.planType === 'add' ? this.addSave(caseIds) : this.editSave()
    },
    // 新建保存
    async addSave(caseIds) {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()

        const params = {
          ...this.taskForm,
          treeId: this.planData.id,
          rateProgress: 0,
          caseId: caseIds,
          connectTreeId: []
        }

        const res = await saveProductPlan(params)
        if (res.isSuccess) {
          this.$message.success('新建成功')
          this.$emit('success')
          this.saveLoading = false
          this.onClose()
        }
      } catch (error) {
        this.saveLoading = false
        return
      }
    },

    // 编辑保存
    async editSave() {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()
        // 已关联用例id
        const params = {
          treeId: this.planData.id,
          id: this.taskForm.id,
          delay: this.taskForm.delay,
          planKey: this.planData.planKey,
          description: this.taskForm.description,
          leadingBy: this.taskForm.leadingBy,
          name: this.taskForm.name,
          planEtime: this.taskForm.planEtime,
          planStime: this.taskForm.planStime,
          rateProgress: this.planData.rateProgress ?? 0,
          stateId: this.taskForm.stateId,
          type: this.taskForm.type,
          envId: this.taskForm.envId,
          bug: this.taskForm.bug,
          libraryId: this.taskForm.libraryId
        }

        const res = await updateProductPlan(params)
        this.saveLoading = false
        if (res.isSuccess) {
          this.$message.success('修改成功')
          this.$emit('success')
          this.saveLoading = false
          this.onClose()
        }
      } catch (e) {
        this.saveLoading = false
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.header {
  display: flex;
  align-items: center;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #202d40;
  }
}

.switchStyle :deep() {
  .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  .el-switch__label--left {
    z-index: 9;
    left: 18px;
  }
  .el-switch__label--right {
    z-index: 9;
    left: -5px;
  }
  .el-switch__label.is-active {
    display: block;
  }
  .el-switch__core {
    width: 54px !important;
  }
  .el-switch__label {
    width: 54px !important;
  }
}

:deep() {
  .w-e-text-container {
    min-height: 240px;
  }
}
:deep() {
  .el-drawer__body {
    padding: 0px;
  }
}
.ctx {
  height: calc(100vh - 108px);
  > div {
    overflow: auto;
  }
  .leftForm {
    padding: 16px;
  }
}
.ctx-right {
  border-left: 1px solid #dcdfe6;
  width: 400px;
  padding: 16px;
  background-color: #fafafa;
}
.box-card {
  border: 1px solid rgb(201, 207, 215);
  span {
    font-weight: bold;
    padding-right: 4px;
  }
  .selectButton {
    padding-left: 20px;
    font-weight: bold;
    color: rgb(0, 102, 255);
    cursor: pointer;
    opacity: 0.9;
    i {
      vertical-align: center;
      font-size: 16px;
      font-weight: bold;
      color: rgb(0, 102, 255);
      cursor: pointer;
      opacity: 0.9;
    }
  }
}
.basicBox {
  cursor: pointer;
  position: relative;
  .basicIcon {
    float: left;
    color: #2dbcff;
    font-size: 36px;
    margin-top: 8px;
  }
  .basicRight {
    float: left;
    margin-left: 10px;
    .basicText {
      font-size: 12px;
      color: #ccc;
      margin-top: -12px;
    }
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 10px;
    left: 0;
    height: 40px;
    z-index: 99;
    cursor: pointer;
  }
  .basic-time {
    position: relative;
    .basic-date {
      position: absolute;
      top: 4px;
      left: 0;
      height: 40px;
      z-index: 99;
      cursor: pointer;
    }
  }
}
.tab {
  :deep(.el-input__suffix-inner) {
    display: none;
  }
}
:deep(.el-dialog__body) {
  .el-form-item {
    margin-bottom: 10px;
  }
}
.r_body {
  border-left: 1px solid #dcdfe6;
  height: 100%;
  padding-right: 15px;
  overflow-y: auto;
}
.empty-senior {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  i {
    margin-right: 10px;
  }
}
:deep(.w-e-toolbar .w-e-menu) {
  width: 25px;
}
:deep(.w-e-text-container) {
  height: 240px;
}

.custom-theme-dark {
  .box-card {
    border-color: #495266;
  }
  .ctx-right {
    border-left-color: #495266;
    background-color: #252933;
  }
  .footer {
    border-top-color: #495266;
    background-color: #252933;
  }
  .r_body {
    border-color: #495266;
  }
}
</style>
<style lang="scss" scoped>
.pt-radio {
  .el-radio {
    position: relative;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    height: 16px;
    cursor: pointer;
    color: rgb(32, 45, 64);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  }
  .el-radio__inner {
    border: 1px solid rgb(201, 207, 215);
  }
  div {
    font-size: 13px;
    line-height: 16px;
    padding-left: 24px;
    color: rgb(145, 153, 163);
    margin: 8px 0px 16px;
  }
}
</style>
