<template>
  <div>

    <el-dialog
      width="78%"
      top="10vh"
      :visible="visible"
      :before-close="onClose"
      height="70%"
      :close-on-click-modal="false"
      v-on="$listeners"
    >
      <template slot="title">
        <span style="margin-right:40px">关联用例</span>
        <el-switch
          v-model="status"
          active-text="展示全部树级"
          @change="switchTreeExpend"
        />
      </template>
      <el-row>
        <el-col v-loading="treeLoading" :span="8" class="elStyle l_body custom-tree">
          <vone-tree
            ref="tree"
            :data="dataTree"
            height="500px"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            highlight-current
            :props="defaultProps"
            :filter-node-method="filterNode"
            @node-click="treeCaseClick"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span>
                <svg class="icon iconCls" aria-hidden="true">
                  <use xlink:href="#el-application-filejia2" />
                </svg>

                <span class="treeNode">{{ data.name }}</span>
              </span>
              <span v-if=" (node.checked || node.indeterminate) && data.num" class="num">{{ data.num }}</span>
            </span>
          </vone-tree>
        </el-col>
        <el-col :span="16" class="elStyle r_body">
          <vone-search-dynamic
            card-key="testm-plan-card"
            v-model:model="formData"
            class="search_wrap"
            label-position="top"
            :is-cards="true"
            @getTableData="filterData"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用例" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入用例" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编号" prop="caseKey">
                  <el-input v-model="formData.caseKey" placeholder="请输入编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标签" prop="tabName">
                  <el-select
                    v-model="formData.tabName"
                    filterable
                    multiple
                    clearable
                    placeholder="请输入标签筛选用例"
                    remote
                    :remote-method="getTagsListByLibraryId"
                    :loading="requireLoading"
                  >
                    <el-option v-for="(item) in tagsList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="优先级" prop="priority ">
                  <vone-icon-select v-model="formData.priority" :data="prioritList" filterable clearable style="width:100%">
                    <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                      <i
                        :class="`iconfont ${item.icon}`"
                        :style="{
                          color: item.color,
                          fontSize: '16px',
                          paddingRight: '6px',
                        }"
                      />
                      {{ item.name }}
                    </el-option>
                  </vone-icon-select>
                </el-form-item>
              </el-col>
            </el-row>
          </vone-search-dynamic>
          <section v-loading="tableLoading">
            <vone-empty v-if="!rightTree.length" style="height:440px" />
            <vone-tree
              v-else
              ref="righttree"
              :data="rightTree"
              height="440px"
              node-key="id"
              show-checkbox
              :expand-on-click-node="false"
              highlight-current
              default-expand-all
              :props="defaultProps"
              :filter-node-method="filterNode"
              @check="checkChange"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="left-side">
                  <span v-if="data.type === 'case'">
                    <a @click="showInfo(data)">

                      <i v-if="data.stepType === 'subclause'" class="iconfont el-icon-application-view-list" style="color: #37cdde" />
                      <i v-else class="el-icon-document" style="color: #3e7bfa" />

                      {{ data.caseKey }}
                      <el-tooltip :content="`【${ data.caseKey}】${data.name}`" placement="top">
                        <span class="title">{{ data.name.length > 40 ? data.name.slice(0, 40) + "..." : data.name }}</span>

                      </el-tooltip>
                    </a>
                  </span>
                  <span v-else>
                    <svg class="icon iconCls" aria-hidden="true">
                      <use xlink:href="#el-application-filejia2" />
                    </svg>
                    {{ data.name }}
                  </span>
                </span>
                <span v-if="data.type == 'case' && data.priority && prioritMap[data.priority]" class="right-side">

                  <i :class="`iconfont ${prioritMap[data.priority].icon}`" :style="{ color:`${prioritMap[data.priority].color}`}" />
                  <span>
                    {{ prioritMap[data.priority].name }}
                  </span>

                </span>

              </span>
            </vone-tree>
          </section>

        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">

        <el-button @click="onClose">取消</el-button>
        <el-button :loading="tableLoading" type="primary" @click="saveInfo">确定</el-button>

      </div>
    </el-dialog>
    <caseCard
      v-if="caseParams.addVisible"
      :id="caseParams.id"
      ref="caseCard"
      v-model:visible="caseParams.addVisible"
      :dialog-type="caseParams.dialogType"
      :node-data="caseParams.menuNode"
      :type="caseParams.type"
      :library-id="libraryId"
      :info-data="caseParams.caseForm"
      v-bind="caseParams"
      @getTagsListByLibraryId="getTagsListByLibraryId"
    />
  </div>
</template>

<script>
import { debounce } from 'lodash'

import { getCaseLibraryTags } from '@/api/vone/testTab'
import {
  queryTreeCases,
  queryTreeCasesByfilter
} from '@/api/vone/testmanage/plan'
import { findProductCaseTreeOfRepository } from '@/api/vone/testplan'

import { catchErr } from '@/utils'
import { list2Tree } from '@/utils/list2Tree'
import caseCard from './case-detail.vue'

export default {
  components: {
    caseCard
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    // 用例库id
    libraryId: {
      type: String,
      default: ''
    },
    // 已关联用例
    caseList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      caseParams: {
        dialogType: 'edit',
        type: 'caseTree'
      },
      status: false,
      treeLoading: false,
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      prioritMap: {},
      formData: {
        name: '',
        caseKey: '',
        tabName: [],
        priority: ''
      },
      dataTree: [],
      tableOptions: {
        isOperation: false // 表格有操作列时设置
      },
      tableLoading: false,
      selectedId: [],
      selectedTreeId: [],
      tableData: { records: [] }, // 表格数据
      currentNode: {},
      defaultProps: {
        label: 'name'
      },
      requireLoading: false,
      tagsList: [], // 用例库标签列表
      releaseTreeMap: {}, // 已关联的用例树
      allTreeCaseMap: {}, // 当前用例库下所有分组树节点和对应用例id数据
      rightTree: []
    }
  },
  watch: {
    async visible(v) {
      if (!v) return
      this.status = true
      this.selectedId = [...this.caseList]
      this.selectedTreeId = []
      this.gettreeData()

      this.getTagsListByLibraryId()
      this.prioritMap = this.prioritList.reduce((r, v) => (r[v.code] = v) && r, {})
      if (!this.caseList.length) {
        this.rightTree = []
      }
    }

  },
  methods: {
    filterNode(value, data) {
      if (!value || value.length == 0) return true
      return value.indexOf(data.id) > -1
    },
    filterData() {
      const params = {
        tabInfo: this.formData.tabName,
        name: this.formData.name,
        caseKey: this.formData.caseKey,
        priority: this.formData.priority
      }
      // 重置或筛选项为空
      if (!Object.values(params).some(Boolean)) {
        this.$refs.tree.filter()
        this.rightTree = []
        return
      }
      this.getTreeCasesByFilter(params)
    },
    async getTreeCasesByFilter(data) {
      const params = {
        libraryId: this.libraryId,
        ...data
      }
      const res = await queryTreeCasesByfilter(params)
      if (res.isSuccess) {
        // 筛选左侧树显示
        const ids = res.data.map((v) => v.id)
        this.$refs.tree.filter(ids)
        // 设置左侧树选中
        const rootNode = this.$refs.tree.root.childNodes[0]
        this.$refs.tree.setCurrentKey(rootNode?.data?.id)
        // 查询右侧用例数据
        this.getTreeLibary(rootNode?.data)
      }
    },
    switchTreeExpend(val) {
      const nodesMap = this.$refs.tree.store.nodesMap

      for (const id in nodesMap) {
        nodesMap[id].level > 1 && (nodesMap[id].expanded = val)
      }
    },
    getTagsListByLibraryId: debounce(function(query) {
      this.requireLoading = true
      getCaseLibraryTags({
        id: this.libraryId,
        name: query
      }).then(res => {
        this.tagsList = res.data
        this.requireLoading = false
      }).catch(() => {
        this.requireLoading = false
      })
    }, 1000),
    // 查询树节点数据
    async gettreeData() {
      this.treeLoading = true
      const [res, err] = await catchErr(findProductCaseTreeOfRepository(this.libraryId))
      this.treeLoading = false
      if (err) return
      this.allTreeCaseMap = res.data.reduce((acc, cur) => {
        acc[cur.id] = {
          treeId: cur.id,
          treeName: cur.name,
          parentId: cur.parentId
        }
        return acc
      }, {})
      const tree = list2Tree(res.data, { parentKey: 'parentId' })
      this.dataTree = tree

      if (!this.caseList.length) {
        this.rightTree = []
      } else {
        // this.currentNode = tree[0]?.children[0] || {}
        // this.$refs.tree.setCurrentKey(this.currentNode.id)
        // this.getTreeLibary(this.currentNode)
      }
    },

    async getTreeLibary(data, node) {
      const params = {
        libraryId: this.libraryId,
        treeId: data.id,
        tabInfo: this.formData.tabName,
        name: this.formData.name,
        caseKey: this.formData.caseKey,
        priority: this.formData.priority
      }
      this.tableLoading = true
      const res = await queryTreeCases(params)
      this.tableLoading = false
      if (res.isSuccess) {
        this.rightTree = res.data

        this.$nextTick(() => {
          this.$refs.righttree.setCheckedKeys(this.selectedId)
        })
      }
    },
    treeCaseClick(data, node) {
      this.currentNode = data
      if (node.level <= 1) return
      this.getTreeLibary(data, node)
    },
    async checkChange(row) {
      this.currentNode = row
      const node = this.$refs.righttree.getNode(row.id)
      const checked = node.checked
      const treeChecked = this.$refs.righttree.getCheckedNodes(false, true)
      const nodeChecked = []
      treeChecked.map(v => {
        if (v.type === 'tree') {
          nodeChecked.push(v.id)
        }
      })

      const list = checked ? nodeChecked : this.releaseTreeMap[this.currentNode.id]

      list?.map(id => this.updatedIds(id, checked, this.selectedTreeId))
      this.releaseTreeMap[this.currentNode.id] = checked ? nodeChecked : []

      if (row.type == 'tree') {
        this.updateTreeChildren(row, checked)
      } else {
        this.updatedIds(row.id, checked, this.selectedId)
      }
    },
    onClose() {
      this.$emit('update:visible', false)

      this.currentNode = {}
      // 清空选中节点
      this.$refs.tree.setCheckedKeys([])
      // 清空选中节点
      this.$refs.righttree?.setCheckedKeys([])
    },
    // 保存选中用例
    async saveInfo() {
      if (this.selectedId.length === 0) {
        this.$message.warning('请选择用例添加')
        return
      }

      // 关联用例
      const caseIds = [...this.selectedId]

      const treeMap = this.selectedTreeId.map((id) => this.allTreeCaseMap[id] || {})
      this.$emit('success', { caseIds, treeMap })
      this.$emit('update:visible', false)
    },
    updateTreeChildren(node, checked) {
      node.type === 'case' && this.updatedIds(node.id, checked, this.selectedId)
      node.children?.length &&
        node.children.map((child) => {
          this.updateTreeChildren(child, checked)
        })
    },
    // 更新选中的用例id
    updatedIds(id, checked, list) {
      let index = list.indexOf(id)
      if (checked) {
        index === -1 && list.push(id)
      } else {
        while (index > -1) {
          list.splice(index, 1)
          index = list.indexOf(id)
        }
      }
    },
    showInfo(data) {
      // this.addVisible = true

      this.caseParams = {
        addVisible: true,
        id: data.id,
        infoData: data

      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-dialog__body {
    padding: 0;
  }
  .el-table::before {
    width: 0;
  }

  .pagination {
    margin: 0;
  }
}
.elStyle {
  height: 516px;
  padding: 16px 0 0 16px;
  overflow: auto;
  :deep(.el-tree-node > .el-tree-node__children) {
    min-width: min-content;
  }
}

.r_body {
  padding: 16px;
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
}
.custom-tree {
  // height: calc(100vh - 220px);

  :deep(.el-tree > .el-tree-node) {
    display: grid;
    min-width: 100%;
  }
  :deep(.el-tree-node > .el-tree-node__children) {
    display: inline;
    overflow: unset;
  }
}
// .custom-tree-node {
//   flex: 1;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   & > span {
//     flex: 1 1 auto;
//     display: flex;
//     align-items: center;
//   }
//   .iconCls {
//     margin: 0 3px;
//   }

//   .treeNode {
//     flex: 1 1 auto;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//   }
//   .num {
//     padding: 0px 8px;
//     flex: 0 0 auto;
//     margin-left: 4px;
//     min-width: 24px;
//     background-color: rgb(212, 216, 222);
//     border-radius: 8px;
//     text-align: center;
//     color: rgb(255, 255, 255);
//     height: 16px;
//     line-height: 16px;
//   }
// }

.custom-tree-node {
  flex: 1;
  display: flex;
  justify-content: space-between;
  overflow: hidden;

  .left-side{
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 0;
  }
  .right-side{
    min-width: 50px;
    text-align: right;
    background-color: var(--main-bg-color);
  }
  .iconCls {
    margin: 0 3px;
  }

}
</style>
