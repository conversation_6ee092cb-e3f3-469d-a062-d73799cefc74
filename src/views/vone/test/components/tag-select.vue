<template>
  <el-select
    id="querySelect"
    ref="searchSelect"
    filterable
    remote
    multiple
    allow-create
    clearable
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    loading-text="正在查询..."
    no-match-text="暂未查询到匹配数据,请重新输入"
    :loading="loading"
    v-bind="$attrs"
    popper-class="tag_select-popper"
    <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    @change="filterSelect"
  >
    <el-option
      v-for="(item,index) in selectData"
      :key="index"
      :label="item.name"
      :value="item.name"
    />
  </el-select>
</template>

<script>
import { debounce } from 'lodash'
import { queryCase } from '@/api/vone/testTab'
export default {
  name: 'TagSelect',
  props: {
    placeholder: {
      type: String,
      default: '输入标签名称查询'
    }
  },
  data() {
    return {
      selectData: [],
      loading: false,
      selectIconVal: null
    }
  },

  mounted() {
    this.remoteMethod()
  },
  methods: {
    filterSelect(val) {
      let show = false
      this.selectData?.map(v => {
        if (val.includes(v.id)) {
          show = true
          this.$emit('resetTab')
        }
      })

      if (show) return
      const reg = /^([a-zA-Z0-9\u4e00-\u9fa5\-.]){1,10}$/
      if (val && val.length > 0) {
        val.map(item => {
          if (item.includes('/')) {
            const tabArr = item.split('/')
            if (tabArr && tabArr.length > 3) {
              this.$emit('getVailtab')
              return
            }
            tabArr.forEach(element => {
              if (!reg.test(element)) {
              // this.$refs.searchSelect.query = val[0].substr(0, 10)
              // this.$message.warning('输入标签格式不正确,请重新输入')
                this.$emit('getVailtab')
                return
              }
            })
          } else {
            if (!reg.test(item)) {
            // this.$refs.searchSelect.query = val[0].substr(0, 10)
            // this.$message.warning('输入标签格式不正确,请重新输入')
              this.$emit('getVailtab')
              return
            }
          }
          this.$emit('resetTab')
        })
      } else {
        this.$emit('resetTab')
      }
    },
    // debounce函数去抖
    remoteMethod: debounce(function(query) {
      this.loading = true
      queryCase({ name: query }).then(res => {
        this.selectData = res.data
        this.loading = false
      })
    }, 1000)

  }
}
</script>
<style lang="scss" scoped>
	.tag_select-popper {
		padding-right: 10px;
	}

</style>
