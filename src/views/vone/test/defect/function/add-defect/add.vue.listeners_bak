<template>
  <div class="drawerBox">
    <vone-drawer title="新增缺陷" v-model:visible="visible" :before-close="onClose" :modal="false" size="lg" v-on="$listeners">

      <el-form ref="bugForm" v-loading="pageLoading" :model="bugForm" label-position="top" :rules="rules" :disabled="infoDisabled">
        <el-row>
          <el-col :span="18" class="leftForm">
            <el-form-item label="标题" prop="name">
              <el-input v-model="bugForm.name" placeholder="请输入标题" />
            </el-form-item>
            <el-tabs v-model="tabActive" @tab-click="handleClick">
              <el-tab-pane label="基本信息" name="basic">
                <el-form-item label="缺陷描述" prop="description">
                  <vone-editor v-model="bugForm.description" :preview="infoDisabled" storage-type="LOCAL" />
                </el-form-item>
                <el-form-item label="附件" prop="files" class="fileLoad">
                  <vone-upload ref="bugUploadFile" storage-type="LOCAL" biz-type="BUG_FILE_UPLOAD" :files-data="id ? bugForm.files : []" />
                </el-form-item>

              </el-tab-pane>

            </el-tabs>

          </el-col>
          <el-col :span="6" class="rightForm">
            <infoForm
              :id="id"
              ref="infoForm"
              :info-disabled="infoDisabled"
              :sprint-id="sprintId"
              @save="save"
              @changeFlow="changeFlow"
            />
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="footer">
        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
        <el-button @click="onClose">取消</el-button>
      </div>

    </vone-drawer>
  </div>
</template>

<script>

import { apiAlmBugAdd, apiAlmBugInfo } from '@/api/vone/project/defect'

import infoForm from '../infoForm.vue'

export default {
  components: {
    infoForm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    data: {
      type: Object,
      default: () => {}
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    sprintId: {
      type: String,
      default: undefined
    }
  },

  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入缺陷名称', trigger: 'blur' }]
      },
      bugForm: {

      },
      tabActive: 'basic',
      saveSplit: false,
      saveLoading: false,
      pageLoading: false

    }
  },

  mounted() {
    if (this.id) {
      this.getBugInfo()
    }
  },
  methods: {
    onClose() {
      this.splitFlag = false

      // this.$nextTick(() => {
      // this.$refs.bugFormLeft.clearValidate()
      // this.$refs.bugForm.clearValidate()
      // })
      // this.$emit('success')
      this.$emit('update:visible', false)

      this.saveSplit = false
    },

    handleClick(tab) {

    },
    async saveInfo() {
      this.$refs.bugForm.validate((valid) => {
        if (valid) {
          // 校验表单
          this.$refs.infoForm.proving()
        } else {
          return
        }
      })
    },
    async save() {
      try {
        this.saveLoading = true
        this.$set(this.bugForm, 'files', this.$refs['bugUploadFile'].uploadFiles)
        const params = {

          ...this.bugForm,
          ...this.data,
          ...this.$refs.infoForm.infoForm,
          projectId: this.$refs.infoForm.infoForm.projectId ? this.$refs.infoForm.infoForm.projectId : '0'
        }

        const res = await apiAlmBugAdd(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    async getBugInfo() {
      this.pageLoading = true
      const res = await apiAlmBugInfo(this.id)

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.bugForm = res.data

      await this.$nextTick(() => {
        this.$refs.infoForm.infoForm = res.data
      })
      await this.$refs.infoForm.getProjectInfo()

      this.pageLoading = false
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    }

  }
}
</script>
<style lang="scss" scoped>

.rightForm {
  border-left: 1px solid var(--disabled-bg-color,#ebeef5);
  padding: 12px 20px;
  height: calc(100vh - 100px);
  overflow-y: overlay;
  overflow-x: hidden;
}
.fileLoad {
  :deep(.el-form-item__content) {
    width: 90%;
    float: right;
  }
}
:deep(.drawer .vone-el-drawer__layout) {
   overflow-y: hidden;
}
.leftForm {
  padding: 12px 20px;
  height: calc(100vh - 100px);
  overflow-y: overlay;
  overflow-x: hidden;
}
.custom-theme-dark {
  .rightForm {
    border-color: #495266;
  }
}
</style>

