<template>
  <div class="drawerBox">
    <vone-drawer :title="title" v-model:visible="visible" :before-close="onClose" :modal="false" size="lg" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>

      <el-form ref="bugForm" v-loading="pageLoading" :model="bugForm" label-position="top" :rules="rules" :disabled="infoDisabled">
        <el-row>
          <el-col :span="18" class="leftForm">
            <el-form-item label="标题" prop="name">
              <el-input v-model="bugForm.name" placeholder="请输入标题" />
            </el-form-item>
            <el-tabs v-model="tabActive" @tab-click="handleClick">
              <el-tab-pane label="基本信息" name="basic">
                <el-form-item label="缺陷描述" prop="description">
                  <vone-editor v-model="bugForm.description" :preview="infoDisabled" storage-type="LOCAL" :height="300" />
                </el-form-item>
                <el-form-item label="附件" prop="files" class="fileLoad">
                  <vone-upload ref="bugUploadFile" biz-type="BUG_FILE_UPLOAD" :files-data="id ? bugForm.files : []" storage-type="LOCAL" />
                </el-form-item>

              </el-tab-pane>
              <el-tab-pane v-if="id" label="活动" name="active">
                <activeTab ref="activeTab" :form-info="bugForm" />
              </el-tab-pane>

            </el-tabs>

          </el-col>
          <el-col :span="6" class="rightForm">
            <infoForm
              :id="id"
              ref="infoForm"
              :info-disabled="infoDisabled"
              :sprint-id="sprintId"
              @save="save"
              @changeFlow="changeFlow"
            />
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="footer">
        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
        <el-button @click="onClose">取消</el-button>
      </div>

    </vone-drawer>
  </div>
</template>

<script>

import { apiAlmBugAdd, apiAlmBugInfo } from '@/api/vone/project/defect'

import infoForm from '../infoForm.vue'
import activeTab from './active.vue'
import { catchErr } from '@/utils'
export default {
  components: {
    infoForm,
    activeTab
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    sprintId: {
      type: String,
      default: undefined
    }
  },

  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入缺陷名称', trigger: 'blur' }]
      },
      bugForm: {},
      tabActive: 'basic',
      saveLoading: false,
      pageLoading: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.id && this.getBugInfo()
      }
    }
  },
  mounted() {
    this.id && this.getBugInfo()
  },
  methods: {
    onClose() {
      this.splitFlag = false
      this.$emit('update:visible', false)
    },
    handleClick(tab) {
      if (this.tabActive === 'active') {
        this.$refs.activeTab?.getDefect()
      }
    },
    async saveInfo() {
      this.$refs.bugForm.validate((valid) => {
        if (valid) {
          // 校验右侧信息，成功触发save方法
          this.$refs.infoForm.proving()
        } else {
          return
        }
      })
    },
    async save() {
      try {
        this.saveLoading = true
        this.$set(this.bugForm, 'files', this.$refs['bugUploadFile'].uploadFiles)
        const params = {
          ...this.bugForm,
          ...this.$refs.infoForm.infoForm,
          projectId: this.$refs.infoForm.infoForm.projectId ? this.$refs.infoForm.infoForm.projectId : '0'
        }
        const res = await apiAlmBugAdd(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 查询缺陷详情
    async getBugInfo() {
      this.pageLoading = true
      const [res, err] = await catchErr(apiAlmBugInfo(this.id))
      this.pageLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.bugForm = res.data
      // 设置右侧表单数据
      this.$refs.infoForm.infoForm = res.data
      this.$refs.infoForm.getProjectInfo(res.data)
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    }

  }
}
</script>
<style lang="scss" scoped>

.rightForm {
  border-left: 1px solid var(--disabled-bg-color,#ebeef5);
  padding: 12px 20px;
  height: calc(100vh - 100px);
  overflow-y: overlay;
  overflow-x: hidden;
}
.fileLoad {
  :deep(.el-form-item__content) {
    width: 90%;
    float: right;
  }
}
:deep(.drawer .vone-el-drawer__layout) {
   overflow-y: hidden;
}
.leftForm {
  padding: 12px 20px;
  height: calc(100vh - 100px);
  overflow-y: overlay;
  overflow-x: hidden;
}
.custom-theme-dark {
  .rightForm {
    border-color: #495266;
  }
}
</style>

