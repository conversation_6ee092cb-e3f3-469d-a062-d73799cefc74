<template>
  <div>
    <el-dialog
      title="导入缺陷"
      width="40%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="file">
          <el-upload
            class="upload-demo"
            action
            :limit="1"
            :auto-upload="true"
            :multiple="false"

            :http-request="httpRequest"
            :before-upload="beforeUpload"
          >
            <el-button
              slot="trigger"
              size="small"
              type="primary"
              icon="iconfont el-icon-application-file"
            >选取文件</el-button>
            <el-button
              type="text"
              class="ml-3"
              @click="getDownload"
            >下载批量上传模板</el-button>
            <div slot="tip" class="el-upload__tip">只能上传xls类型文件</div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" icon="iconfont el-icon-edit-import" :loading="upLoading" @click="submit">上传</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { downloadBugTemplate, apiAlmBugLoad } from '@/api/vone/project/defect'
import { download } from '@/utils'

export default {
  components: {
    // errorImport
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: undefined
    },
    importUrl: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      form: {},
      upLoading: false,
      hasError: false,
      dialogFormVisible: false,
      errorImportParam: { visible: false },
      rules: {
      }
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    httpRequest(file) {
      this.$set(this.form, 'file', file.file)
    },
    // 下载批量导入模板
    async getDownload() {
      try {
        download(`缺陷批量导入模版.xls`, await downloadBugTemplate(
          this.$route.params.id
        ))
      } catch (e) {
        this.$message.error('模板下载失败')
        return
      }
    },
    async submit() {
      try {
        this.upLoading = true
        const res = await apiAlmBugLoad({
          file: this.form.file
        })
        this.upLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.$emit('success')
        this.onClose()

        if (res.extra) {
          this.$emit('hasError', res)
          this.$message.warning(res.msg)
        } else {
          this.$message.success(res.msg)
        }
      } catch (e) {
        this.upLoading = false
      }
    },
    // 文件验证规则
    beforeUpload(file) {
      const isXls = file.name.substr(file.name.lastIndexOf('.') + 1) === 'xls'
      // const size = file.size / 1024 <= 5
      if (!isXls) {
        this.$message.error('只支持xls类型文件!')
      }
      // if (!size) {
      //   this.$message.error('最大支持5M的文件!')
      // }

      return isXls
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
