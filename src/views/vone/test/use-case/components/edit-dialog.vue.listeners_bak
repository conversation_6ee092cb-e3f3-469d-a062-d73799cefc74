<template>
  <el-dialog class="dialogContainer" :title="title" :visible="visible" width="40%" top="5vh" :close-on-click-modal="false" :close-on-press-escape="false" v-on="$listeners" @close="close">
    <!-- 表单部分 -->
    <el-form ref="productForm" v-loading="formLoading" :model="productForm" label-position="top" :rules="rules">
      <el-form-item label="用例库类型" prop="type">
        <el-radio-group v-model="productForm.type" :disabled="id ? true :false" @change="changeType">
          <el-radio label="product">产品用例库</el-radio>
          <el-radio label="scenario">场景用例库</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="关联产品" prop="productIds" :rules="{required: true, message: '请选择产品', trigger: 'change' }">
        <el-select
          v-model="productForm.productIds"
          multiple
          filterable
          :disabled="tagCopy"
          style="width:95%"
          placeholder="请输入产品名称"
          remote
          :remote-method="getProductList"
          :loading="requireLoading"
          @focus="setOptionWidth"
        >
          <el-option v-for="item in productList" :key="item.id+'1'" :label="item.name" :value="item.id" :title="item.name" :style="{width:selectOptionWidth}" />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="productForm.name" style="width:95%" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item v-if="tagCopy" label="标签" prop="tagId">
        <el-select
          v-model="productForm.tagId"
          filterable
          multiple
          clearable
          placeholder="请输入标签筛选用例"
          style="width:95%"
          remote
          :remote-method="getTagsListByLibraryId"
          :loading="tagLoading"
        >
          <el-option v-for="(item) in tagsList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="用例树级深度" prop="depth">
        <el-input v-model.trim="productForm.depth" style="width:95%" :disabled="depthDisabled" placeholder="请输入数字" />
        <el-tooltip style="margin-left:2px" placement="top" content="树级深度建议不超过7级">
          <i class="el-icon-warning-outline" />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="标识" prop="caseKey">
        <el-input v-model.trim="productForm.caseKey" :disabled="title=='编辑用例库'" style="width:95%" placeholder="请输入标识" />
      </el-form-item>
      <el-form-item label="负责人" prop="leadingBy">
        <vone-remote-user v-model="productForm.leadingBy" style="width:95%" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="productForm.description" style="width:95%" show-word-limit type="textarea" maxlength="200" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" size="small" :loading="saveLoading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>

import { queryFiveProduct } from '@/api/vone/product'
import { operationUseCase, getUseCaseDetail, createCaseLibraryByTag } from '@/api/vone/testmanage/case'
import _, { debounce } from 'lodash'
import storage from 'store'

import { catchErr } from '@/utils'
import { getCaseLibraryTags } from '@/api/vone/testTab'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 是否根据标签复制用例库
    tagCopy: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    }
  },
  data() {
    const validateDepth = (rule, value, callBack) => {
      if (!/^[1-9]\d*$/.test(value)) {
        callBack(new Error('请输入不以0开头的数字'))
      } else if (value < 2) {
        callBack(new Error('树级深度不能小于2'))
      } else {
        callBack()
      }
    }
    return {
      requireLoading: false,
      depthDisabled: false,
      productForm: {
        name: '',
        description: '',
        leadingBy: '',
        depth: '',
        productIds: [],
        tagId: [],
        type: 'product'
      },
      tagLoading: false,
      formLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入用例库名称', trigger: 'blur' },
          {
            pattern: '^([^ ]){1,128}$',
            message: '请输入不超过128个除空格外的字符'
          }
        ],
        type: [
          { required: true, message: '请选择用例库类型', trigger: 'change' }
        ],
        depth: [
          { required: true, validator: validateDepth, trigger: 'blur' }
        ],
        tagId: [
          { required: true, message: '请选择标签', trigger: 'change' }
        ],
        caseKey: [
          { required: true, message: '请输入标识', trigger: 'blur' }
        ]
      },
      productList: [], // 产品
      tagsList: [], // 标签列表
      selectOptionWidth: '', // 设置下拉框宽度
      saveLoading: false
    }
  },
  watch: {
    'productForm.leadingBy'(val) {
      if (val !== storage.get('user').id) {
        this.depthDisabled = true
      }
    }
  },
  mounted() {
    // 查询产品用例库
    this.getProductList()
    if (this.id) {
      this.getCaseLibraryInfo()
    } else {
      const userInfo = storage.get('user')
      this.$set(this.productForm, 'leadingBy', userInfo.id)
    }
    // 查询用例库下标签
    this.tagCopy && this.getTagsListByLibraryId()
  },
  methods: {
    // 查关联产品列表
    getProductList: debounce(async function(query) {
      try {
        this.requireLoading = true
        const res = await queryFiveProduct({ name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.productList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeType(val) {
      this.$refs.productForm.clearValidate(['productIds'])

      this.$set(this.productForm, 'productIds', [])
    },
    // 查询用例库信息
    async getCaseLibraryInfo() {
      this.formLoading = true
      const [res, err] = await catchErr(getUseCaseDetail(this.id))
      this.formLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      const result = { ...res.data }

      result['productIds'] = res.data?.echoMap?.productInfos?.map(r => r.id) || []

      this.productList = [...res.data?.echoMap?.productInfos]

      this.productForm = result
      // 清除校验
      this.$refs.productForm.resetFields()
    },
    // 查询用例库标签列表
    getTagsListByLibraryId: debounce(function(query) {
      this.tagLoading = true
      getCaseLibraryTags({ id: this.id, name: query }).then(res => {
        this.tagsList = res.data
        this.tagLoading = false
      }).catch(() => {
        this.tagLoading = false
      })
    }, 1000),
    close() {
      this.$refs.productForm.resetFields()
      this.$emit('update:visible', false)
    },
    // 保存
    async save() {
      try {
        await this.$refs.productForm.validate()
      } catch (error) {
        return
      }

      this.saveLoading = true
      // 通过标签复制用例库
      this.tagCopy ? this.copyCaseLibrary() : this.configCaseLibraryNoTag()
    },
    async configCaseLibraryNoTag() {
      const param = _.pick(this.productForm, ['id', 'leadingBy', 'name', 'productIds', 'type', 'description', 'depth', 'caseKey'])
      // 新增和修改
      const fetchMethod = this.id ? 'put' : 'post'
      const fetchMsg = this.id ? '修改成功' : '新增成功'

      const [res, err] = await catchErr(operationUseCase(param, fetchMethod))
      this.saveLoading = false
      if (err) return
      if (res.isSuccess) {
        this.$message.success(fetchMsg)
        this.close()
        this.$emit('success')
      } else {
        this.$message.warning(res.msg)
      }
    },
    // 根据标签筛选用例并新建用例库
    async copyCaseLibrary() {
      const picked = _.pick(this.productForm, ['depth', 'description', 'leadingBy', 'name', 'productId', 'productIds', 'type', 'caseKey'])
      const params = {
        ...picked,
        tabId: this.productForm.tagId.join(),
        parentId: this.productForm.id
      }
      const [res, err] = await catchErr(createCaseLibraryByTag(params))
      this.saveLoading = false
      if (err) return
      if (res.isSuccess) {
        this.$message.success('复制成功')
        this.close()
        this.$emit('success')
      }
    }
  }
}
</script>
