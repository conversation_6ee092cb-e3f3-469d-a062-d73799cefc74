<!-- 测试用例 -->
<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>测试用例</span>
      </div>
      <div class="menubox">
        <a :class="type == 'archiveTree'? 'is-active' : ''" @click="e=>changeTable('archiveTree')">已归档</a>
        <a :class="type == 'catchTree'? 'is-active' : ''" @click="e=>changeTable('catchTree')">回收站</a>
      </div>
      <el-tabs v-model="type" class="vone-tab-line">
        <el-tab-pane v-for="item in types" :key="item.key" :label="item.tab" :name="item.key" />
      </el-tabs>
      <!-- <div class="tabBtnbox">
        <el-tooltip v-if="!allTreeStatus" content="展开全部树级" placement="top">
          <i class="iconfont el-icon-application-unfold-tree" @click="openAllTree" />
        </el-tooltip>
        <el-tooltip v-else content="收起全部树级" placement="top">
          <i class="iconfont el-icon-application-fold-tree" @click="openAllTree" />
        </el-tooltip>
      </div> -->
      <div class="treebox">
        <allTree v-if="type=='caseTree'&&allTreeStatus" ref="allTree" @submit="submit" @treeDrop="treeDrop" @delNode="delNode" />
        <component
          :is="type"
          v-else-if="type!=='archiveTree' && type!=='catchTree'"
          ref="tree"
          :library-id="libraryId"
          :type="type"
          :status="status"
          :all-tree-status="allTreeStatus"
          @submit="submit"
          @success="success"
          @treeDrop="treeDrop"
          @delNode="delNode"
          @exportFile="exportTypeFile($event, 'tree')"
        />
      </div>
    </div>
    <div v-if="type=='archiveTree' &&!status" class="rightSection" :span="18">
      <!-- 查看归档记录 -->
      <archiveRecords :library-id="libraryId" />
    </div>
    <div v-else class="rightSection">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="testm-case-table"
            :model="formData"
            :table-ref="$refs['testm-case-table']"
            v-model:default-fileds="defaultFileds"
            show-basic
            v-model:extra="extraData"
            @getTableData="getTableData"
          />
        </template>
        <template v-if="type!=='archiveTree'" slot="actions">
          <el-button v-if="type!=='catchTree'&&(menuNode.parentId!=='0'&& menuNode.id!==0)" type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('testm_project_test_tree_add')" @click="openAddCard">新增</el-button>
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
            <el-dropdown-menu v-if="type=='caseTree'" slot="dropdown">
              <el-dropdown-item v-for="(item, index) in (menuNode.parentId=='0'?actions.slice(1,6):menuNode.id=='0'?actions.slice(1,6):$route.params.id?actions:actions.slice(0,7))" :key="index" :command="item.fn" :disabled="item.disabled" :icon="item.icon">
                <span>{{ item.name }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>

            <el-dropdown-menu v-else-if="type=='graftTree'" slot="dropdown">
              <el-dropdown-item v-for="(item, index) in (menuNode.parentId=='0'?actions.slice(1,6):menuNode.id=='0'?actions.slice(1,6):actions.slice(0,7))" :key="index" :command="item.fn" :icon="item.icon" :disabled="item.disabled">
                <span>{{ item.name }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>

            <el-dropdown-menu v-else slot="dropdown">
              <el-dropdown-item v-for="(item, index) in (type=='catchTree'?recoverActions:$route.params.id?actions:'')" :key="index" :command="item.fn" :icon="item.icon" :disabled="item.disabled">
                <span>{{ item.name }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>

          </el-dropdown>

        </template>
        <template slot="fliter">
          <vone-search-filter
            v-model:extra="extraData"
            :model="formData"
            v-model:default-fileds="defaultFileds"
            @getTableData="getTableData"
          />
        </template>
      </vone-search-wrapper>
      <main :style="{height: tableHeight}">
        <vxe-table
          ref="testm-case-table"
          class="vone-vxe-table draggTable"
          height="auto"
          border
          resizable
          :loading="pageLoading"
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          v-model:data="tableData.records"
          :column-config="{ minWidth:'120px' }"
          :checkbox-config="{ reserve: true }"
          :row-config="{ keyField: 'id',useKey: true }"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="50" fixed="left" align="center" />
          <vxe-column v-for="(head,index) in caseColumn" :key="index" :title="head.label" :field="head.prop" :min-width="head.width || 120">
            <template #default="scope">
              <span v-if="head.prop == 'name'">
                <a @click="editRow(scope.row)">

                  <svg v-if="scope.row.stepType === 'subclause'" class="icon" aria-hidden="true">
                    <use xlink:href="#el-icon-test-step" />
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#el-icon-test-text" />
                  </svg>

                  <span style="margin-left:4px;">{{ scope.row.caseKey+ ' '+scope.row.name }}</span>
                </a>
              </span>
              <span v-if="head.prop == 'version'">
                {{ scope.row.version }}
              </span>
              <span v-if="head.prop == 'draft'">
                <span v-if="type==='caseTree'">终稿</span>
                <vone-icon-select v-else v-model="scope.row.draft" :no-permission="!scope.row.draft||!$permission('testm_plan_usecase_eidt')" style="width:100%" @change="changeStatus(scope.row)">
                  <el-option :value="false" label="终稿" />
                  <el-option :value="true" label="草稿" />
                </vone-icon-select>
              </span>
              <span v-if="head.prop == 'priority'">
                <span v-if="scope.row.priority === 1">
                  <i class="iconfont el-icon-icon-dengji-zuidi2" style="color:#4ECF95;" />
                  最低
                </span>
                <span v-else-if="scope.row.priority === 2">
                  <i class="iconfont el-icon-icon-dengji-jiaodi2" style="color:#5ACC5E;" />
                  较低
                </span>
                <span v-else-if="scope.row.priority === 5">
                  <i class="iconfont el-icon-icon-dengji-zuigao2" style="color:#FA6A69;" />
                  最高
                </span>
                <span v-else-if="scope.row.priority === 4">
                  <i class="iconfont el-icon-icon-dengji-jiaogao2" style="color:#FA8669;" />
                  较高
                </span>
                <span v-else>
                  <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color,#3e7bfa);" />
                  普通
                </span>
              </span>
              <span v-if="head.prop == 'leadingBy'">
                <vone-user-avatar :avatar-path="getUserInfo(scope.row)?getUserInfo(scope.row).avatarPath :''" :name="getUserInfo(scope.row)?getUserInfo(scope.row).name :''" />
              </span>
            </template>
          </vxe-column>

          <vxe-column title="操作" fixed="right" align="left" width="120">
            <template #default="{ row }">
              <span v-if="type=='catchTree'" class="operation-icon-main">
                <el-tooltip class="item" content="还原到用例库未分组" placement="top">
                  <el-button
                    size="mini"
                    type="text"
                    :disabled="!$permission('testm_project_test_tree_edit')"
                    icon="iconfont el-icon-application-undo"
                    @click="reduction(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-application-delete"
                    :disabled="!$permission('testm_project_test_tree_del')"
                    @click="deleteRow(row)"
                  />
                </el-tooltip>
              </span>
              <span v-else-if="type=='archiveTree'" class="operation-icon-main">
                <el-tooltip class="item" content="撤销归档" placement="top">
                  <el-button
                    size="mini"
                    type="text"
                    :disabled="!$permission('testm_test_use_file_call')"
                    icon="el-icon-application-undo"
                    @click="reCall(row)"
                  />
                </el-tooltip>
              </span>
              <span v-else>
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button type="text" :disabled="!$permission('testm_project_test_tree_edit')" icon="iconfont el-icon-application-edit" @click="editRow(row)" />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="移入回收站" placement="top">
                  <el-button type="text" :disabled="!$permission('testm_project_test_tree_del')" icon="iconfont el-icon-application-delete" @click="deleteRow(row)" />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e(row)">
                  <el-button type="text" icon="iconfont el-icon-application-more" class="operation-dropdown" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="iconfont el-icon-application-copy" :disabled="row.draft" :command="() => allCopy(row)">
                      <span>复制</span>
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="row.draft" icon="iconfont el-icon-application-file" :command="() => doRecords(row)">
                      <span>执行记录</span>
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="row.draft" icon="iconfont el-icon-application-edit-visible" :command="() => checkHistory(row)">
                      <span>查看历史版本</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </template>
          </vxe-column>
        </vxe-table>
      </main>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    </div>
    <caseCard
      :id="rowId"
      ref="caseCard"
      v-model="addVisible"
      :dialog-type="dialogType"
      :node-data="menuNode"
      :type="type"
      :library-id="libraryId"
      :info-data="caseForm"
      @success="success"
      @getTagsListByLibraryId="getTagsListByLibraryId"
    />
    <!-- 批量修改 -->
    <allEdit v-if="editVisible" v-model="editVisible" :dialog-type="dialogType" :node-data="menuNode" :ids="ids" :edit="editType" :library-id="libraryId" @success="success" />
    <!-- 迁移 -->
    <relateCase v-model="allMoveParams.visible" :library-id="$route.params.caseId||libraryId" />
    <!-- 复制用例 -->
    <copyCaseDialog v-if="copyParam.visible" v-bind="copyParam" v-model="copyParam.visible" :library-id="libraryId" :menu-node="menuNode" @success="success" />

    <!-- 用例复用 -->
    <case-copy v-if="caseCopyParams.visible" v-bind="caseCopyParams" v-model="caseCopyParams.visible" v-model:name="caseCopyParams.name" :library-id="libraryId" @success="byCaseSave" />

    <!-- 导入用例 -->
    <importFile v-if="importParam.visible" v-bind="importParam" v-model="importParam.visible" :library-id="$route.params.caseId||libraryId" @success="saveImp" @hasError="hasError" @conflict="xmindConflict" />
    <!-- 导入用例失败 -->
    <errorImport v-if="errorImportParam.visible" v-bind="errorImportParam" v-model="errorImportParam.visible" />
    <!-- 导入冲突 -->
    <imoprtConflict v-bind="conflictParam" v-model="conflictParam.visible" @success="successConflict" />
    <!-- 文件导出 -->
    <ExportConfig v-if="exportOptions.visible" v-bind="exportOptions" :types="selectedTreeItem.type" v-model="exportOptions.visible" @success="exportFlie" />
  </div>
</template>

<script>
import Sortable, { MultiDrag } from 'sortablejs'
import { catchErr, download } from '@/utils'
import { getNoGroupCase } from '@/api/vone/testmanage'
import { caseArchive, getArchiveByLibraryId, recallArchive, testProductTreeCaseDel, updateCases2Tree, finddelCaseByIdPage, trashCase, getTrashCase, callBack, testProductTreeCasePut, copyCaseToProject, getArchiveDetail, productCaseDetail, getTreeAndCase } from '@/api/vone/testmanage/case'
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { getCaseLibraryTags } from '@/api/vone/testTab'
import { debounce } from 'lodash'
import caseCard from './case-card.vue'
import catchTree from './tree-desc.vue'
import archiveTree from './tree-desc.vue'
import caseTree from './tree.vue'
import graftTree from './tree.vue'
import allEdit from './all-edit.vue'
import caseCopy from './components/case-copy'
import copyCaseDialog from './components/copy-case-dialog.vue'

import importFile from './components/import-file.vue'
import errorImport from './components/error-import.vue'
import imoprtConflict from './components/import-conflict.vue'
import ExportConfig from './components/export-config.vue'
import archiveRecords from './archive-records.vue'
import allTree from './all-tree.vue'
import relateCase from './relate-case.vue'
import setDataMixin from '@/mixin/set-data'
// 多选拖拽
Sortable.mount(new MultiDrag())

// 清除拖拽进入节点高亮显示样式
function clearDragClass() {
  const moveList = document.querySelectorAll('.dragg2Tree')
  if (moveList.length > 0) {
    moveList.forEach(item => item.classList.remove('dragg2Tree'))
  }
}
export default {
  components: {
    relateCase,
    archiveRecords,
    catchTree,
    archiveTree,
    graftTree,
    caseTree,
    caseCard,
    allTree,
    copyCaseDialog,
    importFile,
    errorImport,
    imoprtConflict,
    allEdit,
    caseCopy,
    ExportConfig
  },
  mixins: [setDataMixin],
  props: {
    libraryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '用例',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入用例'
        },
        {
          key: 'caseKey',
          name: '编号',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入编号'
        },
        {
          key: 'tabName',
          name: '标签',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择标签'
        },
        {
          key: 'priority',
          name: '优先级',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择优先级'
        }
      ],
      caseColumn: [
        {
          label: '用例',
          prop: 'name',
          width: '330px',
          showTooltip: true
        },
        {
          label: '版本',
          prop: 'version'
        },
        {
          label: '状态',
          prop: 'draft',
          width: '100px'
        },
        {
          label: '优先级',
          prop: 'priority',
          width: '100px'
        },
        {
          label: '维护人',
          prop: 'leadingBy',
          width: '150px'
        }
      ],
      caseIds: [], // 用例晋级时需要的caseids
      types: [
        {
          key: 'caseTree',
          tab: '用例树'
        },
        {
          key: 'graftTree',
          tab: '草稿箱'
        }
        // {
        //   key: 'archiveTree',
        //   tab: '已归档',
        //   disabled: !this.$permission('code_user_list')
        // }, {
        //   key: 'catchTree',
        //   tab: '回收站',
        //   disabled: !this.$permission('code_merge_view')
        // }
      ],
      status: false,
      allTreeStatus: false,
      type: 'caseTree',
      editType: '',
      rowId: '',
      ids: [], // 选中的用例id
      tagsList: [], // 用例库标签列表
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      selecteTableData: [],
      importParam: { visible: false }, // 用户导入
      errorImportParam: { visible: false },
      conflictParam: { visible: false },
      allMoveParams: { visible: false }, // 批量迁移
      exportLoading: false,
      tableData: {
        page: {},
        tree: [],
        records: []
      },
      recoverActions: [
        {
          name: '批量删除',

          fn: this.deleteTableSelect,
          disabled: !this.$permission('testm_product_tree_case_del')
        }
      ],
      actions: [
        {
          name: '批量修改',

          fn: this.allEdit,
          disabled: !this.$permission('testm_project_test_tree_edit')
        },
        {
          name: '批量复制',
          fn: this.allCopy
          // disabled: !this.$permission('testm_case_copy')
        },
        {
          name: '批量迁移',
          fn: this.allMove,
          disabled: !this.$permission('project_case_batch_move')
        },
        {
          name: '批量删除',
          fn: this.deleteTableSelect,
          disabled: !this.$permission('testm_project_test_tree_del')
        },
        {
          name: '归档',
          icon: 'iconfont el-icon-application-archive',
          fn: this.caseFile,
          disabled: !this.$permission('project_case_manage_file')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort
          // disabled: !this.$permission('testm_tree_case_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          disabled: !this.$permission('testm_project_test_tree_export'),
          fn: () => this.exportTypeFile(this.menuNode, 'more')
        },
        {
          name: '用例复用',
          icon: 'iconfont el-icon-application-archive',
          fn: this.caseCopy,
          disabled: !this.$permission('testm_project_test_tree_reuse')
        },
        {
          name: '用例晋级',
          icon: 'iconfont el-icon-application-archive',
          fn: this.caseRise,
          disabled: !this.$permission('project_case_promoted')
        }
      ],
      requireLoading: false,
      caseForm: {}, // 用例数据
      editVisible: false, // 批量修改
      dialogType: '', // 新增或编辑用例
      addVisible: false, // 新增用例卡片显示
      menuNode: {}, // 选中节点数据
      formData: {},
      pageLoading: false,
      copyParam: {
        visible: false
      },
      caseCopyParams: {
        visible: false
      },
      exportOptions: {
        visible: false
      },
      selectedTreeItem: {
        type: '',
        id: ''
      },
      sorTree: []
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getTableData()
    this.getTagsListByLibraryId()
    this.rowDrop()
    this.initCaseTreeSetting()
    this.initArchiveTreeSetting()
    this.setData(this.defaultFileds, 'priority', this.prioritList)
  },
  methods: {
    changeTable(type) {
      this.type = type
      if (type == 'catchTree') {
        this.submit()
      }
    },
    openAllTree() {
      this.allTreeStatus = !this.allTreeStatus
    },
    // 复制标题到剪贴板
    titleCopy(row) {
      const _this = this
      this.$copyText(`${row.caseKey} ${row.name}`).then(function(e) {
        _this.$message.success(' 已复制到剪贴板！')
      }, function(e) {
        _this.$message.warning(' 该浏览器不支持自动复制')
      })
    },
    // 删除树级清空用例列表
    delNode() {
      this.$set(this.tableData, 'records', [])
    },
    // 显示树级设置方法
    initTreeSettings(tabEl, box) {
      let timer
      const showBox = (box) => {
        if (!box) return
        clearTimeout(timer)
        box.style.opacity = 1
        box.style.visibility = 'visible'
      }
      const hideBox = (box) => {
        if (!box) return
        timer = setTimeout(() => {
          box.style.opacity = 0
          box.style.visibility = 'hidden'
        }, 200)
      }
      // 用例树级设置
      tabEl?.addEventListener('mouseenter', () => showBox(box))
      tabEl?.addEventListener('mouseleave', () => hideBox(box))
      box?.addEventListener('mouseenter', () => showBox(box))
      box?.addEventListener('mouseleave', () => hideBox(box))
    },
    initCaseTreeSetting() {
      this.$nextTick(() => {
        const caseTab = document.querySelector('#tab-caseTree')
        const caseTreeBox = document.querySelector('.caseTreeBox')
        this.initTreeSettings(caseTab, caseTreeBox)
      })
    },
    initArchiveTreeSetting() {
      this.$nextTick(() => {
        const archiveTab = document.querySelector('#tab-archiveTree')
        const archiveBox = document.querySelector('.archiveBox')
        this.initTreeSettings(archiveTab, archiveBox)
      })
    },
    // 导入用例保存
    saveImp() {
      if (this.allTreeStatus) {
        this.$refs.allTree.gettreeData()
        this.$refs.allTree.getCount(this.menuNode)
      } else {
        this.$refs.tree.getCount(this.menuNode)
        this.$refs.tree.refreshNode(this.menuNode.id)
      }
      this.getTableData()
    },
    // 根据树级查用例表格
    submit(data) {
      // 清除选中效果及数据
      this.clearDragData()
      this.addVisible = false
      this.menuNode = data || {}
      this.$nextTick(() => {
        this.getTableData()
      })
    },
    // 用例复用保存
    async byCaseSave(caseids, name, treeid) {
      const params = {
        ids: this.caseCopyParams.name == '用例复用' ? caseids : this.caseIds,
        libraryId: this.caseCopyParams.name == '用例复用' ? this.libraryId : name,
        treeId: this.caseCopyParams.name == '用例复用' ? this.formData.treeId : treeid
      }
      const res = await copyCaseToProject(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.getTableData()
    },
    allMove() {
      this.allMoveParams = { visible: true, name: '批量迁移' }
    },
    // 复用
    caseCopy() {
      this.caseCopyParams = { visible: true, name: '用例复用' }
    },
    // 晋级
    caseRise() {
      this.selecteTableData = this.getVxeTableSelectData('testm-case-table')
      if (this.selecteTableData.length === 0) {
        this.$message.warning('请勾选要晋级的用例')
        return
      }
      this.caseIds = this.selecteTableData.map(item => item.id)
      this.caseCopyParams = { visible: true, name: '用例晋级' }
    },
    async changeStatus(row) {
      const res = await testProductTreeCasePut(row)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.success()
      }
    },
    // 还原操作
    async reCall(row) {
      const [res, err] = await catchErr(recallArchive([row.id]))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.getTableData()
    },
    // 用例归档
    async caseFile() {
      this.selecteTableData = this.getVxeTableSelectData('testm-case-table')
      if (this.selecteTableData.length === 0) {
        this.$message.warning('请勾选要归档的信息')
        return
      }
      await this.$confirm(`是否进行归档`, '提示', {
        type: 'warning'
      })
      const deleteArr = this.selecteTableData.map(item => item.id)
      const res = await caseArchive(deleteArr)
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.$message.success(res.msg)
      if (this.allTreeStatus) {
        this.$refs.allTree.getCount(this.menuNode)
      } else {
        this.$refs.tree.getCount(this.menuNode)
      }
      this.getTableData()
    },
    // 删除节点
    delSave(node) {
      this.$refs.tree.refreshNode(node.node.parentId)
    },
    // // 查询用例库标签列表
    getTagsListByLibraryId: debounce(function(query) {
      this.queryTagList(query)
    }, 1000),
    visibleChange() {
      this.queryTagList()
    },
    queryTagList(name) {
      this.requireLoading = true
      getCaseLibraryTags({ id: this.$route.params.caseId || this.libraryId, name: name }).then(res => {
        this.tagsList = res.data
        this.setData(this.defaultFileds, 'tabName', res.data)
        this.requireLoading = false
      })
    },
    //  表格上的还原
    async reduction(row) {
      const res = await callBack([row.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.getTableData()
    },
    allEdit() {
      this.selecteTableData = this.getVxeTableSelectData('testm-case-table')
      if (this.selecteTableData.length > 0) {
        this.ids = this.selecteTableData.map(v => v.id)
        this.editType = ''
        this.editVisible = true
      } else {
        this.$message('请选择用例')
      }
    },
    // 树拖拽
    async treeDrop(val) {
      const that = this
      this.className = val
      const treeContent = val
      if (treeContent?.length > 0) {
        for (let i = 0; i < treeContent.length; i++) {
          var newsortable = new Sortable(treeContent[i], {
            group: { name: 'caseList', pull: true, push: false },
            animation: 150,
            swapThreshold: 0.65,
            async onAdd(evt) {
              const { oldIndicies, oldIndex, to, items } = evt
              // 清除设置的拖拽进入节点高亮样式
              clearDragClass()
              // 拖入的节点
              const insertNode = to.__vue__?.node?.data ?? {}
              let params = []
              const treeId = insertNode.id
              // 多选节点拖入
              if (oldIndicies.length > 0) {
                // 获取右侧表格数据
                that.dragItems = oldIndicies.sort((a, b) => a.index - b.index).reduce((acc, item) => {
                  const cur = that.tableData.records[item.index]
                  params.push(cur.id)
                  acc[item.index] = cur
                  return acc
                }, {})
                // 删除拖入的表格trDOM
                items.forEach(item => {
                  to.contains(item) && to.removeChild(item)
                })
              } else {
                // 拖入树菜单，添加表格行数据
                // 单条表格数据
                const single = that.tableData.records[oldIndex]

                params = [single.id]
              }
              if (to.__vue__?.node.level <= 1) {
                that.$message.warning('根节点不能添加')
                that.tableData = {
                  records: []
                }
                that.getTableData()
                return
              }
              if (to.__vue__?.node.data.id == 0) {
                that.$message.warning('未分组不能添加')
                that.tableData = {
                  records: []
                }
                that.getTableData()
                return
              }
              // 用例导入左侧分组
              await updateCases2Tree(treeId, params).then(res => {
                if (res.isSuccess) {
                  that.$message.success('用例拖入成功')
                  // that.reload()
                  // 刷新表格
                  that.$nextTick(() => {
                    that.getTableData()
                  })
                  // 跟新数量
                  if (that.allTreeStatus) {
                    that.$refs.allTree.getCount(that.menuNode)
                  } else {
                    that.$refs.tree.getCount(that.menuNode)
                  }
                  that.sorTree.push(newsortable)
                } else {
                  that.$message.error(res.msg)
                }
              })
              that.clearDragData()
            }
          })
        }
      }
    },
    destroy(val) {
      this.sorTree.map(e => {
        e.destroy()
      })
    },
    // 表格行拖拽
    rowDrop() {
      const tbody = document.querySelector('.draggTable .vxe-table--body-wrapper tbody')
      var that = this
      Sortable.create(tbody, {
        group: 'caseList',
        multiDrag: true,
        selectedClass: 'selectedDrag', // 多选选中类名
        handle: '.handleDrag',
        animation: 150,
        avoidImplicitDeselect: true, // 外部点击不能取消选中
        removeCloneOnHide: true,
        dragClass: 'draggingRow', // 拖动中的dom类名
        onChoose: function(/** Event*/evt) {
          that.$nextTick(() => {
            that.sorTree = []
            var node
            if (that.type == 'caseTree' && that.allTreeStatus) {
              node = that.$refs['allTree']?.getTreeNode()
            } else {
              node = that.$refs['tree']?.getTreeNode()
            }
            that.treeDrop(node)
          })
          // evt.oldIndex
        },
        // 拖拽移动回调
        onMove: function(evt) {
          // 清除拖拽进入节点高亮样式
          clearDragClass()
          evt.dragged.style.display = 'none'
          // 添加拖入样式
          evt.to.classList.add('dragg2Tree')
        },
        // 拖拽结束回调，处理添加到左侧分组业务逻辑
        onEnd: (evt) => {
          const { from, to, item, items, newIndex, oldIndicies } = evt
          // 清除添加的样式
          evt.item.style = ''
          // 右侧表格拖入左侧树
          if (from != to) {
            // 单项拖拽
            if (items.length === 0) {
              // 删除拖入的表格trDOM
              to.contains(item) && to.removeChild(item)
            } else {
              // 多项拖拽，删除拖入的表格trDOM
              items.forEach(item => {
                to.contains(item) && to.removeChild(item)
              })
            }
          } else {
            // 拖拽的数据索引
            const oldOrders = oldIndicies.map(ele => ele.index)
            // 未拖拽和拖拽数据
            const draggedList = []
            const unDraggedList = []
            this.tableData.records.filter((ele, i) => {
              oldOrders.includes(i) ? draggedList.push(ele) : unDraggedList.push(ele)
            })
            // 排序后数据
            unDraggedList.splice(newIndex, 0, ...draggedList)
            this.tableData.records = unDraggedList

            // 设置拖拽后默认选中
            this.$nextTick(() => {
              draggedList.forEach(ele => {
                this.$refs['testm-case-table'].setCheckboxRow(ele, true)
              })
            })
          }
          that.$nextTick(() => {
            var node
            if (that.type == 'caseTree' && that.allTreeStatus) {
              node = that.$refs['allTree']?.getTreeNode()
            } else {
              node = that.$refs['tree']?.getTreeNode()
            }
            that.destroy(node)
          })
          // that.clearDragData()
        }
      })
    },
    // 清除拖拽过的数据
    async clearDragData() {
      const drageRows = document.querySelectorAll('.draggTable .vxe-table--body-wrapper tbody tr')
      // 清空选中效果
      await drageRows.forEach(item => {
        const el = item
        if (el) {
          const checkBox = el.firstChild
          const handleDrag = checkBox.querySelector('.handleDrag')
          if (handleDrag) {
            checkBox.removeChild(handleDrag)
          }
        }
      })
      drageRows.forEach(el => {
        Sortable.utils.deselect(el)
      })
      // 清除选中的值
      this.$refs['testm-case-table'].clearCheckboxRow()
    },
    // 获取正常表格数据
    async getTableData() {
      if (this.type !== 'catchTree' && Object.keys(this.menuNode).length == 0) return

      this.pageLoading = true

      this.$set(this.formData, 'libraryId', this.$route.params.caseId || this.libraryId)
      this.$set(this.formData, 'draft', this.type == 'graftTree')
      this.$set(this.formData, 'state', false)
      this.$set(this.formData, 'treeId', this.menuNode.parentId != '0' ? this.menuNode?.id : '')
      this.$set(this.formData, 'tabName', this.formData.tabName || [])

      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        extra: {
          ...this.extraData
        },
        model: {
          ...this.formData
        }
      }

      // 设置查询接口
      const fetchApi = this.type == 'catchTree' ? getTrashCase : this.type == 'archiveTree' ? getArchiveByLibraryId : this.menuNode.name == '未分组' ? getNoGroupCase : this.menuNode.parentId == '0' ? getTreeAndCase : finddelCaseByIdPage

      const res = await fetchApi(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.pageLoading = false
      // this.tableData = this.menuNode.parentId == '0' ? res.data.page : res.data

      this.$set(this, 'tableData', this.menuNode.parentId == '0' ? res.data.page : res.data)

      const data = this.menuNode.parentId == '0' ? res.data.page : res.data

      this.$refs['testm-case-table'].reloadData(data?.records)
    },
    // 批量删除
    async deleteTableSelect() {
      this.selecteTableData = this.getVxeTableSelectData('testm-case-table')
      if (this.selecteTableData.length === 0) {
        this.$message.warning('请勾选要删除的用例')
        return
      }
      let msg = '是否移入回收站'
      let fetchApi = trashCase
      if (this.type === 'catchTree') {
        msg = '删除后不可恢复，确定删除当前项吗'
        fetchApi = testProductTreeCaseDel
      }
      await this.$confirm(`${msg}`, '删除', {
        type: 'warning',
        customClass: 'delConfirm'
      })
      const deleteArr = this.selecteTableData.map(item => item.id)
      const res = await fetchApi(deleteArr)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success(res.msg)

      this.getTableData()
      if (this.type === 'catchTree') return
      if (this.allTreeStatus) {
        this.$refs.allTree.getCount(this.menuNode)
      } else {
        this.$refs.tree.getCount(this.menuNode)
      }
    },
    // 表格选择
    selectChange(value) {
      this.selecteTableData = value
      const list = []
      // 获取所有表格行
      const drageRows = document.querySelectorAll('.draggTable .vxe-table--body-wrapper tbody tr')
      // 获取表格选中的索引
      this.tableData.records.map((ele, i) => {
        if (value.includes(ele)) {
          list.push(i)
        } else {
          // 未选中行，移除拖拽标志
          const el = drageRows[i]
          if (el) {
            const checkBox = el.firstChild
            const handleDrag = checkBox.querySelector('.handleDrag')
            if (handleDrag) {
              checkBox.removeChild(handleDrag)
            }
          }
        }
      })

      // 清除拖拽选中
      drageRows.forEach(el => {
        Sortable.utils.deselect(el)
      })
      // 获取表格行的元素
      const multiDrags = list.map(v => drageRows[v])
      // 手动设置选中表格行拖拽
      multiDrags.map(item => {
        const checkBox = item.firstChild
        const handleDrag = checkBox.querySelector('.handleDrag')
        // 不存在拖拽,添加拖拽样式
        if (!handleDrag) {
          const handleGrab = document.createElement('i')
          handleGrab.classList.add('iconfont', 'el-icon-icon-line-tuozhuai', 'handleDrag')
          item.firstChild.insertBefore(handleGrab, item.firstChild.childNodes[0])
        }

        Sortable.utils.select(item)
      })
    },
    selectAllEvent({ checked }) {
      const list = this.$refs['testm-case-table'].getCheckboxRecords()
      this.selectChange(list)
    },
    selectChangeEvent({ checked }) {
      const list = this.$refs['testm-case-table'].getCheckboxRecords()
      this.selectChange(list)
    },
    // 导入
    imPort() {
      this.importParam = { visible: true, id: this.menuNode.id }
    },
    hasError(val) {
      this.errorImportParam = { visible: true, info: val }
    },
    // 导入冲突
    xmindConflict(val) {
      this.conflictParam = { visible: true, libraryId: this.$route.params.caseId || this.libraryId }
    },
    // 冲突成功解决回调
    successConflict() {
      this.importParam = { visible: false }
      // this.getTreeData()
      this.$refs.tree.refreshNode(this.menuNode.id)
      if (this.allTreeStatus) {
        this.$refs.allTree.getCount(this.menuNode)
      } else {
        this.$refs.tree.getCount(this.menuNode)
      }
    },
    exportTypeFile(e, t) {
      this.exportOptions = {
        visible: true
      }
      this.selectedTreeItem = {
        id: e.id,
        type: t
      }
      // if (value === 'xlsx') this.exportFlie()
    },
    // 导出excel
    async exportFlie({ fileType, fields, type }) {
      try {
        const params = {
          libraryId: this.$route.params.caseId || this.libraryId,
          treeId: this.selectedTreeItem.id,
          fields // 未选中字段
        }
        this.exportLoading = true
        var apiUrl = ''
        if (this.selectedTreeItem.type == 'tree') {
          apiUrl = `/api/testm/testProductCase/excel/exportExcelMore?type=${type}`
        } else {
          apiUrl = `/api/testm/testProductCase/excel/${fileType == 'world' ? 'exportWord' : 'export'}`
        }
        download(`测试用例.${fileType == 'world' ? 'docx' : 'xlsx'}`, await apiBaseFileLoad(
          apiUrl, params
        ))
        this.exportLoading = false
      } catch (e) {
        //
        fileType !== 'world' ? this.$message.error('导出失败！')
          : this.$message.error('单次导出用例数量最多为5000!')
        this.exportLoading = false
        return
      }
    },

    // 编辑测试用例
    async editRow(row) {
      if (this.type == 'catchTree') return
      this.$refs.caseCard.caseCardLoading = true
      const res = await productCaseDetail(row.id)
      const params = { ...res.data }

      params.tabInfo = res.data?.tabs?.map(item => item.name || item) || []
      this.addVisible = true

      this.dialogType = 'edit'
      this.rowId = row.id

      this.caseForm = params
      this.$refs.caseCard.caseCardLoading = false
    },
    // 查看测试用例详情
    async detailRow(row) {
      // 查询已归档或未归档用例详情接口
      const fetchApi = this.type == 'archiveTree' ? getArchiveDetail : productCaseDetail

      if (!this.$permission('testm_test_use_file_detail')) return // 归档用例查看详情权限控制
      const res = await fetchApi(row.id)

      const params = this.type !== 'archiveTree' ? { ...res.data } : { ...res.data, ...res.data.echoMap }
      params.tabInfo = res.data?.tabs?.map(item => item.name) || []
      this.addVisible = true
      this.dialogType = 'detail'
      this.rowId = row.id
      this.caseForm = params
    },
    // 删除
    async deleteRow(row) {
      try {
        let msg = `确定将【${row.name}】移动到回收站吗？` // 提示信息
        let fetchApi = trashCase // 请求接口
        let successInfo = '移入成功' // 成功提示
        if (this.type === 'catchTree') {
          msg = `确定删除【${row.name}】吗？`
          fetchApi = testProductTreeCaseDel
          successInfo = '删除成功'
        }
        await this.$confirm(`${msg}？`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        const res = await fetchApi([row.id])
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success(`${successInfo}`)
        this.getTableData()
        this.$nextTick(() => {
          if (this.type !== 'catchTree' && this.allTreeStatus) {
            this.$refs.allTree.getCount(this.menuNode)
          } else if (this.type !== 'catchTree' && !this.allTreeStatus) {
            this.$refs.tree.getCount(this.menuNode)
          }
        })
      } catch (e) {
        if (e === 'cancel') return
      }
    },

    // 新增用例
    openAddCard() {
      this.addVisible = true
      this.caseForm = {}
      this.dialogType = 'add'
    },
    success() {
      this.$nextTick(() => {
        if (this.allTreeStatus) {
          this.$refs.allTree.getCount(this.menuNode)
        } else {
          this.$refs.tree.getCount(this.menuNode)
        }
      })
      this.getTableData()
    },
    // 复制用例
    async copyCase(row) {
      this.copyParam = { visible: true, caseId: row.id }
    },
    // 批量复制
    async allCopy(row) {
      let Ids = []
      if (row && row.id) {
        Ids = [row.id]
      } else if (this.getVxeTableSelectData('testm-case-table').length > 0) {
        Ids = this.getVxeTableSelectData('testm-case-table').map(r => r.id)
      }
      if (Ids.length === 0) {
        this.$message.warning('请勾选要复制的用例')
        return
      }
      this.copyParam = { visible: true, caseList: Ids }
    },
    // 查看历史版本
    async checkHistory(row) {
      if (this.$route.params.id) {
        this.$emit('triggerVersion', { visible: true, row })
      } else {
        this.$router.push({
          path: `/test/useCase/version/${row.id}`
        })
      }
    },
    // 执行记录
    doRecords(row) {
      if (this.$route.params.id) {
        this.$emit('triggerRecord', { visible: true, row })
      } else {
        this.$router.push({
          path: `/test/useCase/records/${row.id}`,
          params: {
            id: row.id
          },
          query: {
            caseKey: row.caseKey,
            name: row.name
          }
        })
      }
    }

  }
}
</script>
<style lang='scss' scoped>

.leftSection {
	width: 330px;
  overflow: hidden;
  // overflow-y: hidden;
  // overflow-x: auto;
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		span {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
  }
  .menubox {
    border-bottom: 1px solid var(--solid-border-color);
    padding:8px 16px;
    a {
      margin:0px -16px;
      line-height: 36px;
      display: block;
      padding: 0px 28px;
      color: var(--font-main-color);
      &:hover{
      background: var(--content-bg-hover-color);
      }
    }
    .is-active {

      background-color: var(--main-hover-page-color);
      color: var(--main-theme-color);

    }
  }
}
.vone-tab-line {
  margin: 8px 16px 8px 16px;
}
:deep(.vone-tab-line .el-tabs__item) {
  height: 36px;
  line-height: 36px;
}
:deep(.vone-tab-line .el-tabs__item.is-top:nth-child(2)) {
  margin-left: 0px;
}
.tabBtnbox {
  float: right;
  margin: -34px 16px 0px 0px;
  position: relative;
  z-index: 999;
  .iconfont {
    width: 30px;
    text-align: center;
    color: var(--font-second-color);
  }
}
.treebox{
  width: 330px;
  overflow: auto;
}
.icon {
  font-size: 16px
}
</style>

