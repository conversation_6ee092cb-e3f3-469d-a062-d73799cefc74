<!-- 文件导出设置 -->
<template>
  <el-dialog
    title="文件导出"
    :visible="visible"
    width="40%"
    append-to-body
    :close-on-click-modal="false"
    @close="onClose"
    v-on="$listeners"
  >
    <el-form>
      <el-form-item v-if="types == 'tree'" label="文件类型">
        <el-radio-group v-model="form.type">
          <el-radio label="0">
            xlsx(多sheet)
            <el-tooltip :open-delay="500" content="导出用例目录最多支持255个" placement="top-start">
              <i class="el-icon-warning-outline color-danger ml-2" />
            </el-tooltip>
          </el-radio>
          <el-radio label="1">word(单sheet)</el-radio></el-radio-group>
      </el-form-item>
      <el-form-item v-if="types == 'more'" label="文件类型">
        <el-radio-group v-model="form.fileType">
          <el-radio label="xlsx">xlsx</el-radio>
          <el-radio label="world">word</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.fileType=='xlsx'" label="自定义字段">
        <el-checkbox-group v-model="form.fields">
          <el-checkbox v-for="item in fieldList" :key="item.value" :label="item.value" :disabled="item.value === 'name'">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <el-row slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import { getExportFileFields } from '@/api/vone/testmanage/file'
import { catchErr } from '@/utils'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    types: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        fileType: 'xlsx',
        fields: ['name'],
        type: '0'
      },
      allCheckdFields: [], // 全部选择的字段
      fieldList: []
    }
  },
  created() {
    this.getExportFields()
  },
  methods: {
    onClose() {
      this.form = {
        fileType: 'xlsx',
        fields: []
      }
      this.$emit('update:visible', false)
    },
    submit() {
      const unCheckedFields = this.allCheckdFields.filter(item => !this.form.fields.includes(item))
      const params = {
        type: this.form.type,
        fileType: this.form.fileType,
        fields: unCheckedFields
      }
      this.$emit('success', params)

      this.onClose()
    },
    async getExportFields() {
      const [res, err] = await catchErr(getExportFileFields())
      if (err) return
      if (res.isSuccess) {
        // 默认全选
        this.allCheckdFields = Object.keys(res.data)
        this.form.fields = [...this.allCheckdFields]
        this.fieldList = Object.entries(res.data).map(([value, label]) => {
          return {
            label,
            value
          }
        })
      }
    }

  }
}
</script>
<style lang='scss' scoped>

</style>
