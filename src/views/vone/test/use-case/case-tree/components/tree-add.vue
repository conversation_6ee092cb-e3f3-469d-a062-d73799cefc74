<!-- 目录树名称弹窗 -->
<template>
  <el-dialog :title="title" v-model="visible" width="30%" :before-close="onClose" :close-on-click-modal="false">
    <el-form ref="treeFormRef" :model="treeForm" :rules="rulesFormRules" label-position="top">
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="treeForm.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="treeForm.description" :rows="5" show-word-limit type="textarea" maxlength="200" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="save">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import storage from 'store'

import { productCaseTreeAdd, productCaseTreeEdit } from '@/api/vone/testmanage/case'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 节点数据
    data: {
      type: Object,
      default: () => ({})
    },
    flag: {
      type: String,
      default: 'add'
    },
    libraryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      title: '添加分组',
      treeForm: {
        name: ''
      },
      rulesFormRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            pattern: '[^ ]+',
            message: '名称不能为空格'
          },
          {
            max: 64,
            message: '名称不能超过64个字符'
          }
        ]
      }
    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      if (this.flag === 'edit') {
        this.title = '编辑分组名称'
        this.treeForm = { ...this.data }
      } else {
        this.title = '新增分组名称'
        this.treeForm = {}
      }
    }
  },
  methods: {
    async save() {
      try {
        await this.$refs.treeFormRef.validate()
      } catch (error) {
        return
      }
      this.flag === 'add' ? this.addNodeTree() : this.eidtNodeTree()
    },
    // 新增节点
    async addNodeTree() {
      this.loading = true
      const params = {
        state: true,
        createdBy: this.data.createdBy || this.loginUser.id,
        leadingBy: this.data.leadingBy || this.loginUser.id,
        libraryId: this.$route.params.caseId || this.libraryId,
        name: this.treeForm.name,
        description: this.treeForm.description,
        parentId: this.data.id || '0'
      }
      const res = await productCaseTreeAdd(params)
      if (res.isSuccess) {
        this.loading = false
        this.$message.success('新增成功')
        this.$emit('success')
        this.onClose()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改节点名称
    async eidtNodeTree() {
      const params = {
        state: true,
        id: this.data.id,
        leadingBy: this.data.leadingBy,
        name: this.treeForm.name,
        parentId: this.data.parentId,
        description: this.treeForm.description,
        libraryId: this.$route.params.caseId || this.libraryId
      }
      const res = await productCaseTreeEdit(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.$emit('success')
        this.onClose()
      }
    },
    onClose() {
      this.treeForm.name = ''
      this.treeForm.description = ''
      this.loading = false
      this.$nextTick(() => {
        this.$refs.treeFormRef.resetFields()
        this.$emit('update:visible', false)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.nameCls {
  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
  }
}
</style>
