<template>
  <el-dialog
    v-if="visible"
    title="关联计划"
    width="78%"
    top="10vh"
    :visible="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
  >
    <el-row>
      <el-col :span="8" class="elStyle l_body">
        <el-input v-model="filterText" placeholder="搜索" prefix-icon="el-icon-search" style="margin-bottom:8px;" />
        <el-tree
          ref="tree"
          show-checkbox
          :data="treeData"
          node-key="id"
          highlight-current
          :expand-on-click-node="false"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="treeCaseClick"
          @check="checkChange"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <span class="treeNode">{{ data.name }}</span>
          </span>
        </el-tree>
      </el-col>
      <el-col v-loading="tableLoading" :span="16" class="elStyle r_body">
        <el-form ref="form" inline :model="form" :rules="rules">
          <el-form-item label="报告名称:" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
        </el-form>
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic table-search-key="testm-report-table" :model="formData" :table-ref="$refs['testm-report-table']" @getTableData="getTableList(currentNode.id)">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="测试计划" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="编号" prop="caseKey">
                    <el-input v-model="formData.caseKey " placeholder="请输入编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="标签" prop="tabName">
                    <tagSelect v-model="formData.tabName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority ">
                    <vone-icon-select v-model="formData.priority" :data="prioritList" filterable clearable style="width:100%">
                      <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                        <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                        {{ item.name }}
                      </el-option>
                    </vone-icon-select>
                  </el-form-item>
                </el-col>
              </el-row>

            </vone-search-dynamic>
          </template>

        </vone-search-wrapper>

        <div style="height:calc(100vh - 502rem);">
          <vxe-table
            ref="project-report-tableConPlan"
            class="vone-vxe-table draggTable"
            border
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth:'120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
            @checkbox-change="selectionChange"
            @checkbox-all="selectionAll"
          >
            <vxe-column type="checkbox" width="36" fixed="left" align="center" />
            <vxe-column title="计划" field="name" show-overflow-tooltip>
              <template v-slot="{ row }">
                <div style="text-align:left;">
                  <i class="iconfont el-icon-testcase" style="color: var(--main-theme-color,#3e7bfa);margin-right:4px;" />
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="type" title="类型">
              <template v-slot="{ row }">
                {{ row.type=='system'?'系统测试':row.type=='smoke'?'冒烟测试':'回归测试' }}
              </template>

            </vxe-column>
            <vxe-column title="维护人" field="leadingBy" show-overflow-tooltip>
              <template v-slot="{ row }">
                <span v-if="row.leadingBy && row.userData">
                  <vone-user-avatar
                    :avatar-path="row.userData.avatarPath"
                    :name="row.userData.name"
                  />
                </span>

              </template>
            </vxe-column>
          </vxe-table>
          <vone-pagination ref="pagination" :total="tableData.total" @update="getTableList(currentNode.id)" />
        </div>

      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="saveInfo">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { list2Tree } from '@/utils/list2Tree'

import { TestPlanTree, getTestPlanCase } from '@/api/vone/testplan'
import { catchErr } from '@/utils'
import { getUserDetail } from '@/api/vone/base/user'
import tagSelect from '@/views/vone/test/components/tag-select.vue'

export default {
  components: {
    tagSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    // 已关联用例
    caseList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {

      form: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      filterText: '',
      userMap: {}, // 用户信息
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      formData: {
        name: '',
        caseKey: '',
        tabName: '',
        priority: ''
      },
      tableOptions: {
        isOperation: false, // 表格有操作列时设置
        isIndex: false // 列表序号
      },
      treeData: [],
      btnLoading: false,
      tableLoading: false,
      tableSelected: [],
      tableData: { records: [] }, // 表格数据
      currentNode: {},
      defaultProps: {
        label: 'name'
      },
      checkArray: []
    }
  },
  computed: {
    // 默认选中用例id
    selectedId() {
      return this.tableSelected.map(v => v.id)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$set(this.form, 'name', '')
        this.tableSelected = [...this.caseList]
        this.getTreeList()
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().indexOf(value) !== -1
    },
    async getTreeList() {
      const [res, err] = await catchErr(TestPlanTree()) // 计划id
      if (err) return
      if (res.isSuccess) {
        this.treeData = list2Tree(res.data, { parentKey: 'parentId' })
        this.tableSelected = [...this.caseList]
        this.currentNode = this.treeData[0] || {}
        // 查询表格数据
        this.getTableList(this.currentNode.id)
        this.$nextTick(() => {
          // 设置默认选中第一行
          this.$refs?.tree.setCurrentKey(this.currentNode.id)
        })
      }
    },
    treeCaseClick(data) { // 点击节点
      this.currentNode = data
      this.getTableList(data.id)
    },
    checkChange(row, e) {
      console.log(row, e)
      this.currentNode = row
      if (e.checkedKeys.indexOf(row.id) > -1) {
        this.checkArray = e.checkedKeys
        this.getTableList(row.id)
      } else {
        this.selectionAll([])
        this.clearSelection()
      }
    },

    onClose() {
      // 取消选择
      this.clearSelection()
      this.$emit('update:visible', false)
      this.tableData = { records: [] }
      this.currentNode = {}
      // 清空选中节点
      this.$refs.tree?.setCurrentKey()
    },
    // 查询用户信息
    async getUserDetail(userId, row) {
      if (this.userMap[userId]) {
        this.$set(row, 'userData', this.userMap[userId])
        return
      }
      const [res, err] = await catchErr(getUserDetail(userId))
      if (err) return
      if (res.isSuccess) {
        this.userMap[userId] = res.data
        this.$set(row, 'userData', res.data)
      }
    },
    // 查询表格列表
    async getTableList(nodeId) {
      const tableQuery = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableQuery,
        extra: {},
        model: {
          ...this.formData,
          treeId: nodeId
        }
      }
      this.tableLoading = true
      const [res, err] = await catchErr(getTestPlanCase(params))
      this.tableLoading = false
      if (err) return
      if (res.isSuccess) {
        this.tableData = res.data
        this.tableData.total = res.data.total
        const tableList = res.data.records
        tableList.forEach(v => {
          v.leadingBy && this.getUserDetail(v.leadingBy, v)
        })

        this.clearSelection()
        if (tableList.length === 0) return
        // 点击左侧树设置表格全选
        if (this.checkArray.indexOf(nodeId) > -1) {
          this.tableData.records.forEach((ele, i) => {
            this.$nextTick(() => {
              this.$refs['project-report-tableConPlan'].setCheckboxRow(ele, true)
            })
          })
          this.selectionAll(this.tableData.records)
        } else {
          const checkId = [] // 当前表格选中
          tableList.map((item) => {
            if (this.selectedId.indexOf(item.id) > -1) {
              checkId.push(item.id)
              this.$nextTick(() => {
                this.$refs['project-report-tableConPlan'].setCheckboxRow(item, true)
              })
            }
          })
          if (checkId.length > 0) {
            // 是否半勾选
            const isHalf = checkId.length < tableList.length
            this.$refs.tree.getNode(nodeId).indeterminate = isHalf // 设置半选中状态
            if (checkId.length === tableList.length) {
              this.$refs.tree.setChecked(this.currentNode.id, true)
            }
          }
        }
        // this.$refs['project-report-tableConPlan'].doLayout()
      }
    },
    // 保存选中用例
    async saveInfo() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const treeIds = this.$refs.tree.getCheckedKeys()
          if (this.tableSelected?.length === 0) {
            this.$message.warning('请选择测试计划')
            return
          }
          this.btnLoading = true
          this.$emit('success', this.tableSelected, treeIds, this.form.name)
          this.btnLoading = false
          // this.onClose()
        } else {
          return false
        }
      })
    },
    // 切换全选
    selectionAll(selection) {
      // 是否全选
      const checkAll = selection.length ? selection.length > 0 : selection.records.length > 0
      const node = this.$refs.tree.getNode(this.currentNode.id)
      let parent = node.parent

      this.$refs.tree.setChecked(this.currentNode.id, checkAll)
      node.indeterminate = false

      // 手动设置父级半选中
      while (parent && parent.level >= 0) {
        parent.indeterminate = checkAll || parent.childNodes.some(node => node.checked || node.indeterminate) // 全选或者有子节点选中
        parent = parent.parent
      }
      // 全选
      if (checkAll) {
        selection.records?.map(item => {
          this.selectedId.indexOf(item.id) === -1 && this.tableSelected.push(item)
        })
      } else {
        // 取消选择
        this.tableData.records.forEach((ele) => {
          let index = this.selectedId.indexOf(ele.id)
          while (index > -1) {
            this.tableSelected.splice(index, 1)
            index = this.selectedId.indexOf(ele.id)
          }
        })
      }
    },
    // 切换选择
    selectionChange(selection) {
      // 当前项选中还是未选中
      const isSelect = selection.checked

      if (isSelect) {
        this.tableSelected.push(selection.row)
      } else {
        let index = this.selectedId.indexOf(selection.row.id)
        while (index > -1) {
          this.tableSelected.splice(index, 1)
          index = this.selectedId.indexOf(selection.row.id)
        }
      }

      const isChecked = selection.checked
      const isIndeterminate = selection.records.length > 0 ? selection.records.length < this.tableData.records.length : false
      const node = this.$refs.tree.getNode(this.currentNode.id)
      let parent = node.parent
      // 手动设置当前节点选中及父节点选中状态
      this.$refs.tree.setChecked(this.currentNode.id, isChecked)
      node.indeterminate = isIndeterminate
      while (parent && parent.level >= 0) {
        parent.indeterminate = isIndeterminate || parent.childNodes.some(node => node.checked || node.indeterminate) // 全选或者有子节点选中
        parent = parent.parent
      }
    },
    // 取消选择
    clearSelection() {
      this.$refs['project-report-tableConPlan']?.clearSelected()
    }

  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-dialog__body {
    padding: 0;
  }
}
.elStyle {
  height: 515px;
  padding: 16px;
  overflow: auto;
  :deep(.el-tree-node > .el-tree-node__children) {
    min-width: min-content;
  }
}
// .l_body{
//   overflow-y: auto;
// }
.r_body {
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }
  .iconCls {
    margin: 0 3px;
  }

  .treeNode {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
