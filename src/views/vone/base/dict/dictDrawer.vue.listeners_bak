<template>
  <el-dialog :title="dicId ? '修改字典' : '添加字典'" v-model:visible="visible" :before-close="onClose" v-bind="$attrs" :wrapper-closable="false" width="600px" v-on="$listeners">
    <div v-loading="pageLoading" class="vone-drawer-main">
      <!-- 基本配置 -->
      <el-form ref="baseForm" :model="dictData" :rules="rules" label-position="top">
        <el-row :gutter="24" class="row-box">
          <el-col :span="12">
            <el-form-item prop="label" label="字典类型">
              <el-input v-model="dictData.label" disabled />
            </el-form-item>
          </el-col>
          <el-col v-if="hasParentKey" :span="12">
            <template>
              <el-form-item v-for="item in templateList" :key="item.code" prop="parentId" :label="item.label">
                <el-select v-model="dictData.parentId" style="width:100%" filterable>
                  <el-option v-for="item in enumList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>

            </template>

          </el-col>
          <el-col :span="12">
            <el-form-item prop="code" label="编码">
              <el-input
                v-model="dictData.code"
                placeholder="请输入编码"
                :disabled="dicId ? true : false"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="name" label="字典名称">
              <el-input v-model.trim="dictData.name" placeholder="请输入字典名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="sort" label="排序">
              <el-input-number v-model="dictData.sort" style="width: 100%" :min="0" :max="100" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="state" label="状态">
              <el-switch v-model="dictData.state" class="openSwitch" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item prop="description" label="描述">
              <el-input v-model="dictData.description" type="textarea" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 扩展属性 -->

      <div v-if="hasAdvanced">
        <el-form ref="advancedForm" :model="advanceForm" :rules="advanceRules" label-position="top">
          <el-row :gutter="24">
            <el-col v-for="(template, i) in advanceTemplete" :key="i" :span="12">
              <el-form-item :prop="template.key" :label="(template.key === 'OPNE_PROCESS_CODE')&&!flowConfig?'':template.name">
                <el-input v-if="template.key === 'tagStyle'" v-model="advanceForm[template.key]" type="color" />

                <el-select
                  v-if="((template.key === 'OPNE_PROCESS_CODE')&&flowConfig)"
                  v-model="advanceForm[template.key]"
                  :placeholder="template.attPlaceholder"
                  clearable
                  filterable
                >
                  <el-option v-for="(item, index) in flowConfigList" :key="index" :label="item.name" :value="item.key" />
                </el-select>

                <el-select
                  v-if="template.key === 'RELEASE_PLAN'"
                  v-model="advanceForm[template.key]"
                  :placeholder="template.attPlaceholder"
                  clearable
                  filterable
                >
                  <el-option v-for="(item, index) in flowList" :key="index" :label="item.name" :value="item.modelKey" />
                </el-select>
                <el-select
                  v-if="template.key === 'RELEASE_WORKORDES'"
                  v-model="advanceForm[template.key]"
                  :placeholder="template.attPlaceholder"
                  clearable
                  filterable
                >
                  <el-option v-for="(item, index) in flowList" :key="index" :label="item.name" :value="item.modelKey" />
                </el-select>
                <el-select
                  v-if="template.key === 'PROJECT_START'"
                  v-model="advanceForm[template.key]"
                  :placeholder="template.attPlaceholder"
                  clearable
                  filterable
                >
                  <el-option v-for="(item, index) in modelKey" :key="index" :label="item.name" :value="item.key" />
                </el-select>
                <el-select
                  v-if="template.key === 'PROJECT_CLOSE'"
                  v-model="advanceForm[template.key]"
                  :placeholder="template.attPlaceholder"
                  clearable
                  filterable
                >
                  <el-option v-for="(item, index) in modelKey" :key="index" :label="item.name" :value="item.key" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

    </div>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveDict">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
const envList = [
  { key: '0', label: '开发环境' },
  { key: '1', label: '测试环境' },
  { key: '2', label: '生产环境' }
]
import {
  apiBaseDictAddDict,
  apiBaseDictGetInfoById,
  apiBaseDictEdit,
  apiBaseDictNoPage,
  apiBaseDictExtend,
  actReProcdef
} from '@/api/vone/base/dict'
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dictKey: {
      type: String,
      default: () => undefined
    },
    row: {
      type: Object,
      default: () => { }
    },
    dicId: {
      type: String,
      default: () => undefined
    },
    dictList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      dictData: {
        sort: 0,
        type: this.dictKey,
        label: '',
        state: true
      },
      envList,
      enumList: [],
      parentObj: {},
      saveLoading: false,
      pageLoading: false,
      customDatasForms: {
        customDatas: [{}]
      },

      rules: {
        // label: [{ required: true, message: '请选择' }],
        parentId: [{ required: true, message: '请选择' }],
        code: [{ required: true, message: '请输入标识' }],
        name: [{ required: true, message: '请输入名称' }, {
          max: 64, message: '输入内容长度不能超过64个字符'
        }],
        description: [
          {
            required: false,
            max: 250, message: '输入内容长度不能超过250个字符'
          }
        ]
      },
      advanceForm: {
        tagStyle: '',
        RELEASE_PLAN: '',
        RELEASE_WORKORDES: '',
        PROJECT_START: '',
        PROJECT_CLOSE: '',
        OPNE_PROCESS_CODE: ''
      },
      hasParentKey: false, // 是否有父级
      hasAdvanced: false, // 是否有扩展属性
      templateList: [], // 父级属性模板
      advanceTemplete: [], // 扩展属性模板
      advanceRules: {}, // 高级属性
      flowList: [],
      flowConfig: false,
      flowConfigList: []
    }
  },

  watch: {
    visible: {
      handler: function(v) {
        if (!v) return
        this.getExtend()
      },
      immediate: true
    },
    'dictData.parentId': {
      handler(val) {
        // if (this.enumList.find(r => r.id == val).code == 'CODE_WAREHOUSE') {
        if (val == '1455378001788141568') { // 代码仓库引擎id
          this.getFlowConfig()
          this.flowConfig = true
        } else {
          this.flowConfig = false
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.dicId) {
      this.getDictInfo()
    } else {
      const label = this.dictList.find(r => r.type == this.dictKey).label
      this.$set(this.dictData, 'label', label)
    }
    // this.getFlowList()
  },
  methods: {
    getFlowList() {
      getFlowModelListNoPage({ status: 1, mainProcess: 1, typeId: this.dictKey == 'PROJECT_TYPE' ? 1018 : 1008 }).then(res => {
        if (res.isSuccess) {
          this.flowList = []
        }
      })
    },
    getFlowConfig() {
      actReProcdef({}).then(res => {
        if (res.isSuccess) {
          this.flowConfigList = res.data
        }
      })
    },
    async getExtend() {
      const res = await apiBaseDictExtend(this.dictKey)

      if (!res.isSuccess) {
        return
      }
      if (res.data) {
        if (res.data.parentKey) {
          this.hasParentKey = true
          this.getEnmuList(res.data.parentKey.type)
          this.templateList = [res.data.parentKey]
        } else {
          this.hasParentKey = false
        }

        if (res.data.extendList) {
          this.hasAdvanced = true
          this.advanceTemplete = res.data.extendList
          const advanceRoule = this.advanceTemplete.map((r) => ({
            required: r.notEmpty,
            message: r.message,
            max: r.maxlength,
            trigger: 'change',
            pattern: r.regexp,
            key: r.key
          }))

          advanceRoule.forEach((item) => {
            if (!this.advanceRules[item.key]) {
              this.advanceRules[item.key] = [item]
            } else {
              this.advanceRules[item.key].push(item)
            }
          })
        } else {
          this.hasAdvanced = false
        }
      }
    },
    onClose() {
      // this.$refs.baseForm.resetFields()
      this.$emit('update:visible', false)
    },
    async getEnmuList(type) {
      const res = await apiBaseDictNoPage(
        {
          type: type
        }
      )

      if (!res.isSuccess) {
        // return this.$message.error(res.msg)
      }
      this.enumList = res.data
    },
    async saveDict() {
      if (this.dicId) {
        this.editDict()
      } else {
        try {
          await Promise.all([
            this.$refs.baseForm.validate(),
            this.advanceTemplete.length ? this.$refs.advancedForm.validate() : null
          ])
        } catch (error) {
          return
        }

        this.saveLoading = true

        // 拼接扩展属性
        const dictionaryExtendeds = []
        this.advanceTemplete.map((r, p) => {
          dictionaryExtendeds.push({
            key: r.key,
            value: this.advanceForm[r.key]
          })
        })

        const res = await apiBaseDictAddDict(
          {
            ...this.dictData,
            dictionaryExtendeds: dictionaryExtendeds
          }
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.saveLoading = false
          return this.$message.error(res.msg)
        }
        this.$message.success('保存成功')
        this.onClose()
        this.$emit('success')
      }
    },
    async editDict() {
      // if (this.dictData.label == '发布类型') {
      //   this.dictData.dictionaryExtendeds.map(item => {
      //     item.value = this.advanceForm[item.key]
      //   })
      // }
      // if (this.dictData.label == '项目流程类型') {
      //   this.dictData.dictionaryExtendeds.map(item => {
      //     item.value = this.advanceForm[item.key]
      //   })
      // }
      // if (this.dictData.label == '引擎类型') {
      //   this.dictData.dictionaryExtendeds.map(item => {
      //     item.value = this.advanceForm[item.key]
      //   })
      // }

      // 拼接扩展属性
      const dictionaryExtendeds = []
      this.advanceTemplete.map((r, p) => {
        dictionaryExtendeds.push({
          key: r.key,
          value: this.advanceForm[r.key]
        })
      })

      await this.$refs.baseForm.validate()
      this.saveLoading = true
      const res = await apiBaseDictEdit(
        {
          ...this.dictData,
          dictionaryExtendeds: dictionaryExtendeds
        }
      )
      this.saveLoading = false
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }

      this.$message.success('修改成功')
      this.onClose()
      this.$emit('success')
    },

    // 点击+号图标添加新的一栏
    async addRow(index, rows, str) {
      if (str == 'custom') {
        const res = await this.$refs.customDatasForm.validate()
        if (res == false) return false
      }
      rows.splice(index + 1, 0, {})
    },
    // 点击减号删除一行
    deleteRow(index, rows) {
      rows.splice(index, 1)
    },
    // 回显详情
    async getDictInfo() {
      this.pageLoading = true
      const res = await apiBaseDictGetInfoById(this.dicId)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      // if(){

      // }
      this.dictData = res.data
      if (res.data.label == '发布类型' || res.data.label == '环境' || res.data.label == '项目流程类型' || res.data.label == '引擎类型') {
        res.data.dictionaryExtendeds.map(item => {
          this.advanceForm[item.key] = item.value || ''
        })
      } else {
        this.advanceForm = res.data.dictionaryExtendeds[0]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.row-box {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap
}
</style>

