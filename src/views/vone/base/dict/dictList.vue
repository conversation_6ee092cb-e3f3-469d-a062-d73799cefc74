<template>
  <div>
    <vone-search-wrapper>
      <template slot="custom">
        <strong>{{ dictValue }}</strong>
      </template>
      <template slot="actions">
        <el-button v-if="dictKey" type="primary" icon="iconfont el-icon-tips-plus-circle" size="small" :disabled="!$permission('base_dict_add')" @click="clickAddDict">
          新增</el-button>
        <el-dropdown v-if="dictKey" trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div style="height:calc(100vh - 180px)">
      <vxe-table
        ref="dict-table"
        class="vone-vxe-table"
        height="auto"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{name: 'empty'}"
        :loading="tableLoading"
        :data="tableData.records"
        :column-config="{
          minWidth:'120px'
        }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" />
        <vxe-column title="名称" field="name" show-overflow-tooltip min-width="150" />
        <vxe-column field="state" show-overflow-tooltip title="状态" width="120">
          <template #default="scope">
            <el-switch v-model="scope.row.state" class="openSwitch" :disabled="scope.row.readonly || !$permission('base_dict_edit')" active-text="启用" inactive-text="禁用" @change="editStatus(scope.row)" />
          </template>
        </vxe-column>
        <vxe-column field="code" show-overflow-tooltip title="编码" width="120" />
        <vxe-column field="description" show-overflow-tooltip title="描述" />
        <vxe-column v-if="dictKey == 'ENGINE_INSTANCE'" field="engineName" show-overflow-tooltip title="引擎分类">
          <template #default="scope">
            <div v-if="JSON.stringify(scope.row.echoMap) != '{}' && scope.row.echoMap.parentId" size="small">{{ scope.row.echoMap.parentId.name }}</div>
            <div v-else>--</div>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button :disabled="row.readonly || !$permission('base_dict_edit')" type="text" icon="iconfont el-icon-application-edit" @click="editRowDict(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button :disabled="row.readonly || !$permission('base_dict_delete')" type="text" icon="iconfont el-icon-application-delete" @click="delRowDict(row)" />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getDicList" />
    <dictDrawer v-if="addDictParams.visible" ref="addDict" v-bind="addDictParams" v-model="addDictParams.visible" :dict-key="dictKey" :dict-list="allDict" @success="getDicList" />

  </div>
</template>

<script>
import dictDrawer from './dictDrawer.vue'
import { apiBaseDictDelDic, apiBaseDictPage, apiBaseDictUpdateStatus } from '@/api/vone/base/dict'

export default {
  components: {
    dictDrawer
  },
  props: {
    dictKey: {
      type: String,
      default: () => undefined
    },
    dictValue: {
      type: String,
      default: () => undefined
    },
    allDict: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoading: false,
      formData: {
        type: 'ENVIRONMENT_TYPE'
      },
      selecteTableData: [],
      tableData: {},

      addDictParams: {
        visible: false
      },
      dictList: [],
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('base_dict_delete'),
          fn: this.allDel
        }
      ]

    }
  },
  watch: {
    dictKey: {
      handler(value) {
        this.getDicList(value)
      }
      // immediate: true
    }
  },
  mounted() {
    if (this.allDict.length) {
      this.dictList = this.allDict
    }
  },
  methods: {
    selectable(row) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    async getDicList(value) {
      this.$set(this.formData, 'type', value || this.dictKey)
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiBaseDictPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    clickAddDict() {
      this.addDictParams = {
        visible: true
      }
    },
    // 修改字典状态
    async editStatus(row) {
      try {
        this.pageLoading = true
        const res = await apiBaseDictUpdateStatus(row.id, row.state)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('状态修改成功')
        this.getDicList()
      } catch (error) {
        this.pageLoading = false
        return
      }
    },

    refreshTable(isReset) {
      this.$refs['dict-table'].getTableData(isReset)
    },
    editRowDict(row) {
      this.addDictParams = {
        visible: true,
        dicId: row.id
      }
    },
    // 批量删除
    async allDel() {
      const params = this.getVxeTableSelectData('dict-table').map(r => r.id)

      if (!params.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      await this.$confirm(`确定删除 ${params.length} 条数据吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })

      const res = await apiBaseDictDelDic(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getDicList()
    },
    async delRowDict(item) {
      await this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
      const res = await apiBaseDictDelDic([
        item.id
      ])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getDicList()
    }

  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";
.switchStyle :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}
.switchStyle :deep(.el-switch__label--left) {
  z-index: 9;
  left: 18px;
}
.switchStyle :deep(.el-switch__label--right) {
  z-index: 9;
  left: -5px;
}
.switchStyle :deep(.el-switch__label.is-active) {
  display: block;
}
.switchStyle:deep(.el-switch__core) {
  width: 54px !important;
}
.switchStyle :deep(.el-switch__label) {
  width: 54px !important;
}
</style>
