<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="noticeTable"
          v-model:model="formData"
          :table-ref="$refs['noticeTable']"
          v-model:default-fileds="defaultFileds"
          show-basic
          v-model:extra="extraData"
          @getTableData="getTableList"
        />
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-icon-system-add" @click="addNotice">
          新增
        </el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getTableList"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{height: $tableHeight}">
      <vxe-table
        ref="noticeTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
      >
        <vxe-column type="checkbox" width="37" fixed="left" />
        <vxe-column title="索引号" field="code" width="130">
          <template #default="{row}">
            <a @click="showInfo(row)">
              {{ row.code }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="标题" field="title" />
        <vxe-column title="主题分类" field="noticeType" width="100">
          <template #default="{row}">
            {{ notifyTypeMap[row.noticeType] }}
          </template>
        </vxe-column>
        <vxe-column title="发布状态" field="state" width="90">
          <template #default="{ row }">
            <el-tag v-if="row.state == 2" style="color:var(--Red-10); border-color:var(--Red--50); background: var(--Red--50)">已撤销</el-tag>
            <el-tag v-if="row.state == 1" style="color:var(--Green-10); border-color:var(--Green--50); background: var(--Green--50)">已发布</el-tag>
            <el-tag v-if="row.state == 0" style="color:#8C8C8C; border-color:#F5F5F5; background: #F5F5F5"> 暂存</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="发布日期" field="createTime" width="180" />
        <vxe-column title="发布人" field="updatedBy" width="120">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="row.echoMap&&row.echoMap.updatedBy &&row.echoMap.updatedBy.avatarPath"
              :name="row.echoMap&&row.echoMap.updatedBy &&row.echoMap.updatedBy.name"
            />
          </template>
        </vxe-column>
        <vxe-column title="联系人" field="liaisoner" width="100">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="row.echoMap&&row.echoMap.liaisoner &&row.echoMap.liaisoner.avatarPath"
              :name="row.echoMap&&row.echoMap.liaisoner &&row.echoMap.liaisoner.name"
            />
          </template>
        </vxe-column>
        <vxe-column field="phone" title="联系电话" width="120" />
        <vxe-column field="body" title="发布内容" />
        <vxe-column title="操作" fixed="right" align="center" width="120">
          <template #default="{ row }">
            <el-tooltip v-if="row.state!== 1" class="item" content="发布" placement="top">
              <el-button type="text" icon="iconfont el-icon-application-release" @click="e=>openPublish(row)" />
            </el-tooltip>
            <el-tooltip v-else class="item" content="撤销" placement="top">
              <el-button type="text" icon="iconfont el-icon-application-undo" @click="e=>allBackout(row,2)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button :disabled="row.state == 1 ? true : false" type="text" icon="iconfont el-icon-icon-system-edit" @click="e=>editClickRow(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button :disabled="row.state == 1 ? true : false" type="text" icon="iconfont el-icon-icon-system-delete" @click="e=>deleteRow(row)" />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableList" />
    <addEditDialog v-if="addEditDialog" v-model="addEditDialog" :title="title" :click-row="clickRow" @success="getTableList" />
    <addUserDialog v-if="publishParam.visible" v-model="publishParam.visible" v-bind="publishParam" @success="getTableList" />

    <!-- 详情 -->
    <publicInfo v-if="publicInfoParam.visible" v-bind="publicInfoParam" v-model="publicInfoParam.visible" />
  </div>
</template>
<script>
import {
  getNoticeList,
  delNotice,
  editState
} from '@/api/vone/base/notice'

import { apiBaseDictPage } from '@/api/vone/base/dict'

import addEditDialog from './add-edit-dialog.vue'
import addUserDialog from './add-user-dialog.vue'
import publicInfo from './publicInfo.vue'

export default {
  name: 'PublicManagement',
  components: {
    addUserDialog,
    addEditDialog,
    publicInfo
  },
  data() {
    return {
      extraData: {}, defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        }
      ],
      publishParam: { visible: false },
      publicInfoParam: { visible: false },

      formData: {},
      tableData: {
        records: []
      },
      tableLoading: false,
      addEditDialog: false,
      title: '新增公告',
      clickRow: {},
      notifyTypeMap: {},
      actions: [{
        name: '批量发布',
        fn: e => this.allPublish()
      }, {
        name: '批量撤销',
        fn: e => this.allBackout('', 2, 'batch')
      }, {
        name: '批量删除',
        fn: e => this.deleteRow()
      }]
    }
  },
  mounted() {
    this.getNotificTypes()
  },
  methods: {
    // 查询公告类型
    async getNotificTypes() {
      const res = await apiBaseDictPage({
        size: 999,
        current: 1,
        extra: {},
        model: {
          type: 'NOTICE_TYPE'
        }
      })
      if (res.isSuccess) {
        res.data.records.map(item => {
          item.label = item.name
          item.value = item.code
        })

        // this.formFields[2].valueItems = res.data.records
        this.notifyTypeMap = res.data.records.reduce((acc, cur) => {
          acc[cur.code] = cur.name
          return acc
        }, { '1': '系统升级' })
      }
    },
    async getTableList() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination.exportPages()
      const params = {
        ...pageObj,
        extra: {
          sql: this.formData.conditionsSqlData,

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await getNoticeList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    addNotice() {
      this.addEditDialog = true
      this.title = '新增公告'
      this.clickRow = {}
    },
    // 编辑引擎
    editClickRow(row) {
      this.clickRow = row
      this.addEditDialog = true
      this.title = '编辑公告'
    },
    async deleteRow(row) {
      let Ids = []
      if (row && row.id) {
        Ids = [row.id]
      } else {
        Ids = this.getVxeTableSelectData('noticeTable').map(r => r.id)
      }
      if (Ids.length == 0) {
        this.$message.warning('请选择数据')
        return
      }
      await this.$confirm(`确定删除吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
      const res = await delNotice(Ids)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableList()
    },
    openPublish(row) {
      this.publishParam = { visible: true, row: row }
    },
    allPublish() {
      const chooseData = this.getVxeTableSelectData('noticeTable')

      if (!chooseData.length) {
        return this.$message.warning('请选择数据')
      }

      const publiseData = chooseData.filter(r => r.state == 1).map(r => r.code)

      if (publiseData.length) {
        return this.$message.warning(`【${publiseData.join(',')}】状态为已发布,已发布的数据不能再次发布`)
      }

      this.publishParam = { visible: true, data: chooseData, type: 'batch' }
    },
    async allBackout(row, type, isBatch) {
      const chooseData = this.getVxeTableSelectData('noticeTable')

      if (isBatch) {
        const noData = chooseData.filter(r => r.state == 0).map(r => r.code)
        const backData = chooseData.filter(r => r.state == 2).map(r => r.code)
        if (noData.length) {
          return this.$message.warning(`【${noData.join(',')}】状态为暂存,暂存的数据不能撤销`)
        }
        if (backData.length) {
          return this.$message.warning(`【${backData.join(',')}】状态为已撤销,已撤销的数据不能再次撤销`)
        }
      }

      await this.$confirm(`确定撤销吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })

      const Ids = isBatch ? chooseData.map(r => r.id) : [row.id]
      const res = await editState(
        {
          ids: Ids,
          state: 2
        }
      )
      if (!res.isSuccess) {
        return this.$message.warning(res.msg)
      }
      this.$message.success('操作成功')
      this.getTableList()
    },
    showInfo(row) {
      this.publicInfoParam = { visible: true, id: row.id }
    }
  }
}
</script>
<style lang="scss" scoped>

</style>

