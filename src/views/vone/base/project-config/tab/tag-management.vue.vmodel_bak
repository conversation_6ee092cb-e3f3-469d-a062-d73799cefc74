<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px">
      <template slot="custom">
        <span class="strong">标签管理</span>
      </template>
      <template slot="moreSearch">
        <tagSelect v-model="tagSelectName" style="width:280px" @filterSelect="getList" />
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('alm_tag_add')" @click="add">
          新增
        </el-button>
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 148px)">
      <vxe-table
        ref="xTree"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :tree-config="{transform: true, rowField: 'id', parentField: 'parentId'}"
        :empty-render="{ name: 'empty' }"
        :data="data"
        row-id="id"
        @toggle-tree-expand="toggleExpandChangeEvent"
      >
        <vxe-column title="标签名称" field="name" tree-node fixed="left">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </vxe-column>
        <vxe-column title="创建人" field="createdBy" width="150">
          <template #default="scope">
            <vone-user-avatar :avatar-path="scope.row.echoMap && scope.row.echoMap.createBy.avatarPath" :name="scope.row.echoMap &&scope.row.echoMap.createBy.name" />
          </template>
        </vxe-column>
        <vxe-column title="总数" field="nums" width="150">
          <template #default="scope">
            {{ scope.row.echoMap.nums }}
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" width="120" align="left">
          <template #default="scope">
            <template>
              <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
                <el-button type="text" size="mini" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('alm_tag_add')||scope.row.level==3" @click.stop="() => add(scope)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                <el-button type="text" size="mini" icon="iconfont el-icon-application-edit" :disabled="!$permission('alm_tag_put')" @click.stop="() => nodeClick(scope.row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                <el-button type="text" size="mini" icon="iconfont el-icon-application-delete" :disabled="!$permission('alm_tag_del')" @click.stop="() => remove(scope.row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-dialog v-if="visible" :title="title" v-model:visible="visible" width="30%" :before-close="onClose" :close-on-click-modal="false">
      <el-form ref="treeFormRef" :model="treeForm" :rules="rulesFormRules" label-width="80px">
        <el-form-item v-if="title == '新增标签'" label="层级" class="nameCls">
          <el-input v-model.trim="level" style="margin-right: 10px" disabled />
        </el-form-item>
        <el-form-item label="名称" prop="name" class="nameCls">
          <el-input v-model.trim="treeForm.name" style="margin-right: 10px" placeholder="请输入名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { apiAlmTagPage, apiAlmTabDrag, issueTagDel, issueTagAddOrEdit } from '@/api/vone/alm/index'
import tagSelect from '@/components/CustomEdit/components/tag-select'

export default {

  components: {
    tagSelect
  },
  data() {
    return {
      level: '',
      title: '',
      visible: false,
      treeLoading: false,
      treeForm: {},
      tagSelectName: '', // 标签选择名称
      rulesFormRules: {
        name: [
          { required: true, message: '请输入标签', trigger: 'blur' },
          {
            pattern: '[^ ]+',
            message: '标签不能为空格'
          },
          {
            pattern: '^([a-zA-Z0-9\u4e00-\u9fa5\-.]){1,10}$',
            message: '请输入不超过10个字母、数字或横线(-)组成的标识'
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾'
          }
        ]
      },
      data: [],
      arr: [],
      copyData: []
    }
  },
  mounted() {
    this.getList()
  },

  methods: {

    // 查询标签列表
    toggleExpandChangeEvent() {

    },
    async getList() {
      this.treeLoading = true
      var paramsName = this.tagSelectName
      if (params) {
        if (params.indexOf('/') != -1) { // 包含
          paramsName = this.tagSelectName.split('/')
        } else {
          paramsName = this.tagSelectName
        }
      }

      const params = {
        current: 1,
        extra: {},
        model: {
          name: [paramsName] || []
        },
        order: 'descending',
        size: 100000,
        sort: 'id'
      }
      const res = await apiAlmTagPage(params)
      if (res.isSuccess) {
        // const tagsList = res.data.records.filter(item => item.id !== '0')

        // tagsList.sort((a, b) => a.sort - b.sort)
        // this.data = list2Tree(tagsList, { parentKey: 'parentId' })
        this.data = res.data.records
      } else {
        this.$message.warning(res.msg)
      }
      this.treeLoading = false
    },
    onClose() {
      this.visible = false
    },
    handleDragStart() {
      this.copyData = JSON.parse(
        JSON.stringify(this.data)
      )
    },
    collapse(draggingNode, dropNode, type) {
      if (draggingNode.data.name == dropNode.data.name) {
        return type === 'inner'
      } else {
        return true
      }

      // if (this.getDepth(draggingNode, 1) == 3) {
      //   if (dropNode.level == 1) {
      //     return type === 'prev' || type === 'next'
      //   } else if (dropNode.level == 2) {
      //     return false
      //   } else {
      //     return false
      //   }
      // }
      // if (this.getDepth(draggingNode, 1) == 2) {
      //   if (dropNode.level == 3) {
      //     return false
      //   } else if (dropNode.level == 2) {
      //     return type === 'prev' || type === 'next'
      //   } else {
      //     return true
      //   }
      // }
      // if (dropNode.level == 3 || (draggingNode.childNodes && draggingNode.childNodes.length > 0)) {
      //   return type === 'prev' || type === 'next'
      // } else {
      //   return true
      // }
    },
    getDepth(obj, k) {
      let depth = k
      if (obj.childNodes && obj.childNodes.length > 0) {
        obj.childNodes.forEach((v) => {
          if (v.childNodes && v.childNodes.length > 0) {
            depth = this.getDepth(v, k + 1)
          }

          // if(!v.childNodes)return
        })
      }
      return depth
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        id: draggingNode.data.id,
        parentId: draggingNode.data.parentId,
        // preOrgId: this.getChange(dropNode.parent.childNodes, draggingNode.data.id),
        newParentId: dropType == 'inner' ? dropNode.data.id : dropNode.parent.data.id || 0,
        sort: this.getChange(dropNode.parent.childNodes, draggingNode.data.id)

      }

      const res = await apiAlmTabDrag(obj).catch(() => {
        this.data = this.copyData
      })

      if (!res.isSuccess) {
        this.$message.success(res.msg)
        return
      }
      this.$message.success('移动成功')
    },
    getChange(arr, id) {
      var proOrgId = 0
      arr.forEach((item, i) => {
        if (item.data.id == id) {
          proOrgId = i
        }
      })
      return proOrgId
    },
    add(scope) {
      this.arr = []
      this.title = '新增标签'
      this.treeForm = {

      }
      this.visible = true
      this.treeForm.parentId = scope.row ? scope.row.id : 0
      if (scope) {
        this.level = this.getName(scope.row)
      } else {
        this.level = '/'
      }
    },
    // 获取name
    getName(val, data) {
      if (val && val.name) {
        this.arr.unshift(val.name)
      }
      if (val && val.level !== 0) {
        this.getName(this.$refs.xTree.getParentRow(val.id))
      }
      return this.arr.toString().replaceAll(',', '/')
    },
    async nodeClick(data) {
      this.arr = []
      this.title = '编辑标签'
      // 深拷贝数据
      this.treeForm = JSON.parse(JSON.stringify(data))
      this.visible = true
    },
    remove(data) {
      this.$confirm(`是否删除标签【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
        .then(() => {
          issueTagDel([data.id]).then((res) => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.getList()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => { })
    },
    async save() {
      try {
        await this.$refs.treeFormRef.validate()
        const res = await issueTagAddOrEdit(this.treeForm, this.title == '新增标签' ? 'post' : 'put')
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success(res.msg)
        this.getList()
        this.visible = false
      } catch (error) {
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.custom-tree-nodes {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 16px;
  overflow: hidden;
  .operation-icon {
    opacity: 1;
    .is-disabled {
      color: var(--input-border-color);
    }
  }
}
.tableHead {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 76px;
  padding-left: 28px;
  height: 48px;
  line-height: 48px;
  color: var(--main-font-color);
  background-color: var(--bottom-bg-color);
}
</style>
<style lang='scss'>
.treeTag {
	height: calc(100vh - 236px);
  overflow-y: auto;
  .el-tree-node:focus > .el-tree-node__content {
    background-color: inherit;
    &:hover {
      background-color: var(--hover-bg-color);
    }
  }
  .el-tree-node__content {
    position: relative;
    height: 42px;
    color: var(--main-font-color);
    border-bottom: 1px solid var(--disabled-bg-color);
    & > .el-tree-node__expand-icon {
      font-size: 16px;
      color: var(--main-second-color);
    }
  }
  .el-tree-node__children {
    background-color: var(--node-cildren-bg-color);
  }

  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
}
.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
  margin-right: 16px;
}

</style>
