<template>
  <div>
    <form-page v-if="showConfig || $route.query.showConfig" :type-classify="typeClassify || this.$route.query.typeClassify" :form-id="formId" :type-name="typeName" :view-type="viewType" @back="back" />
    <div v-else>
      <vone-search-wrapper style="margin-bottom: 16px;">
        <template slot="custom">
          <span class="strong">表单配置</span>
        </template>
        <template slot="actions">
          <el-button :disabled="!$permission('work_priority_delete')" @click="deleteAll">
            批量删除
          </el-button>
        </template>
      </vone-search-wrapper>
      <div :style="{height: tableHeight}">
        <vxe-table
          ref="form-table"
          class="vone-vxe-table"
          border
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ resizable: true, minWidth: 120 }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column type="checkbox" width="37" align="center" fixed="left" />
          <vxe-column title="名称" field="name" fixed="left">
            <template #default="scope">
              <a @click="checkRow(scope.row)">
                {{ scope.row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="分类标识" field="bizType" width="120">
            <template #default="scope">
              {{ scope.row.bizType }}
            </template>
          </vxe-column>
          <vxe-column title="是否被使用" field="isUse" width="120">
            <template #default="scope">
              {{ scope.row.isUse ? '是' : '否' }}
            </template>
          </vxe-column>
          <vxe-column title="内置" field="readonly" width="120">
            <template #default="scope">
              {{ scope.row.readonly ? '是' : '否' }}
            </template>
          </vxe-column>
          <vxe-column title="描述" field="description" width="200" />
          <vxe-column title="操作" fixed="right" align="left" width="120">
            <template #default="{ row }">
              <template>
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button type="text" :disabled="!$permission('work_form_info_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editRow(row)" />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="配置" placement="top">
                  <el-button type="text" :disabled="row.readonly || !$permission('work_form_config')" icon="iconfont el-icon-application-step-setting icon_click" @click="configRow(row)" />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                  <el-button type="text" icon="iconfont el-icon-application-more" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="iconfont el-icon-application-copy" :disabled="false" :command="() =>copyRow(row)">
                      <span>复制</span>
                    </el-dropdown-item>
                    <el-dropdown-item icon="iconfont el-icon-application-delete" :disabled="row.readonly || row.isUse || !$permission('work_form_del')" :command="() =>delRow(row)">
                      <span>删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
      <!-- 新增表单 -->
      <el-dialog :title="title" width="30%" v-model:visible="dialogFormVisible" :close-on-click-modal="false">
        <el-form ref="form" v-loading="formLoading" :rules="rules" :model="priorityForm">

          <el-form-item label="名称" prop="name">
            <el-input v-model="priorityForm.name" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="适用分类" prop="bizType">
            <el-select v-model="priorityForm.bizType" clearable filterable :disabled="title == '编辑表单' ? true : false">
              <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

          </el-form-item>
          <!-- <el-form-item label="复制自" prop="copy">
            <el-input v-model="priorityForm.copy" placeholder="请输入名称" />
          </el-form-item> -->

          <el-form-item label="描述" prop="description">
            <el-input v-model="priorityForm.description" type="textarea" placeholder="请输入描述" />
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="sureAdd">确定</el-button>
        </div>
      </el-dialog>

      <!-- 复制表单 -->
      <el-dialog title="复制表单" width="30%" v-model:visible="copyFormVisible" :close-on-click-modal="false">
        <el-form ref="copyForm" :rules="copyRules" :model="copyForm">

          <el-form-item label="名称" prop="name">
            <el-input v-model="copyForm.name" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="copyForm.bizType" clearable filterable disabled>
              <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="copyForm.description" type="textarea" placeholder="请输入描述" />
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="onCopyFormClose()">取消</el-button>
          <el-button type="primary" :loading="copyLoading" @click="copyAdd">确定</el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import { getAlmGetPriorityId } from '@/api/vone/alm/index'
import { apiVaBaseCustomPage, apiVaBaseCustomFormCopy, apiVaBaseCustomFormPut, apiVaBaseCustomFormDel } from '@/api/vone/base/customForm'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import { apiBaseFormProperty } from '@/api/vone/base/index'

import formPage from './common/form-page.vue'

export default {
  components: {
    formPage
  },
  props: {
    id: {
      type: String,
      default: undefined
    },
    typeCode: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      typeClassify: undefined,
      showConfig: false,
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'blur'
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'blur'
          }
        ],

        description: [
          {
            max: 250,
            message: '请输入长度不超过250个字符的描述',
            trigger: 'blur'
          }
        ]
      },
      copyRules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'blur'
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'blur'
          }
        ],
        bizType: [
          {
            required: true,
            message: '请选择业务类型'
          }
        ],
        description: [
          {
            max: 250,
            message: '请输入长度不超过250个字符的描述',
            trigger: 'blur'
          }
        ]

      },
      tableLoading: false,
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        operation: {
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '140', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editRow, // 操作事件
              disabled: this.getStatus
            },
            {
              type: 'icon', // 为icon则是图标
              label: '配置', // 功能名称
              icon: 'iconfont el-icon-application-step-setting', // icon class
              handler: this.configRow, // 操作事件
              disabled: this.getConfig
            }

          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [
            {
              type: 'icon', // 为icon则是图标
              label: '复制', // 功能名称
              icon: 'iconfont el-icon-application-copy', // icon class
              handler: this.copyRow, // 操作事件
              disabled: false
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.delRow, // 操作事件
              disabled: this.getDel
            }
          ]
        }
      },
      title: '',
      tableData: {
      },
      selecteTableData: [],
      dialogFormVisible: false,
      priorityForm: {
        code: '',

        description: '',
        icon: 'el-icon-eleme',

        readonly: false
      },
      saveLoading: false,
      formData: {},
      typeList: [],
      copyForm: {},
      copyFormVisible: false,
      copyLoading: false,
      formId: undefined,
      typeName: '',
      viewType: false, // 判断是配置页还是详情页
      formLoading: false
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    selectable(row) {
      if (row.readonly || row.isUse) {
        return false
      } else {
        return true
      }
    },
    getStatus(index, row) {
      return !this.$permission('work_form_info_edit')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || this.tableData.records[index].isUse || !this.$permission('work_form_del')
    },

    getConfig(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('work_form_config')
    },
    async getTypeList() {
      const res = await apiBaseDictEnumList(['TypeClassify'])

      if (!res.isSuccess) {
        return
      }
      this.typeList = res.data.TypeClassify
    },
    async getTableData() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiVaBaseCustomPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    addPriority() {
      this.title = '新增表单'
      this.dialogFormVisible = true
    },
    editRow(row) {
      this.getTypeList()
      this.title = '编辑表单'
      this.getFormInfo(row)
      this.dialogFormVisible = true
    },
    async getFormInfo(row) {
      this.formLoading = true
      const res = await apiBaseFormProperty(
        row.id
      )
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.priorityForm = res.data
    },
    // 查看表单项
    checkRow(row) {
      this.viewType = true
      this.typeClassify = row.bizType || this.$route.query.typeClassify
      this.formId = row.id
      this.showConfig = true
      this.typeName = row.name
    },
    // 配置表单项
    configRow(row) {
      this.viewType = false
      this.typeClassify = row.bizType
      this.formId = row.id
      this.showConfig = true
      this.typeName = row.name
    },
    // 复制
    copyRow(row) {
      this.getTypeList()
      this.formId = row.id

      // this.$set(this.copyForm, 'name', null)
      // this.$set(this.copyForm, 'description', null)
      this.$set(this.copyForm, 'bizType', row.bizType)
      this.copyFormVisible = true
    },
    back() {
      this.showConfig = false

      this.$nextTick(() => {
        this.getTableData()
      })

      if (this.$route.query) {
        this.$router.push('/base/setting/view')
      }
    },
    async getInfo(row) {
      this.formLoading = true
      const res = await getAlmGetPriorityId(
        row.id
      )
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.priorityForm = res.data
    },
    async sureAdd() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      this.saveLoading = true
      const res = await apiVaBaseCustomFormPut(
        this.priorityForm
      )
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.dialogFormVisible = false
      this.getTableData()
    },

    delRow(item) {
      this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      }).then(async() => {
        const res = await apiVaBaseCustomFormDel(
          [item.id]
        )
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('删除成功')
        this.getTableData()
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('form-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '批量删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('form-table').map(r => r.id)
        const res = await apiVaBaseCustomFormDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    async copyAdd() {
      try {
        await this.$refs.copyForm.validate()
      } catch (e) {
        return
      }
      try {
        this.copyLoading = true
        this.$set(this.copyForm, 'id', this.formId)
        const res = await apiVaBaseCustomFormCopy(
          this.copyForm
        )
        this.copyLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('复制成功')
        this.onCopyFormClose()
        this.getTableData()
      } catch (e) {
        this.copyLoading = false
      }
    },
    onCopyFormClose() {
      this.$refs.copyForm.resetFields()
      this.copyFormVisible = false
    }
  }

}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
}

</style>
