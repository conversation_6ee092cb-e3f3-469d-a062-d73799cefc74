<template>
  <!-- 编辑预览 -->
  <div class="drawerBox">
    <el-dialog top="4vh" v-model:visible="visible" width="80%" :before-close="onClose" size="lg" :close-on-click-modal="false">

      <div slot="title">
        <div>{{ title }}【
          <span class="drawerCode">
            {{ fixedForm.code }}
          </span>
          】

        </div>
      </div>

      <div v-loading="drawerLoading">

        <el-form ref="fixedForm" :model="fixedForm" :rules="fixdFormRules" label-position="top" disabled>
          <div class="basicHeader">

            <el-row v-for="item in fixedProperty.filter(r=>r.key == 'name')" :key="item.id" class="name-row">
              <el-col :span="24">
                <!-- 输入框 -->
                <el-input v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
              </el-col>

            </el-row>

            <el-row :gutter="24" type="flex" justify="space-between">
              <el-col v-for="item in fixedProperty.filter(r=>r.key !='name')" :key="item.id" class="el-col-6">

                <div class="fixedItem">
                  <!-- 图标 -->
                  <span>
                    <svg class="icon" aria-hidden="true" style="font-size:42px">
                      <use :xlink:href="`#${iconMap[item.key]}`" />
                    </svg>
                  </span>

                  <el-form-item :label=" item.key != 'name' ? item.name : null" :prop="item.key">
                    <!-- 人员组件 -->

                    <span v-if="item.type == 'USER' || item.key == 'tagId'" class="ml-10">

                      {{ fixedForm[item.key] && fixedForm.echoMap[item.key] ? fixedForm.echoMap[item.key].name : "未设置" }}
                    </span>

                    <!-- 输入框 -->
                    <el-input v-else-if="item.type == 'INPUT'" v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />

                    <!-- 状态 -->
                    <span v-else-if="item.key == 'stateCode'" class="ml-10">
                      {{ fixedForm[item.key] && fixedForm.echoMap[item.key] ? fixedForm.echoMap[item.key].name : "测试中" }}
                    </span>

                    <!-- 下拉框 -->
                    <span v-else-if="item.type == 'SELECT'">
                      <el-select v-if="item.key != 'stateCode'" v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate">
                        <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'planIds' ? i.id : i.code">

                          <span v-if="item.key == 'typeCode'">
                            <i :class="['iconfont', `${i.icon}`]" :style="{'color':i.color}" />
                            {{ i.name }}

                          </span>
                        </el-option>
                      </el-select>
                    </span>

                    <!-- 日期组件 -->
                    <el-date-picker v-else-if="item.type == 'DATE'" v-model="fixedForm[item.key]" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${item.defaultTime}:00`" :placeholder="item.placeholder" :disabled="!item.isUpdate" />

                  </el-form-item>
                </div>

              </el-col>
            </el-row>
          </div>
        </el-form>
        <el-row>
          <el-col :span="16" class="centerBox">
            <el-tabs v-model="tabActive" tab-position="left" class="tabHeight">
              <el-tab-pane label="基本信息" class="contentBox" name="basic">
                <vone-div-wrapper :title="'基本信息'">
                  <div v-loading="infoLoading" class="centerBasic">
                    <el-form ref="otherForm" :model="otherForm" label-position="top">
                      <el-row>
                        <el-col v-for="item in basicProperty" :key="item.id" :span="24">
                          <el-form-item :label="item.name" :prop="item.key">
                            <!-- 文本编辑器 -->
                            <span v-if="item.type == 'EDITOR'">

                              <vone-editor ref="editor" v-model="otherForm[item.key]" :disabled="!item.isUpdate" />

                            </span>

                            <!-- 文件 -->
                            <vone-upload v-else-if="item.type == 'FILE'" ref="uploadFile" :biz-type="fileMap[typeCode]" :files-data="otherForm.files" :disabled="!item.isUpdate" />

                          </el-form-item>

                        </el-col>
                      </el-row>
                    </el-form>

                    <div class="rowLine" />

                    <el-form ref="customForm" :model="customForm" label-position="top" :rules="formRules">
                      <el-row :gutter="24" type="flex" class="row-box">
                        <el-col v-for="item in customList" :key="item.id" :span="12">
                          <el-form-item :label="item.name" :prop="item.key">
                            <!-- 人员组件 -->
                            <vone-remote-user v-if="item.type == 'USER'" v-model="customForm[item.key]" :disabled="!item.isUpdate" :multiple="item.multiple" />

                            <!-- 文件 -->
                            <vone-upload v-else-if="item.type == 'FILE'" ref="formUploadFile" :files-data="customForm[item.key]" :biz-type="fileMap[typeCode]" :disabled="!item.isUpdate" />

                            <!-- 项目人员组件 -->
                            <projectRemoteUser v-if="item.type == 'PROJECTUSER'" v-model="customForm[item.key]" :multiple="item.multiple" :disabled="!item.isUpdate" />

                            <!-- 整数 -->
                            <el-input-number v-else-if="item.type == 'INT'" v-model="customForm[item.key]" :min="1" :max="1000" :disabled="!item.isUpdate" controls-position="right" style="width:100%" :placeholder="item.placeholder" :precision="item.precision" />

                            <!-- 日期组件 -->
                            <el-date-picker v-else-if="item.type == 'DATE'" v-model="customForm[item.key]" prefix-icon="el-icon-date" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${item.defaultTime}:00`" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                            <!-- 输入框 -->
                            <el-input v-if="item.type == 'INPUT'" v-model="customForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                            <!-- 组织机构 -->
                            <vone-tree-select v-else-if="item.type == 'ORG'" v-model="customForm[item.key]" search-nested :tree-data="orgData" placeholder="请选择机构" :disabled="!item.isUpdate" :multiple="item.multiple" />

                            <!-- 输入文本框 -->
                            <el-input v-else-if="item.type == 'TEXTAREA'" v-model="customForm[item.key]" type="textarea" :placeholder="item.placeholder" :disabled="!item.isUpdate" />

                            <!-- 下拉单选框 -->
                            <el-select v-else-if="item.type == 'SELECT' && !item.multiple" v-model="customForm[item.key]" :placeholder="item.placeholder" clearable filterable :disabled="!item.isUpdate">
                              <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'sourceCode' || item.key == 'typeCode' || item.key == 'priorityCode' ? i.code : i.id">
                                <span v-if="item.key == 'ideaId'">{{ `${i.code}  ${i.name}` }}</span>

                                <span v-if="item.key == 'productId'">
                                  {{ i.name }}
                                  <!-- <span v-if="i.echoMap" style="float:right">
                                    <el-tag v-if="i.echoMap.isHost" type="success">
                                      主
                                    </el-tag>
                                    <el-tag v-if="i.echoMap.isHost == false" type="warning">
                                      辅
                                    </el-tag>
                                  </span> -->

                                </span>
                              </el-option>
                            </el-select>
                            <!-- 下拉多选框 -->
                            <el-select v-else-if="item.type == 'SELECT' && item.multiple" v-model="customForm[item.key]" :placeholder="item.placeholder" multiple clearable filterable :disabled="!item.isUpdate">
                              <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.id" />
                            </el-select>
                            <dataSelect v-else-if="item.type == 'LINKED'" text-info="add" v-model:model="customForm[item.key]" :config="item" :placeholder="item.placeholder" @change="dataSelectChange($event,item.key)" />
                            <div v-else-if="item.type == 'QUOTE'">
                              <vone-user-avatar v-if="item.quoteType === 'user'" :avatar-path="item.user.avatarPath" :avatar-type="true" height="22px" width="22px" :name="item.user.name" :show-name="true" />
                              <el-input v-else v-model="customForm[item.key]" disabled placeholder="" />
                            </div>
                          </el-form-item>

                        </el-col>
                      </el-row>
                    </el-form>
                  </div>
                </vone-div-wrapper>

              </el-tab-pane>

            </el-tabs>
          </el-col>

          <el-col :span="8" class="rightBox">
            <el-tabs v-model="rightTabActive">
              <el-tab-pane v-for="(tab,index) in rightTabs" :key="index" :label="tab.label" :name="tab.name">
                <vone-empty class="empty" />
              </el-tab-pane>
            </el-tabs>
          </el-col>

        </el-row>
      </div>

      <div slot="footer">

        <el-button @click="onClose">取消</el-button>
        <!-- <el-button type="primary" disabled>保存</el-button> -->

      </div>

    </el-dialog>
  </div>
</template>

<script>
const iconMap = {
  handleBy: 'el-icon-icon-dark-avatar',
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  typeCode: 'el-icon-icon-yixiang',
  stateCode: 'el-icon-icon-fill-zhuangtai'
}
const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD'
}
import {
  apiAlmSourceNoPage
} from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmTypeNoPage
} from '@/api/vone/alm/index'
import { apiBaseFormProperty } from '@/api/vone/base/index'

import storage from 'store'
import dataSelect from '@/components/CustomEdit/components/data-select'
import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'

import _ from 'lodash'
export default {
  name: 'VoneCustomEdit',
  components: {
    dataSelect,
    projectRemoteUser
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },

    typeCode: {
      type: String,
      default: undefined
    },
    leftTabs: {
      type: Array,
      default: () => []
    },
    rightTabs: {
      type: Array,
      default: () => []
    },
    formId: {
      // 平台配置,预览表单接口,需要根据formId查询模板
      type: String,
      default: undefined
    }
  },
  data: function() {
    return {
      drawerLoading: false,
      fileMap,
      iconMap,
      fileList: [],
      fileId: [],
      tabActive: 'basic',
      rightTabActive: 'active',
      pageLoading: false,
      customForm: {},
      fixedForm: {},
      otherForm: {},
      formRules: {},
      fixdFormRules: {},
      basicProperty: [],
      fixedProperty: [],
      customList: [],
      infoLoading: false,
      orgData: [] // 组织机构
    }
  },
  async mounted() {
    await this.getFormProperty()
    await this.getSelectSource()
  },

  methods: {
    dataSelectChange({ item, list, user }, key) {
      this.customList.forEach(e => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.customForm[e.key] = item[config.relationField]
            const obj = list.find(e => e.id == config.relationField && !e.primary)
            e.quoteType = obj.type
            if (e.quoteType == 'user') {
              e.user = user[item[config.relationField]]
            }
          }
        }
      })
    },
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })
    },

    async getFormProperty() {
      this.drawerLoading = true

      try {
        this.drawerLoading = true
        this.formRules = {}
        const res = await apiBaseFormProperty(this.formId)
        this.drawerLoading = false
        if (!res.isSuccess) {
          return
        }

        if (!res.data) return

        // 所有固定属性
        const all =
          res.data.customFormFields && res.data.customFormFields.length
            ? res.data.customFormFields.filter((r) => r.isBasic && r.state)
            : []

        all.forEach((element) => {
          element.placeholder = JSON.parse(element.config)?.placeholder
          element.multiple = JSON.parse(element.config)?.multiple
          element.message = JSON.parse(element.config)?.message
          element.validator = JSON.parse(element.config)?.validator
          element.max = JSON.parse(element.config)?.max
          element.defaultTime = JSON.parse(element.config)?.defaultTime
          element.options =
            !element.isBuilt
              ? JSON.parse(element.config)?.options
              : []
          element.type = element.type.code
          element.precision = JSON.parse(element.config)?.precision
        })

        // 其它基本属性
        const other = all.filter(
          (r) => r.key == 'files' || r.key == 'description'
        )
        // 固定基本属性
        const fixed = _.difference(all, other)

        this.fixedProperty = fixed.sort(function(a, b) {
          return a.sort - b.sort
        })

        this.basicProperty = other.sort(function(a, b) {
          return a.sort - b.sort
        })

        // 固定属性的校验规则
        const fixedRoule = this.fixedProperty.map((r) => ({
          required: r.isRequired,
          message: r.message,
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : 'string'
        }))

        fixedRoule.forEach((item) => {
          if (!this.fixdFormRules[item.key]) {
            this.fixdFormRules[item.key] = [item]
          } else {
            this.fixdFormRules[item.key].push(item)
          }
        })

        // 自定义属性
        const custom =
          res.data.customFormFields && res.data.customFormFields.length
            ? res.data.customFormFields.filter((r) => !r.isBasic && r.state)
            : []

        custom.forEach((element) => {
          element.placeholder = JSON.parse(element.config)?.placeholder
          element.multiple = JSON.parse(element.config)?.multiple
          element.message = JSON.parse(element.config)?.message
          element.validator = JSON.parse(element.config)?.validator
          element.defaultTime = JSON.parse(element.config)?.defaultTime
          element.options =
            !element.isBuilt
              ? JSON.parse(element.config)?.options
              : []
          element.type = element.type.code
        })

        // 排序
        this.customList = custom.sort(function(a, b) {
          return a.sort - b.sort
        })

        // 自定义属性的校验规则
        const advanceRoule = this.customList.map((r) => ({
          required: r.isRequired,
          message: r.message,
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : r.type == 'INT' ? 'number' : 'string'
        }))

        advanceRoule.forEach((item) => {
          if (!this.formRules[item.key]) {
            this.formRules[item.key] = [item]
          } else {
            this.formRules[item.key].push(item)
          }
        })

        const userInfo = storage.get('user')

        this.fixedForm = {
          id: new Date(),
          code: `${this.typeCode}-xxx`,
          name: '这里名称XXXXXXXXXXXXXXX',

          expectedTime: new Date(),
          planEtime: new Date(),
          stateCode: ''
        }
        this.otherForm = {
          description: '这里描述'
        }
        this.customForm = {
          putBy: userInfo.id,
          leadingBy: userInfo.id,
          planStime: new Date(),
          estimatePoint: 1,
          tag: []
        }

        // }
      } catch (e) {
        this.pageLoading = false
      }
      this.pageLoading = false
    },
    async getSelectSource() {
      this.getSourceList()
      this.getPrioritList()
      this.getIssueType()
    },

    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'sourceCode', res.data)

      // 默认值
      if (res.data && res.data.length) {
        this.$set(this.customForm, 'sourceCode', res.data[0].code)
      }
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'priorityCode', res.data)
      // 默认值
      if (res.data && res.data.length) {
        this.$set(this.customForm, 'priorityCode', res.data[0].code)
      }
    },

    // 查询需求分类
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
      // 默认值
      if (res.data && res.data.length) {
        this.$set(this.fixedForm, 'typeCode', res.data[0].code)
      }
    },

    onClose() {
      this.$emit('update:visible', false)
    },

    changeFlow() {
      this.$emit('success')
      this.onClose()
    }

  }
}
</script>

<style lang="scss" scoped>
:deep(.el-tabs--left .el-tabs__header.is-left) {
  margin-right: 0;
}
.drawerCode {
  color: var(--auxiliary-font-color);
}
:deep(.el-dialog .el-dialog__body) {
  padding: 0;
}

.basicHeader {
  background-color: var(--node-cildren-bg-color);
  min-height: 130px;
  padding: 20px;
  border-bottom: 1px solid var(--disabled-bg-color);
  margin: 0 !important;

  .el-col-6 {
    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 42px;
        margin-right: 10px;
      }
    }
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      & :focus {
        background: var(--main-bg-color);
      }
    }
    :deep(.el-input__suffix) {
      display: none;
    }

    :deep(.el-input--small) {
      font-size: 14px;
      margin-left: 5px;
    }

    :deep(.el-input.is-disabled .el-input__inner) {
      color: #000 !important;
      font-size: 14px;
    }
  }

  .name-row {
    margin-bottom: 16px;
    .el-col-24 {
      :deep(.el-input--small .el-input__inner) {
        border: none;
        background: none;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.centerBox {
  border-right: 1px solid var(--disabled-bg-color);

  .tabHeight {
    height: 60vh;
    :deep(.el-tabs__nav-scroll) {
      padding: 12px;
      .is-active {
        border-radius: 2px;
      }
      .el-tabs__active-bar {
        display: none;
      }
    }
    :deep(.el-tabs__content) {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  :deep(.el-tabs--left .el-tabs__nav-wrap.is-left::after) {
    width: 1px;
  }

  .contentBox {
    padding: 5px 16px;
  }
  :deep(.el-tabs__item.is-active) {
    background-color: var(--main-theme-color);
    color: var(--main-bg-color);
  }
  :deep(.el-tabs__item) {
    height: 36px;
    line-height: 36px;
    text-align: center;
    font-weight: 400;
    color: #202124;
  }
}

.rightBox {
  :deep(.el-tabs__nav-scroll) {
    padding-left: 15px;
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item.is-active) {
    color: var(--main-theme-color);
  }
}

.border {
  border-left: 4px solid var(--main-theme-color);
  padding-left: 10px;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
}

:deep(.el-tabs--border-card > .el-tabs__content) {
  padding: 0;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
:deep(.el-tabs__nav-wrap .is-top) {
  .el-tabs__nav-scroll {
    padding-left: 15px;
  }
}

.row-box {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}

.focusBlur {
  border: 1px solid var(--input-border-color);
  border-radius: 4px;
  padding: 1px 12px;
  min-height: 50px;
  max-height: 200px;
  overflow-y: auto;
}

.disabledDiv {
  cursor: not-allowed !important;
  pointer-events: none;
  background-color: var(--disabled-bg-color);
  border: none;
}
.ml-10 {
  margin-left: 13px;
}

.empty {
  margin-top: 35%;
}
</style>

