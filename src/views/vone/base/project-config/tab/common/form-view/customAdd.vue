<template>
  <!-- 新增预览 -->
  <div class="drawerBox">
    <el-dialog title="新增" top="6vh" v-model="visible" :before-close="onClose" :modal="true" size="md" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <div class="colBox">
        <el-skeleton :loading="pageLoading" style="width: 100%" animated>
          <template slot="template">
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div v-for="index in 3" :key="index" style="width: 190px;">
                <el-skeleton-item style="width: 50%; margin-bottom: 14px;" />
                <el-skeleton-item style="width: 90%;" />
              </div>
            </div>
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
          </template>
        </el-skeleton>
        <el-form v-if="fixedProperty.length" ref="fixdForm" :model="fixdForm" label-position="top" :rules="fixdFormRules">
          <el-row :gutter="12">

            <div v-for="item in fixedProperty" :key="item.id">
              <el-col :span="item.key =='name' || item.key =='description' || item.key =='files' || item.key =='tagId' ? 24 : 8">
                <el-form-item :label="item.name" :prop="item.key">
                  <!-- 平台人员组件 -->
                  <vone-remote-user v-if="item.type == 'USER'" v-model="fixdForm[item.key]" />

                  <!-- 标签 -->
                  <tagSelect v-else-if="item.type == 'SELECT' && item.multiple && item.key == 'tagId'" v-model="form[item.key]" multiple />
                  <!-- 日期组件 -->
                  <el-date-picker v-else-if="item.type == 'DATE'" v-model="fixdForm[item.key]" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" default-time="9:00:00" placeholder="选择计划开始时间" />

                  <!-- 输入框 -->
                  <el-input v-else-if="item.type == 'INPUT'" v-model="fixdForm[item.key]" :placeholder="item.placeholder" />
                  <!-- 输入文本框 -->
                  <el-input v-else-if="item.type == 'TEXTAREA'" v-model="fixdForm[item.key]" type="textarea" :placeholder="item.placeholder" />

                  <!-- 优先级 -->
                  <vone-icon-select v-else-if="item.type == 'SELECT' && item.key == 'priorityCode'" v-model="fixdForm[item.key]" filterable :data="prioritList" clearable style="width:100%">
                    <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                      <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                      {{ item.name }}
                    </el-option>
                  </vone-icon-select>
                  <!-- 下拉单选框 -->
                  <el-select v-else-if="item.type == 'SELECT' && !item.multiple " v-model="fixdForm[item.key]" :placeholder="item.placeholder" disabled>
                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'sourceCode' || item.key == 'typeCode' || item.key == 'priorityCode' || item.key == 'stateCode' ? i.code : i.id">
                      <span v-if="item.key == 'ideaId'">
                        {{ `${i.code}  ${i.name}` }}
                      </span>
                    </el-option>
                  </el-select>

                  <!-- 下拉多选框 -->
                  <el-select v-else-if="item.type == 'SELECT' && item.multiple" v-model="fixdForm[item.key]" :placeholder="item.placeholder" multiple>
                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.code" />
                  </el-select>

                  <!-- 文本编辑器 -->

                  <span v-else-if="item.type == 'EDITOR'">
                    <vone-editor ref="editor" v-model="fixdForm[item.key]" />

                  </span>
                  <!-- 文件 -->

                  <vone-upload v-else-if="item.type == 'FILE'" ref="uploadFile" :biz-type="fileMap[typeCode]" :files-data="fixdForm.files" />

                </el-form-item>
              </el-col>

            </div>
          </el-row>

        </el-form>
        <el-divider v-if="customList.length" />

        <h4 v-if="customList.length">属性</h4>

        <el-form v-if="customList.length" ref="form" :model="form" label-position="top" :rules="formRules">
          <el-row type="flex" :gutter="24" class="row-box">
            <el-col v-for="item in customList" :key="item.id" :span="12">
              <!-- <div> -->

              <el-form-item :label="item.name" :prop="item.key">
                <!-- 平台人员组件 -->
                <vone-remote-user v-if="item.type == 'USER'" v-model="form[item.key]" :multiple="item.multiple" />
                <!-- 项目人员组件 -->
                <projectRemoteUser v-if="item.type == 'PROJECTUSER'" v-model="form[item.key]" :multiple="item.multiple" />
                <!-- 整数 -->
                <el-input-number v-else-if="item.type == 'INT'" v-model="form[item.key]" :min="0.1" :max="1000" :precision="item.precision" controls-position="right" style="width:100%" :placeholder="item.placeholder" />

                <!-- 文件 -->
                <vone-upload v-else-if="item.type == 'FILE'" ref="formUploadFile" :files-data="form[item.key]" :biz-type="fileMap[typeCode]" />

                <!-- 日期组件 -->
                <el-date-picker v-else-if="item.type == 'DATE'" v-model="form[item.key]" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${item.defaultTime}:00`" :placeholder="item.placeholder" />
                <!-- 输入框 -->
                <el-input
                  v-if="item.type == 'INPUT'"
                  v-model="form[item.key]"
                  :placeholder="item.placeholder"
                  maxlength="200"
                  show-word-limit
                />
                <!-- 组织机构 -->
                <vone-tree-select v-else-if="item.type == 'ORG'" v-model="form[item.key]" search-nested :tree-data="orgData" placeholder="请选择机构" :multiple="item.multiple" />
                <!-- 输入文本框 -->
                <el-input v-else-if="item.type == 'TEXTAREA'" v-model="form[item.key]" autosize type="textarea" :placeholder="item.placeholder" />

                <!-- 下拉单选框 -->
                <el-select v-else-if="item.type == 'SELECT' && !item.multiple" v-model="form[item.key]" :placeholder="item.placeholder" clearable filterable :disabled="item.disabled">
                  <!-- 只有来源,分类,优先级传参得时候保存的是code,其它都是id -->
                  <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'sourceCode' || item.key == 'typeCode' || item.key == 'priorityCode' ? i.code : i.id">
                    <span v-if="item.key == 'requirementId' || item.key == 'ideaId' ">{{ `${i.code}  ${i.name}` }}</span>

                    <span v-if="item.key == 'productId'">
                      {{ i.name }}
                      <!-- <span v-if="i.echoMap" style="float:right">
                        <el-tag v-if="i.echoMap.isHost" type="success">
                          主
                        </el-tag>
                        <el-tag v-if="i.echoMap.isHost == false" type="warning">
                          辅
                        </el-tag>
                      </span> -->

                    </span>

                  </el-option>

                </el-select>

                <!-- 下拉多选框 -->
                <el-select v-else-if="item.type == 'SELECT' && item.multiple" v-model="form[item.key]" :placeholder="item.placeholder" multiple clearable filterable>
                  <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.id" />
                </el-select>
                <dataSelect v-else-if="item.type == 'LINKED'" ref="dataSelect" text-info="add" v-model:model="form[item.key]" :config="item" :placeholder="item.placeholder" @change="dataSelectChange($event,item.key)" />
                <div v-else-if="item.type == 'QUOTE'">
                  <vone-user-avatar v-if="item.quoteType === 'user'" :avatar-path="item.user.avatarPath" :avatar-type="true" height="22px" width="22px" :name="item.user.name" :show-name="true" />
                  <el-input v-else v-model="form[item.key]" disabled placeholder="" />
                </div>
              </el-form-item>

              <!-- </div> -->
            </el-col>
          </el-row>

        </el-form>
      </div>

      <div slot="footer" class="footer">

        <el-button @click="onClose">取消</el-button>
        <!-- <el-button type="primary" disabled>保存</el-button> -->

      </div>

    </el-dialog>
  </div>
</template>

<script>

import { apiBaseFormProperty } from '@/api/vone/base/index'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmTypeNoPage
} from '@/api/vone/alm/index'

import tagSelect from '@/components/CustomEdit/components/tag-select'
import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'
import dataSelect from '@/components/CustomEdit/components/data-select'
import storage from 'store'

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD'
}
export default {
  name: 'VoneCustomAdd',
  components: {
    tagSelect,
    dataSelect,
    projectRemoteUser
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },

    typeCode: {
      type: String,
      default: undefined
    },
    formId: {
      type: String,
      default: undefined
    }
  },
  data: function() {
    return {
      model: {
        labelPosition: 'top'
      },
      fileMap,
      fixdForm: {

        // files: []
      }, // 基本属性
      fixdFormRules: {}, // 固定属性校
      formRules: {},
      form: {
        estimateHour: 1
      },
      fixedProperty: [], // 固定属性
      customList: [], // 自定义属性
      fileList: [],
      pageLoading: false,
      sourceList: [], // 来源
      prioritList: [], // 优先级
      typeCodeList: [], // 分类
      orgData: [] // 组织机构
    }
  },
  async mounted() {
    await this.getFormPage()
    await this.getOption()
  },
  methods: {
    dataSelectChange({ item, list, user }, key) {
      this.customList.forEach(e => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.form[e.key] = item[config.relationField]
            const obj = list.find(e => e.id == config.relationField && !e.primary)
            e.quoteType = obj.type
            if (e.quoteType == 'user') {
              e.user = user[item[config.relationField]]
            }
          }
        }
      })
    },
    getOption() {
      this.getSourceList()
      this.getPrioritList()
      this.getIssueType()
    },
    async getFormPage() {
      try {
        this.pageLoading = true
        this.templeteList = []
        this.formRules = {}

        const res = await apiBaseFormProperty(this.formId)
        this.pageLoading = false
        if (!res.isSuccess) {
          return
        }

        if (!res.data) return

        // 固定属性
        const fixed =
          res.data.customFormFields && res.data.customFormFields.length
            ? res.data.customFormFields.filter((r) => r.isBasic && r.state && r.key != 'stateCode')
            : []

        const map = {
          typeCode: this.typeCodeList // 类型

        }

        fixed.forEach((element) => {
          element.placeholder = JSON.parse(element.config)?.placeholder
          element.multiple = JSON.parse(element.config)?.multiple
          element.message = JSON.parse(element.config)?.message
          element.validator = element.isRequired ? JSON.parse(element.config)?.validator : null
          element.max = JSON.parse(element.config)?.max
          element.options =
            element.isBuilt && map[element.key]
              ? map[element.key]
              : JSON.parse(element.config)?.options
          element.type =
          element.type = element.type && element.type.code != 'CUSTOM' ? element.type.code : JSON.parse(element.config)?.customType
        })
        this.fixedProperty = fixed.sort(function(a, b) {
          return a.sort - b.sort
        })

        // 固定属性的校验规则
        const fixedRoule = this.fixedProperty.map((r) => ({
          required: r.isRequired,
          message: r.message,
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple || r.type == 'FILE' ? 'array' : 'string'
        }))

        fixedRoule.forEach((item) => {
          if (!this.fixdFormRules[item.key]) {
            this.fixdFormRules[item.key] = [item]
          } else {
            this.fixdFormRules[item.key].push(item)
          }
        })

        // 自定义属性
        const custom =
          res.data.customFormFields && res.data.customFormFields.length
            ? res.data.customFormFields.filter((r) => !r.isBasic && r.state)
            : []

        const listMap = {
          priorityCode: this.prioritList, // 优先级
          sourceCode: this.sourceList, // 来源
          typeCode: this.typeCodeList // 类型
        }
        custom.forEach((element) => {
          element.placeholder = JSON.parse(element.config)?.placeholder
          element.multiple = JSON.parse(element.config)?.multiple
          element.message = JSON.parse(element.config)?.message
          element.validator = element.isRequired ? JSON.parse(element.config)?.validator : null
          element.defaultTime = JSON.parse(element.config)?.defaultTime
          element.options =
            element.isBuilt && listMap[element.key]
              ? listMap[element.key]
              : JSON.parse(element.config)?.options

          element.type = element.type && element.type.code != 'CUSTOM' ? element.type.code : JSON.parse(element.config)?.customType
          element.precision = JSON.parse(element.config)?.precision
          element.defaultValue = JSON.parse(element.config)?.defaultValue
          element.relationShipsheet = JSON.parse(element.config)?.relationShipsheet

          if (element.type == 'SELECT' || element.type == 'LINKED' || element.type == 'INT') {
            this.$set(this.form, element.key, JSON.parse(element.config)?.defaultValue)
          }
          // if (element.type == 'LINKED') {
          //   this.$nextTick(() => {

          //     this.$refs.dataSelect.forEach(j => {
          //       j.getInitTableData(element.relationShipsheet)
          //     })
          //   })
          // }
        })

        // 排序
        this.customList = custom.sort(function(a, b) {
          return a.sort - b.sort
        })

        // 自定义属性的校验规则
        const advanceRoule = this.customList.map((r) => ({
          required: r.isRequired,
          message: r.message,
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : r.type == 'INT' ? 'number' : 'string'
        }))

        advanceRoule.forEach((item) => {
          if (!this.formRules[item.key]) {
            this.formRules[item.key] = [item]
          } else {
            this.formRules[item.key].push(item)
          }
        })

        // 新增时默认赋值当前登录人
        const userInfo = storage.get('user')
        this.$set(this.form, 'putBy', userInfo.id)
        this.$set(this.form, 'leadingBy', userInfo.id)
        this.$set(this.fixdForm, 'handleBy', userInfo.id)
      } catch (e) {
        this.pageLoading = false
      }
      this.pageLoading = false
    },

    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })

      // 默认值
      if (data && data.length) {
        this.$set(this.fixdForm, key, data[1].code)
        // this.$set(this.form, key, data[0].code)
      }
    },

    // 查询全部类型
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeCode
      }
      )
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
    },

    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'sourceCode', res.data)
    },

    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'priorityCode', res.data)
    },

    onClose() {
      this.splitFlag = false
      this.$emit('update:visible', false)
    }

  }
}

</script>

<style lang="scss" scoped>

:deep(.drawer .vone-el-drawer__layout) {
  overflow-y: hidden;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

:deep(.el-dialog__body) {
  height: 70vh;
  overflow-y: scroll;
}

.row-box {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap
}
:deep(.el-skeleton) {
   .el-skeleton__item {
    height: 32px;
    margin-bottom: 18px;
    width: 100%;
  }
  .skeleton-item_lable {
    width: 10%;
    height: 14px;
  }
}
</style>

