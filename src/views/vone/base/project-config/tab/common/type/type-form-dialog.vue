<template>
  <div>
    <el-dialog :title="title" width="40%" v-model="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="typeForm" v-loading="formLoading" :rules="rules" :model="typeForm" label-position="top">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-input v-model="typeForm.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事项编号前缀" prop="prefix">
              <el-input v-model="typeForm.prefix" placeholder="请输入事项编号前缀" :disabled="title == '编辑类型'" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="标识" prop="code">
              <el-input v-model="typeForm.code" placeholder="请输入标识" :disabled="title == '编辑类型'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标" prop="icon">
              <el-input v-model="typeForm.icon" placeholder="请输入图标" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="表单" prop="customFormId">
              <el-select v-model="typeForm.customFormId" :disabled="type=='copy'" clearable filterable>
                <el-option v-for="item in allForm" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作流" prop="workflowId">
              <el-select v-model="typeForm.workflowId" clearable :disabled="type=='copy'" filterable @focus="setOptionWidth" @change="changeWorkFlow">
                <el-option v-for="item in flowList" :key="item.id" :label="item.name" :value="item.id" :style="{width:selectOptionWidth}" />
              </el-select>

            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="颜色" prop="color">
              <el-input v-model="typeForm.color" type="color" placeholder="请选择颜色" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="enabled">
              <el-switch v-model="typeForm.enabled" class="switchStyle" active-color="#13ce66" active-text="启用" inactive-text="禁用" @change="changeStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              v-if="typeClassify !='IDEA'"
              label="应用类型"
              prop="projectTypeCodes"
              :rules="[{ required: true, message: '请选择应用类型',
                         trigger: 'change',
              }]"
            >
              <el-checkbox-group v-model="typeForm.projectTypeCodes">
                <el-checkbox v-for="(item, index) in projectTypeList" :key="index" :label="item.code">{{
                  item.name
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input v-model="typeForm.description" type="textarea" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>

        <el-button type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getAllProjectType } from '@/api/vone/project/index'
import { apiVaBaseCustomFormQuery } from '@/api/vone/base/customForm'
import { apiAlmWorkflowNoPage, anyncTypeToProject } from '@/api/vone/base/work-flow'
import { apiAlmTypeAdd, apiAlmTypeCopy, getAlmGetTypeById } from '@/api/vone/alm/index'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: undefined
    },
    typeClassify: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    type: {
      type: String,
      default: ''
    },
    dataLength: {
      type: Number,
      default: null
    }

  },
  data() {
    return {
      flag: false,
      typeForm: {
        readonly: false,
        icon: 'el-icon-yibiaoban-shuxingyanse',
        color: '#000',
        projectTypeCodes: [],
        enabled: true
      },
      allForm: [],
      flowList: [],
      projectTypeList: [],
      formLoading: false,
      rules: {
        classify: [
          {
            required: true,
            message: '请选择事项分类',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          }
        ],
        prefix: [
          {
            required: true,
            message: '请输入事项编号前缀',
            trigger: 'change'
          },
          {
            max: 20,
            message: '长度不超过20个字符',
            trigger: 'change'
          },
          {
            pattern: '^[A-Za-z_-]+$',
            message: '只能输入大小写英文，中划线，下划线组成的标识'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入长度不超过20个字符的标识',
            trigger: 'change'
          },
          {
            pattern: '^[A-Za-z0-9_-]{1,20}$',
            message: '请输入不超过20位由字母、数字或者下划线或中划线组成的标识'
          }
        ],
        customFormId: [
          {
            required: true,
            message: '请选择表单'
          }

        ],
        workflowId: [
          {
            required: true,
            message: '请选择工作流'
          }

        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change'
          }
        ]

      },
      allFlow: [],
      selectOptionWidth: '',
      saveLoading: false
    }
  },

  mounted() {
    this.$set(this.typeForm, 'classify', this.typeClassify)
    this.getWorkFlow()
    this.getAllForm()
    this.getAllProjectTypes()

    if (this.id) {
      this.getTypeInfo()
    }
  },
  methods: {
    async getTypeInfo() {
      this.formLoading = true
      const res = await getAlmGetTypeById(this.id)
      if (!res.isSuccess) {
        return
      }
      this.typeForm = res.data
      this.formLoading = false
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.typeForm.resetFields()
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 查询项目类型
    async getAllProjectTypes() {
      const res = await getAllProjectType()
      if (!res.isSuccess) {
        return
      }
      this.projectTypeList = res.data
      this.$set(this.typeForm, 'projectTypeCodes', res.data.map(r => r.code))
    },
    // 查询表单
    async getAllForm() {
      const res = await apiVaBaseCustomFormQuery()

      if (!res.isSuccess) {
        return
      }
      this.allForm = res.data.filter(r => r.bizType == this.typeClassify)
    },
    // 工作流
    async getWorkFlow() {
      const res = await apiAlmWorkflowNoPage()
      if (!res.isSuccess) {
        return
      }
      this.flowList = res.data
    },

    changeWorkFlow() {
      // this.$message({
      //   type: 'warning',
      //   showClose: true,
      //   message: '切换工作流保存之后,当前类型的任务除了处于结束状态的任务，都将移动到新的任务流的起始状态,请谨慎操作'
      // })
    },
    // 保存
    async saveInfo() {
      try {
        await this.$refs.typeForm.validate()
      } catch (e) {
        return
      }

      try {
        this.saveLoading = true
        var data
        if (this.type == 'copy') {
          data = await apiAlmTypeCopy(
            this.typeForm
          )
        } else {
          data = await apiAlmTypeAdd(
            this.typeForm
          )
        }
        this.saveLoading = false
        if (!data.isSuccess) {
          this.$message.warning(data.msg)
          return
        }
        // this.$message.success(data.msg)
        this.onClose()
        this.$emit('success')

        if (this.typeClassify == 'IDEA' || !this.flag) return
        this.showConfirm(data.data)
      } catch (e) {
        this.saveLoading = false
        return
      }
    },
    changeStatus() {
      this.flag = true
    },
    async showConfirm(row) {
      const h = this.$createElement
      await this.$confirm('', {
        message: h('div', null, [
          h('i', { class: 'iconfont el-icon-tips-check-circle-fill', style: 'color:#00BF80;font-size: 16px;vertical-align: bottom' }),
          h('span', { style: 'margin-left:5px;font-weight:600;vertical-align:top;color:#00BF80 ; font-size:16px' }, '创建成功'),
          h('br', undefined, undefined),
          h('span', { style: 'display:inline-block;margin:10px 0 0 20px;' }, `该类型已${row.enabled ? '开启' : '禁用'},确定要同步到项目?`)
        ]),
        confirmButtonText: '同步',
        cancelButtonText: '不同步'
      }).then(async() => {
        const res = await anyncTypeToProject(row.id
        )
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.$message.success('操作成功')
      }).catch(() => {
        console.log()
      })
    }

  }
}
</script>
<style lang="scss" scoped>
.ml-10 {
  margin-left: 16px;
}
</style>

