<template>
  <div>
    <el-dialog title="编辑" width="40%" v-model="visible" :before-close="onClose" :close-on-click-modal="false" <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
      <el-form ref="classForm" v-loading="formLoading" :model="classForm" :rules="rules" label-position="top">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="classForm.name" placeholder="名称" disabled />
        </el-form-item>
        <el-form-item label="表单" prop="customFormId">
          <el-row type="flex" justify="space-between">
            <el-select v-model="classForm.customFormId" clearable filterable>
              <el-option v-for="item in formList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-button class="ml-10" @click="newForm(classForm.customFormId)">
              查看表单
            </el-button>
          </el-row>
        </el-form-item>
        <el-form-item label="工作流" prop="workflowId">
          <el-row type="flex" justify="space-between">
            <el-select v-model="classForm.workflowId" clearable filterable @change="changeWorkFlow(classForm.workflowId)">
              <el-option v-for="item in flowList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-button class="ml-10" @click="newFlow(classForm.workflowId)">
              查看工作流
            </el-button>
          </el-row>
        </el-form-item>
        <el-form-item label="应用到其它工作项类型" prop="customFormTypeCode">
          <el-checkbox-group v-model="classForm.customFormTypeCode">
            <!-- <el-row>
              <el-col v-for="(item, index) in allType" :key="index" :span="6">
                <el-checkbox :label="item.code">{{ item.name }}</el-checkbox>
              </el-col>
            </el-row> -->

            <el-checkbox v-for="item in allType" :key="item.id" :label="item.code">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { apiVaBaseCustomFormQuery, apiVaBaseClassifyPut } from '@/api/vone/base/customForm'
import { apiAlmWorkflowNoPage } from '@/api/vone/base/work-flow'
import {
  apiAlmTypeNoPage
} from '@/api/vone/alm/index'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    ip: {
      type: String,
      default: null
    },
    typeClassify: {
      type: String,
      default: null
    },
    form: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      classForm: {
        customFormTypeCode: []

      },
      formLoading: false,
      rules: {
        name: [{
          required: true,
          message: '请输入长度不超过20个字符的名称'

        },
        {
          pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
          message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
        },
        {
          max: 20,
          message: '请输入长度不超过20个字符的名称'

        }],
        customFormId: [
          {
            required: true,
            message: '请选择表单'
          }
        ],
        workflowId: [
          {
            required: true,
            message: '请选择工作流'
          }
        ]
      },
      formList: [],
      flowList: [],
      allType: [], // 分类下的所有类型
      allFlow: []
    }
  },

  mounted() {
    this.getformList()
    this.getWorkFlow()
    this.getTypeList()
    this.classForm = this.form

    this.$set(this.classForm, 'customFormId', this.form.customForm ? this.form.customForm.id : '')
    this.$set(this.classForm, 'workflowId', this.form.workflowInfo ? this.form.workflowInfo.id : '')
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.classForm.resetFields()
    },
    // 查看表单
    newForm(val) {
      const name = this.formList.find(r => r.id == val).name
      const newpage = this.$router.resolve({ path: '/base/setting/view', query: {
        showConfig: true, formId: val, typeClassify: this.typeClassify, typeName: name, viewType: true
      }})
      window.open(newpage.href, '_blank')
    },
    // 查看工作流
    newFlow(val) {
      const name = this.flowList.find(r => r.id == val).name
      const newpage = this.$router.resolve({ name: 'base_work_flow_config',
        params: { id: val },
        query: { name: name, type: 'details' }
      })
      window.open(newpage.href, '_blank')

      // this.$router.push({
      //   name: 'base_work_flow_config',
      //   params: { id: val },
      //   query: { name: name, type: 'details' }
      // })
    },
    changeWorkFlow() {
      this.$message({
        type: 'warning',
        showClose: true,
        message: '切换工作流保存之后,当前类型的任务除了处于结束状态的任务，都将移动到新的任务流的起始状态,请谨慎操作'
      })
    },
    // 查询分类下的所有类型
    async getTypeList() {
      const res = await apiAlmTypeNoPage(
        {
          classify: this.typeClassify
        }
      )
      if (!res.isSuccess) {
        return
      }
      this.allType = res.data
      this.$set(this.classForm, 'customFormTypeCode', res.data.length ? res.data.map(r => r.code) : [])
    },
    // 表单分类
    async getformList() {
      this.formLoading = true
      const res = await apiVaBaseCustomFormQuery()
      this.formLoading = false
      if (!res.isSuccess) {
        return
      }

      this.formList = res.data && res.data.length ? res.data.filter(r => r.bizType == this.typeClassify) : []
    },
    // 工作流
    async getWorkFlow() {
      this.formLoading = true
      const res = await apiAlmWorkflowNoPage()
      this.formLoading = false
      if (!res.isSuccess) {
        return
      }
      this.flowList = res.data
    },

    // 保存
    async saveInfo() {
      try {
        await this.$refs.classForm.validate()
      } catch (e) {
        return
      }
      const params = {
        code: this.classForm.code,
        customFormId: this.classForm.customFormId,
        customFormTypeCode: this.classForm.customFormTypeCode,
        workflowId: this.classForm.workflowId,
        workflowTypeCode: this.classForm.customFormTypeCode
      }
      this.save(params)
    },
    async save(params) {
      const { isSuccess, msg } = await apiVaBaseClassifyPut(
        params
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.onClose()
      this.$emit('success')
    }
  }
}
</script>
<style lang="scss" scoped>
.ml-10 {
  margin-left: 16px;
}
</style>

