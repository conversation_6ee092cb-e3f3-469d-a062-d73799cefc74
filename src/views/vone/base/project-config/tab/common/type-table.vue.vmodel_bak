<template>
  <div class="box">
    <!-- 配置工作流节点显示字段 -->
    <workFlow v-if="showFlow" :flow-id="workFlowId" :custom-form-id="customFormId" :type-classify="typeClassify" :type-code="typeCode" :allcustom="allcustom" :type-name="typeName" @back="back" />

    <div v-else>

      <section class="rowLine">
        <header>

          <strong>
            {{ form.name }}
          </strong>

          <span>
            <el-button v-if="form.workflowInfo" type="primary" icon="iconfont el-icon-application-step-setting" @click="configFlowAll(form)">配置</el-button>
            <el-dropdown trigger="click" @command="(e) => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="iconfont el-icon-application-edit" :command="putClassfiy">
                  编辑
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </header>
        <el-form ref="form" v-loading="formLoading" :model="form" label-position="top">
          <el-row class="basicHeader" :gutter="24">
            <el-col :span="6">

              <div class="fixedItem">

                <el-form-item label="表单名称">
                  <!-- 图标 -->
                  <span class="custom-icon-bg bg_1">
                    <i class="iconfont el-icon-application-file" />
                  </span>
                  {{ form.customForm ? form.customForm.name : "--" }}

                </el-form-item>
              </div>

            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <el-form-item label="属性数量">
                  <span class="custom-icon-bg bg_2">
                    <i class="iconfont el-icon-application-view-card" />
                  </span>
                  {{ form.customForm && form.customForm.customFormFields.length ? form.customForm.customFormFields.length : 0 }} 个

                </el-form-item>
              </div>

            </el-col>
            <el-col :span="6">
              <div class="fixedItem">

                <el-form-item label="工作流名称">
                  <span class="custom-icon-bg bg_3">
                    <i class="iconfont el-icon-application-workflow" />
                  </span>

                  {{ form.workflowInfo ? form.workflowInfo.name : '--' }}
                </el-form-item>
              </div>

            </el-col>
            <el-col :span="6">

              <div class="fixedItem">

                <el-form-item label="状态数量">
                  <span class="custom-icon-bg bg_4">
                    <i class="iconfont el-icon-application-carried-out" />

                  </span>

                  {{ form.workflowInfo && form.workflowInfo .workflowNodes && form.workflowInfo .workflowNodes.length ? form.workflowInfo .workflowNodes.length : 0 }} 个

                </el-form-item>
              </div>

            </el-col>
          </el-row>
        </el-form>
      </section>

      <section>
        <vone-search-wrapper>
          <template slot="search">
            <strong>
              类型
            </strong>

          </template>

          <template slot="actions">
            <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('work_type_add')" @click="addTypeForm">新增</el-button>
            <el-dropdown trigger="click" @command="(e) => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vone-search-wrapper>
        <div style="height: calc(100vh - 245px)">
          <vxe-table
            ref="type-table"
            class="vone-vxe-table draggTable"
            border
            height="auto"
            show-overflow="tooltip"
            :row-config="{useKey: true}"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData"
            :column-config="{ resizable: true, minWidth: 120 }"
            :checkbox-config="{checkMethod:selectable}"
            row-id="id"
          >
            <vxe-column type="checkbox" width="66" align="center" fixed="left">
              <template>
                <a>
                  <i class="iconfont el-icon-application-drag handle" />
                </a>
              </template>
            </vxe-column>

            <vxe-column title="名称" field="name" />
            <vxe-column title="图标" field="icon" width="70">
              <template slot-scope="scope">
                <span v-if="scope.row.icon && scope.row.color">
                  <i :class="`iconfont ${scope.row.icon}`" :style="{ color:`${scope.row.color ? scope.row.color : '#ccc'}`}" />
                </span>
                <span v-else>
                  {{ scope.row.icon }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="标识" field="code" />

            <vxe-column title="表单" field="customFormId">
              <template slot-scope="scope">
                <span v-if="scope.row.customFormId && scope.row.echoMap && scope.row.echoMap.customFormId">
                  {{ scope.row.echoMap.customFormId.name }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="工作流" field="workflowId">
              <template slot-scope="scope">
                <span v-if="scope.row.workflowId && scope.row.echoMap && scope.row.echoMap.workflowId">
                  {{ scope.row.echoMap.workflowId.name }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="状态" field="enabled" width="120">
              <template #default="{ row }">

                <el-switch v-model="row.enabled" class="switchStyle" active-color="#13ce66" active-text="启用" inactive-text="禁用" @change="editStatus(row)" />

              </template>
            </vxe-column>
            <vxe-column title="描述" field="description" />
            <vxe-column title="操作" fixed="right" align="left" width="120">
              <template #default="{ row }">
                <template>
                  <el-tooltip class="item" content="编辑" placement="top">
                    <el-button type="text" :disabled="!$permission('work_type_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editTypeForm(row)" />
                  </el-tooltip>
                  <el-divider direction="vertical" />
                  <el-tooltip class="item" content="配置工作流表单" placement="top">
                    <el-button type="text" :disabled=" !$permission('work_type_config')" icon="iconfont el-icon-application-step-setting" @click="configFlow(row)" />
                  </el-tooltip>
                  <el-divider direction="vertical" />
                  <el-dropdown :hide-on-click="false" @command="e => e && e()">
                    <el-button type="text" icon="iconfont el-icon-application-more" />
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item icon="iconfont el-icon-application-copy" :disabled="!$permission('work_type_add')" :command="() =>copyTypeForm(row)">
                        <span>复制</span>
                      </el-dropdown-item>
                      <el-dropdown-item icon="iconfont el-icon-application-delete" :disabled="row.readonly ||!$permission('work_type_delete')" :command="() =>deleteRow(row)">
                        <span>删除</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </div>

      </section>
    </div>

    <!-- 编辑分类信息/ -->
    <editClassfiy v-if="classParam.visible" v-bind="classParam" v-model:visible="classParam.visible" :type-classify="typeClassify" @success="getPage" />

    <!-- 类型新增编辑/ -->
    <editTypeForm v-if="typeParam.visible" v-bind="typeParam" v-model:visible="typeParam.visible" @success="getTableData(typeClassify)" />

  </div>
</template>

<script>
import Sortable from 'sortablejs'

import { apiAlmTypeDel, sortTypeBoard, getListNeglectQuery, anyncTypeToProject } from '@/api/vone/base/work-flow'

import { apiVaBaseClassify } from '@/api/vone/base/customForm'
import {
  changeTypeEnabled
} from '@/api/vone/base/work-flow'

import editClassfiy from './type/type-dialog'
import editTypeForm from './type/type-form-dialog'
import workFlow from './type-to-flow.vue'
import _ from 'lodash'

export default {
  components: {
    editClassfiy,
    editTypeForm,
    workFlow
  },
  props: {
    id: {
      type: String,
      default: undefined
    },
    typeClassify: {
      type: String,
      default: undefined
    },
    projectTypeCodes: {
      type: Array,
      default: () => []
    },
    listData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showFlow: false,
      workFlowId: undefined,
      customFormId: undefined,
      allcustom: {},
      form: {},
      code: [],
      formLoading: false,
      tableLoading: false,
      tableData: [],
      dialogFormVisible: false,
      types: [],
      title: '',
      saveLoading: false,
      formData: {

      },
      classParam: { visible: false },
      typeParam: { visible: false },
      typeList: [],
      typeCode: undefined,
      typeName: undefined,
      rowId: '',
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('work_type_delete'),
          fn: this.deleteAll
        }
      ]
    }
  },
  watch: {
    typeClassify: {
      handler(v) {
        if (v) {
          this.showFlow = false
          this.$nextTick(() => {
            this.getTableData(this.typeClassify)
          })

          this.getFormList()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.rowDrop()
  },
  methods: {
    // 表格行拖拽
    rowDrop() {
      const tbody = document.querySelector('.draggTable .vxe-table--body-wrapper tbody')

      Sortable.create(tbody, {
        group: 'name',
        handle: '.handle',
        animation: 150,
        avoidImplicitDeselect: true, // 外部点击不能取消选中
        removeCloneOnHide: true,
        dragClass: 'draggingRow', // 拖动中的dom类名
        // 拖拽结束回调，处理添加到左侧分组业务逻辑
        onEnd: (evt) => {
          const { newIndex, oldIndex } = evt
          if (newIndex === oldIndex) return
          const moved = this.tableData.splice(oldIndex, 1)[0]

          this.tableData.splice(newIndex, 0, moved)
          const ids = this.tableData.map(v => v.id)
          sortTypeBoard(ids).then(res => {
            if (res.isSuccess) {
              this.$message.success('修改成功')
            } else {
              this.$message.error('修改失败')
            }
          })
        }
      })
    },
    back() {
      this.showFlow = false
      this.$nextTick(() => {
        this.getTableData(this.typeClassify)
      })
    },
    getPage() {
      this.getTableData(this.typeClassify)
      this.getFormList()
    },

    changeWorkFlow() {
      if (this.title == '新增类型') return
      this.$message({
        type: 'warning',
        showClose: true,
        message: '切换工作流保存之后,当前类型的任务除了处于结束状态的任务，都将移动到新的任务流的起始状态,请谨慎操作'
      })
    },
    // 修改分类信息
    putClassfiy() {
      this.classParam = { visible: true, id: this.typeClassify, form: this.form }
    },
    // 配置分类工作流节点权限
    configFlowAll(form) {
      // this.$set(this.form, 'customFormId', this.form.customForm.id)
      // this.$set(this.form, 'workflowId', this.form.workflowInfo.id)
      this.showFlow = true
      this.workFlowId = form.workflowInfo.id
      // this.allcustom = row.echoMap.customFormId // huoqu 全部的工作项
      this.customFormId = form.customForm.id
      this.typeCode = null
      this.typeName = this.form.name
    },
    // 配置工作流
    configFlow(row) {
      this.showFlow = true
      this.workFlowId = row.echoMap.workflowId.id
      this.allcustom = row.echoMap.customFormId // huoqu 全部的工作项
      this.customFormId = row.customFormId
      this.typeCode = row.code
      this.typeName = row.name
      this.rowId = row.id
    },
    selectable({ row }) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    getStatus(index, row) {
      return !this.$permission('work_type_edit')
    },
    getConfig() {
      return !this.$permission('work_type_config')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('work_type_delete')
    },

    onClose() {
      this.dialogFormVisible = false
      this.$refs.typeForm.resetFields()
    },

    // 分类
    async getFormList() {
      this.formLoading = true
      const res = await apiVaBaseClassify()
      if (!res.isSuccess) {
        return
      }
      this.typeList = res.data
      this.form = this.typeList.find(r => r.code == this.typeClassify)
      this.formLoading = false
    },

    async getTableData(typeClassify) {
      this.$set(this.formData, 'classify', typeClassify || this.typeClassify)
      this.tableLoading = true

      const res = await getListNeglectQuery(this.formData)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.form = this.listData.find(r => r.code == this.typeClassify)
      this.tableData = res.data
    },
    addTypeForm() {
      this.typeParam = {
        visible: true,
        title: '新增类型',
        type: 'add',
        typeClassify: this.typeClassify,
        dataLength: Number(this.tableData.total)
      }
    },
    editTypeForm(row) {
      this.typeParam = {
        visible: true,
        title: '编辑类型',
        id: row.id,
        rowItem: row,
        type: 'edit',
        typeClassify: this.typeClassify
      }
    },
    // 复制类型
    copyTypeForm(e) {
      const row = _.cloneDeep(e)
      row.code = row.code + '_copy'
      this.typeParam = {
        visible: true,
        title: '复制类型',
        id: row.id,
        type: 'copy',
        typeClassify: this.typeClassify,
        rowItem: row
      }
    },
    deleteRow(item) {
      this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false,
        closeOnClickModal: false
      }).then(async() => {
        const res = await apiAlmTypeDel(
          [item.id]
        )
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('删除成功')
        this.getTableData(this.typeClassify)
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('type-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
      this.pageLoading = true
      try {
        const selectId = this.getVxeTableSelectData('type-table').map(r => r.id)
        const res = await apiAlmTypeDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData(this.typeClassify)
      } catch (e) {
        this.pageLoading = false
      }
    },
    async editStatus(row) {
      const res = await changeTypeEnabled(row.id,
        row.enabled
      )
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      // console.log(this.typeClassify, 'this.classify')
      if (this.typeClassify == 'IDEA') {
        this.getTableData(this.typeClassify)
        return this.$message.success('操作成功')
      }
      const h = this.$createElement
      await this.$confirm('', {
        message: h('div', null, [
          h('i', { class: 'iconfont el-icon-tips-exclamation-circle-fill', style: 'color:#3E7BFA;font-size: 16px;vertical-align: bottom' }),
          h('span', { style: 'display:inline-block;margin:0 10px' }, `已${row.enabled ? '开启' : '禁用'},确定要同步到项目?`)
        ]),
        confirmButtonText: '同步',
        cancelButtonText: '不同步'
      }).then(async() => {
        const res = await anyncTypeToProject(
          row.id
        )
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.$message.success('操作成功')

        // if (row.enabled) {
        //   // 执行开启操作
        // } else {
        //   // 执行关闭操作
        // }
      }).catch(() => {
        this.$set(row, 'enabled', row.enabled)
      })
    },
    showConfirm() {

    }
  }

}
</script>

<style lang="scss" scoped>
.box {
  :deep(.el-dialog__body) {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
.switchStyle :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}
.switchStyle :deep(.el-switch__label--left) {
  z-index: 9;
  left: 18px;
}
.switchStyle :deep(.el-switch__label--right) {
  z-index: 9;
  left: -5px;
}
.switchStyle :deep(.el-switch__label.is-active) {
  display: block;
}
.switchStyle:deep(.el-switch__core) {
  width: 54px !important;
}
.switchStyle :deep(.el-switch__label) {
  width: 54px !important;
}

.basicHeader {

  margin: 0 !important;

  .el-col-6 {
    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 16px;
        border-radius: 4px;
        // margin-right: 10px;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 5px;
      padding: 0 10px;
    }
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
    }
    :deep(.el-input__suffix) {
      display: none;
    }
    :deep(.el-form-item__label) {
      color: var(--auxiliary-font-color);
      // margin-left: 10px;
    }
    :deep(.el-input--small) {
      font-size: 14px;
    }
    // :deep(.is-disabled .el-input--small) {
    //   font-size: 14px;
    //   color: #000;
    // }
    :deep(.el-input.is-disabled .el-input__inner) {
      color: #000 !important;
      font-size: 14px;
    }
  }

  .el-col-24 {
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      font-weight: bold;
      font-size: 16px;
    }
  }
}
.rowLine {
  border-bottom: 1px solid var(--el-divider);
  margin: 0 -16px 16px;
  padding: 0 16px 7px 16px;
  header{
    display: flex;
    justify-content: space-between;
  }
}
.handle{
  float: left;
  padding-top: 4px;
  margin-right: 6px;
  // font-size: 14px;
}
:deep(.vone-vxe-table .vxe-header--row .col--checkbox .vxe-cell) {
  padding-left: 22px !important;
}
:deep(.toolbar) {
  border: none;
  margin: -16px -16px 0px -16px;
}
:deep(.toolbar_filter) {
  margin-bottom: 0px;
}
:deep(.el-form .el-form-item__label) {
  padding-bottom: 0px;
}
:deep(.el-form-item--small .el-form-item__content) {
  line-height: 28px;
}
.custom-icon-bg{
  height: 24px;
  line-height: 24px;
  width: 24px;
  border-radius: 4px;
  display: inline-block;
  text-align: center;

}
.bg_1{
  background-color:#DBFFFA;
  .iconfont{
    color: #09BDBD;
  }
}
.bg_2{
  background-color:#D1E6FF;
  .iconfont{
    color: #3388FF;
  }

}
.bg_3{
  background-color:#FFEECF;
  .iconfont{
    color:#F7A500;
  }
}
.bg_4{
  background-color:#EBDEFF;
  .iconfont{
    color: #6B4DFF;
  }
}
</style>
