<template>
  <div class="sectionPageContent">
    <div class="leftSection" :style="{'width': openFlag ? '400px' : '240px' }">
      <div class="header">
        <div class="team">
          <!-- <el-checkbox v-model="teamCheck" @change="teamChange">显示团队</el-checkbox> -->
        </div>
        <div class="search">
          <el-tooltip class="item" effect="dark" content="同步" placement="top">
            <i class="iconfont el-icon-application-renew" @click="syncOrgData" />
          </el-tooltip>
          <el-divider style="margin: 0 10px 0 6px" direction="vertical" />
          <el-popover v-model="visible" placement="bottom-start" width="300" popper-class="table-search-form org-search-form">
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">筛选机构</span>
              </div>
              <div class="search-form">
                <el-form ref="searchForm" inline label-position="top">
                  <el-form-item label="机构名称" prop="name">
                    <el-input v-model.trim="searchForm.name" style="width: 100%" placeholder="输入机构名称" />
                  </el-form-item>
                  <el-form-item label="负责人" prop="leadingBy">
                    <vone-remote-user v-model="searchForm.leadingBy" style="width: 100%" placeholder="选择负责人" />
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer org-footer">
                <el-button plain @click="reset">重置</el-button>
                <el-button type="primary" @click="searchTree">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="筛选" placement="top">
                <i :class="['iconfont', 'el-icon-application-filter', visible ? 'active' : '']" />
              </el-tooltip>
            </span>
          </el-popover>

          <el-divider style="margin: 0 10px 0 6px" direction="vertical" />
          <el-tooltip v-if="!openFlag" class="item" effect="dark" content="展开" placement="top">
            <i class="iconfont el-icon-direction-menu-unfold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
          <el-tooltip v-else class="item" effect="dark" content="收起" placement="top">
            <i class="iconfont el-icon-direction-menu-fold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
        </div>
      </div>
      <div class="treeContent">
        <el-tree
          ref="orgTree"
          v-loading="treeLoading"
          class="tree custom-tree-icon"
          :data="data"
          :highlight-current="true"
          node-key="id"
          default-expand-all
          check-strictly
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          @node-click="changeUser"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <el-tooltip v-if="data.isShow" class="node-label" effect="dark" :content="node.label" placement="top">
              <div>
                <svg-icon v-if="data.type == 'OrgTeam'" icon-class="setting-team" />
                <span class="label" :data-node="node.level">{{ node.label }}</span>
              </div>
            </el-tooltip>
            <div v-else class="node-label" @mouseenter="(e) => isShowToltip(e, data)" @mouseout="hideTip( data)">
              <svg-icon v-if="data.type == 'OrgTeam'" icon-class="setting-team" />
              <span class="label" :data-node="node.level">{{ node.label }}</span>
            </div>
            <span class="manager">
              <el-tag v-if="data.echoMap.leadingBy" effect="dark">{{ data.echoMap.leadingBy.name }}</el-tag>
            </span>
            <span v-if="data.type != 'OrgTeam'" class="operation-tree-icon">
              <el-button type="text" size="mini" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('base_org_add')" @click.stop="() => add(node, data)" />
              <el-button type="text" size="mini" icon="iconfont el-icon-application-edit" :disabled="!$permission('base_org_edit')" @click.stop="() => nodeClick(node, data)" />
              <el-button type="text" size="mini" icon="iconfont el-icon-application-delete" :disabled="!$permission('base_org_delete') || data.type == 'ORG_TYPE'" @click.stop="() => remove(node, data)" />
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="rightSection">
      <main>
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic
              ref="tableSearchForm"
              table-search-key="orgUserTable"
              v-model:model="formData"
              v-model:default-fileds="defaultFileds"
              show-basic
              v-model:extra="extraData"
              :table-ref="$refs['orgUserTable']"
              :show-column="false"
              @getTableData="getTableData"
            />
          </template>
          <template slot="fliter">
            <vone-search-filter
              v-model:extra="extraData"
              v-model:model="formData"
              v-model:default-fileds="defaultFileds"
              @getTableData="getTableData"
            />
          </template>

        </vone-search-wrapper>
        <div :style="{height: tableHeight}">
          <vxe-table
            ref="orgUserTable"
            class="vone-vxe-table"
            border
            resizable
            height="auto"
            show-overflow="tooltip"
            :loading="pageLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            row-id="id"
            :column-config="{ minWidth:'120px' }"
          >
            <vxe-column title="账号" field="account" min-width="150" fixed="left" />
            <vxe-column title="名称" field="name" min-width="150">
              <template #default="{ row }">
                <vone-user-avatar :avatar-path="row.avatarPath" :avatar-type="row.avatarType" :name="row.name" />
              </template>
            </vxe-column>
            <vxe-column title="邮箱" field="email" width="150" />
            <vxe-column title="角色" field="role" width="150">
              <template #default="{ row }">
                {{ row.allRole }}
              </template>
            </vxe-column>
            <vxe-column title="用户类型" field="type" width="120">
              <template #default="{ row }">
                {{ userTypeName(row) }}
              </template>
            </vxe-column>
            <vxe-column field="updateTime" show-overflow="tooltip" title="更新时间" width="180" />
            <vxe-column field="mobile" title="手机号" />
          </vxe-table>
        </div>

        <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
      </main>
    </div>
    <addEdit v-if="dialogVisible" ref="editDialog" v-model:dialog-visible="dialogVisible" :edit-title="editTitle" :org-p="org" :parent-id="parentId" :parent-code="parentCode" :tree-id="treeId" @success="getOrgList" />
  </div>
</template>

<script>
import addEdit from './addEditModel.vue'
import {
  orgList,
  deleteOrg,
  updateSort,
  apiBasePageForOrg,
  syncOrg
} from '@/api/vone/base/org'
import { gainTreeList, textRange } from '@/utils'
export default {
  components: {
    addEdit
  },
  data() {
    return {

      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        }
      ],

      treeLoading: false,
      dialogVisible: false,
      teamCheck: false,

      searchForm: { name: '', leadingBy: '' },
      data: [],
      editTitle: '',
      org: {
        id: '',
        name: '',
        parentId: '',
        state: true,
        description: '',
        sort: 0
      },
      pageLoading: false,
      tableData: {},
      formData: {
        orgId: '',
        name: '',
        account: ''
      },
      extraData: {},
      treeId: '',
      parentId: '',
      parentCode: '',
      visible: false,
      node: null,
      openFlag: false
    }
  },
  computed: {
    userTypeName() {
      return function(row) {
        return row.echoMap?.type?.name || ''
      }
    },
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.$nextTick(() => {
          if (val.orgId) {
            this.$refs.orgTree.setCurrentKey(val.orgId)
          } else if (val.teamIds && val.teamIds.length) {
            this.teamCheck = true
            this.$refs.orgTree.setCurrentKey(val.teamIds[0])
          }
        })
      } }
  },
  mounted() {
    this.getOrgList()
  },
  methods: {
    isShowToltip(e, node) {
      const bool = textRange(e.target)
      this.$set(node, 'isShow', bool)
    },
    hideTip(node) {
      this.$set(node, 'isShow', false)
    },
    // 获取表格数据
    async getTableData() {
      let params = {}
      this.pageLoading = true

      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.tableSearchForm?.sortObj

      if (this.node?.type == 'OrgTeam') {
        this.$set(this.formData, 'teamIds', [this.node.id])
      } else {
        this.$set(this.formData, 'orgId', this.formData.orgId)
      }
      params = {
        ...pageObj,
        ...sortObj,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      let res = null
      if (this.node?.type == 'OrgTeam') {
        delete params.model.orgId

        res = await apiBasePageForOrg(params)
      } else {
        delete params.model.teamIds
        res = await apiBasePageForOrg(params)
      }
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(element => {
        element.allRole = element.userRoles && element.userRoles.length ? element.userRoles.map(r => r.echoMap.roleId.name).join('、') : ''
      })
      this.tableData = res.data
    },
    syncOrgData() {
      syncOrg().then(res => {
        if (res.isSuccess) {
          this.$message.success(res.msg)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    collapse(draggingNode, dropNode, type) {
      if (draggingNode.level == 1) {
        return false
      } else if (draggingNode.level === dropNode.level) {
        if (draggingNode.parent.id === dropNode.parent.id) {
          // 向上拖拽 || 向下拖拽
          return type === 'prev' || type === 'next'
        }
      } else {
        // 不同级进行处理
        return false
      }
    },
    changeUser(node) {
      this.formData.orgId = node.id
      this.node = node
      this.$nextTick(() => {
        this.getTableData()
      })
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        orgId: draggingNode.data.id,
        parentId: dropType == 'before' ? dropNode.data.parentId : dropNode.data.id,
        preOrgId: this.getChange(dropNode.parent.childNodes, draggingNode.data.id)
      }
      updateSort(obj).then(res => {
      })
    },
    getChange(arr, id) {
      var proOrgId = ''
      arr.forEach((item, i) => {
        if (item.data.id == id) {
          if (i == 0) {
            proOrgId = null
          } else {
            proOrgId = arr[i - 1].data.id
          }
        }
      })
      return proOrgId
    },
    getOrgList() {
      this.treeLoading = true
      const params = {
        ...this.searchForm,
        'team': this.teamCheck
      }
      orgList(params).then((res) => {
        this.treeLoading = false
        const orgTree = gainTreeList(res.data)
        this.data = orgTree
        // this.formData.orgId = orgTree[0].id

        // if (this.formData.orgId) {
        //   console.log(this.formData, '909090009')
        //   this.$nextTick(() => {
        //     this.$refs.orgTree.setCurrentKey(this.formData.orgId)
        //   })
        // } else if (this.formData.teamIds.length) {
        //   this.teamCheck = true
        //   this.$nextTick(() => {
        //     this.$refs.orgTree.setCurrentKey(this.formData.teamIds[0])
        //   })
        // }

        // this.getTableData()
      })
    },
    teamChange() {
      this.getOrgList()
    },
    reset() {
      this.searchForm = { name: '', leadingBy: '' }
      this.$nextTick(() => {
        this.getOrgList()
        this.visible = false
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name != null && data.name.indexOf(value) !== -1
    },
    searchTree() {
      this.visible = false
      this.getOrgList()
      // this.$refs.orgTree.filter(this.searchText)
    },

    add(node, data) {
      this.dialogVisible = true
      this.editTitle = '新增'
      // this.initOrg()
      this.parentCode = data.code
      this.parentId = data.id
    },
    remove(node, data) {
      if (data.children) return this.$message.warning('当前机构下有子级机构,不允许直接删除，请先删除子机构')

      this.$confirm(`是否删除机构【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm'
      })
        .then(() => {
          deleteOrg([data.id]).then((res) => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.initOrg()
              this.getOrgList()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => { })
    },
    nodeClick(node, data) {
      this.dialogVisible = true
      this.editTitle = '编辑'
      this.treeId = data.id
      this.parentId = data.parentId

      // this.org = {
      //   id: data.id,
      //   name: data.name,
      //   parentId: data.parentId,
      //   state: data.state,
      //   description: data.description,
      //   sort: data.sort
      // }
    },
    handleNumChange(val) {
      this.org.sortValue = val
    },

    initOrg() {
      this.org = {
        id: '',
        name: '',
        parentId: '',
        state: true,
        description: '',
        sort: 0
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import "@/styles/variables.scss";
.org-search-form .search-main{
  .search-header {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--font-main-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
  .search-form {
    padding: 16px 20px;
    .el-form-item {
      width: 100%;
    }
  }
  .org-footer {
    text-align: right;
    padding: 12px 20px;
    border-top: 1px solid var(--solid-border-color);
  }
}
.search {
  display: flex;
  align-items: center;
  :deep(.el-divider--vertical) {
    margin: 0 10px 0 10px;
  }
  .iconfont {
    cursor: pointer;
    color: var(--font-second-color);
  }
  .iconfont:hover {
    color: var(--main-theme-color);
  }
  .iconfont.active {
    color: var(--main-theme-color);
  }
}
.leftSection {
  width: 240px;
  position: relative;
  .header {
    line-height: 48px;
    height: 47px;
    border-bottom: 1px solid var(--el-divider);
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-items: center;
    .team {
      flex: 1;
      :deep(.el-checkbox__label) {
        font-weight: 400;
        color: var(--font-main-color);
      }
    }

  }
}
.treeContent {
  margin-top:8px;
  height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px);
  overflow-y: overlay;
  .custom-tree-node {
			flex: 1;
			width: calc(100% - 90px);
      height: 100%;
      display: flex;
      align-items: center;
		}
		.el-tree-node__content {
      height: 36px;
      color: var(--font-main-color);
			display: inline-block;
			.node-label {
        display: inline-block;
        width: calc(100% - 90px);
				overflow: hidden;
				white-space: nowrap;
				text-overflow:ellipsis;
				.svg-icon {
					width: 16px;
					height: 16px;
          vertical-align: -0.2em;
				}
      }
			.manager {
        position: absolute;
        right: 16px;
      }
      .operation-tree-icon {
        .el-button {
          padding: 0px;
          height: unset;
          line-height: unset;
          min-width: unset;
          font-size: 16px;
          color: var(--font-second-color);
        }
        .el-button.is-disabled {
          background-color: unset;
          border: unset;
          color: var(--input-border-color) !important;
        }
        .el-button:hover {
          color: var(--main-theme-color);
        }
        opacity: 0;
				position: absolute;
        right: 16px;
      }
      &:hover {
				.manager {
					display: none
				}
        .operation-tree-icon {
          opacity: 1;
					background: var(--hover-bg-color);
        }
      }
			.svg-icon {
				margin-right: 4px;
			}
		}
}
:deep(.el-tree-node__expand-icon) {
  color: #777F8E;
}
.rightSection{
  position: relative;
}
</style>
