<template>
  <div>
    <el-dialog
      title="配置巡检策略"
      width="40%"
      v-model:visible="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      v-on="$listeners"
    >
      <el-form :model="strageForm">
        <el-form-item label="巡检策略" prop="monitorCron">
          <el-select v-model="strageForm.monitorCron" filterable>
            <el-option label="5分钟" value="5" />
            <el-option label="10分钟" value="10" />
            <el-option label="15分钟" value="15" />
            <el-option label="30分钟" value="30" />
            <el-option label="60分钟" value="60" />
            <el-option label="6小时" value="360" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   apiBaseCheckIsOpreationBatch,
//   apiBaseSetPatrolStrategy,
// } from "@/api/base/system/engine/engine";

export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    ip: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      strageForm: {
        monitorCron: '10'
      }
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      // this.getInfo()
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    // 查询巡检策略
    async getInfo() {
      // const { success, message } = await apiBaseCheckIsOpreationBatch({
      //   engineId: this.ip
      // })
      // if (!success) {
      //   this.$message.warning(message)
      //   return
      // }
    },

    // 保存巡检策略
    async saveInfo() {
      // const { success, message } = await apiBaseSetPatrolStrategy(
      //   this.ip,
      //   this.strageForm.monitorCron
      // )
      // if (!success) {
      //   this.$message.warning(message)
      //   return
      // }
      // this.$message.success(message)
      // this.$emit('update:visible', false)
      // this.$emit('success')
    }
  }
}
</script>
