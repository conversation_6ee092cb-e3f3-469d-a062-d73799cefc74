<template>
  <vone-drawer :title="title" v-model:visible="visible" size="lg" :before-close="onClose" class="voneDrawer">

    <vone-div-wrapper title="基本信息" class="table-card">
      <el-form ref="basicForm" :model="basicForm" label-position="top" :rules="basicFormRules" v-on="$listeners">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item prop="name" label="节点ip">
              <el-input v-model="basicForm.name" placeholder="请输入节点ip" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item prop="description" label="描述">
              <el-input v-model="basicForm.description" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="hostId" label="所属服务器">
              <el-select v-model="basicForm.hostId" style="width:100%" clearable placeholder="请选择所属服务器">
                <el-option v-for="(item, index) in hostDatas" :key="index" :label="item.ip" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="engineLoginName" label="用户名">
              <el-input v-model="basicForm.engineLoginName" placeholder="请输入引擎登录用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="enginePassword" label="密码">
              <el-input v-model="basicForm.enginePassword" show-password type="password" placeholder="请输入引擎登录密码" />
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
    </vone-div-wrapper>

    <vone-div-wrapper title="高级属性配置" class="table-card" style="margin-top:10px">
      <advanceConfig ref="advanceConfig" :advance-params="advanceTemplete" :engine-data="currentEngineData" />
    </vone-div-wrapper>

    <div slot="footer">

      <el-button type="primary" :loading="saveLoading" @click="save">保存</el-button>
      <el-button @click="onClose">取消</el-button>
    </div>

  </vone-drawer>
</template>
<script>

import { apiBaseEngineExtendInstance, apiBaseEngineAdd, apiBaseEngineInfoGetById } from '@/api/vone/base/engine'

import advanceConfig from '../../advanceConfig'
import { OS_LIST } from '../../utils'
export default {
  components: {
    advanceConfig
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    engineId: {
      type: String,
      default: null
    },
    id: {
      type: String,
      default: null
    },
    engineData: {
      type: Object,
      default: null
    },
    hostDatas: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      basicForm: {},
      saveLoading: false,
      osList: OS_LIST,
      basicFormRules: {
        name: [{
          required: true,
          pattern: '^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])(\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)){3}$',
          message: '请输入合法的节点IP'
        }],

        hostId: [{ required: true, message: '请选择所属服务器', trigger: 'blur' }],
        description: [{ required: false, max: 250, message: '引擎描述信息长度不能超过250个字符' }],
        engineLoginName: [{ required: true, max: 32, message: '请输入引擎登录用户名,长度不超过32位的数字或字符' }],
        enginePassword: [{ required: true, max: 256, message: '请输入引擎登录密码，长度不超过256位数字或字符' }]

      },
      advanceTemplete: [],
      currentEngineData: undefined
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      if (this.id) {
        this.loadEngine()
      }
    }
  },
  created() {
    this.getAdvancedData()
  },
  methods: {
    /**
     * 编辑，查询引擎数据
     */
    async loadEngine() {
      this.loading = true
      const res = await apiBaseEngineInfoGetById(this.id)
      this.editLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.currentEngineData = res.data
      this.basicForm = { ...res.data }
      this.$set(this.basicForm, 'enginePassword', '******')
    },

    onClose() {
      this.$emit('update:visible', false)
    },
    /**
     * 加载高级属性
     */
    async getAdvancedData() {
      const res = await apiBaseEngineExtendInstance('JENKINS_SLAVE')
      if (!res.isSuccess) {
        return
      }
      this.advanceTemplete = res.data
    },
    async save() {
      // 同时验证表单
      const [, engineExtendeds] = await Promise.all([this.$refs.basicForm.validate(), this.$refs.advanceConfig.getFormData()])

      // if(this.basicForm.enginePassword == '******'){

      // }

      if (
        this.basicForm !== null &&
        this.basicForm.enginePassword === '******'
      ) {
        delete this.basicForm.enginePassword
      }

      const formData = {
        parentId: this.engineId,
        // patrolStrategy: 15,
        classify: this.engineData.classify,
        instance: 'JENKINS_SLAVE',
        ...this.basicForm,
        engineExtendeds
      }

      await this.$confirm('保存JenkinsMaster节点信息，将延时10s获取节点最新状态，若获取失败请手动刷新节点状态', '提示', {
        type: 'warning',
        closeOnClickModal: false
      })

      try {
        this.saveLoading = true
        const res = await apiBaseEngineAdd(formData)
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.$emit('success')
        this.$message.success('保存成功')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
        // this.onClose()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.voneDrawer {
   :deep(.vone-el-drawer__layout) {
    height: calc(100vh - 110px);
    overflow-y: auto;
    padding: 20px;
  }

}

</style>

