<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="engineTable"
          v-model:model="formData"
          :table-ref="$refs['engineTable']"
          v-model:default-fileds="defaultFileds"
          show-basic
          v-model:extra="extraData"
          @getTableData="getEngineList"
        />
      </template>
      <template slot="actions">
        <el-button style="margin-left: 12px" type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('base_engine_add')" @click="clickAddEngine">
          新增
        </el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-model:extra="extraData"
          v-model:model="formData"
          v-model:default-fileds="defaultFileds"
          @getTableData="getEngineList"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="engineTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" align="center" fixed="left" />
        <vxe-column title="名称" field="name" min-width="150" fixed="left">
          <template #default="{ row }">
            <span v-if="row.instance && row.instance.code">
              <el-tooltip effect="dark" :content="row.instance.desc" placement="top">
                <i :class=" `iconfont ${row.icon}`" :style="{color:`${row.color}`}" />
              </el-tooltip>
            </span>
            <span v-else><i class="iconfont el-icon-tips-exclamation-circle danger" /></span>
            <a @click="showInfo(row)">
              {{ row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="state" width="100">
          <template #default="{ row }">
            <el-button v-if="row.state" type="text" :disabled="!$permission('base_engine_testConnect')" class="safe" @click="connectEngine(row)">
              <i class="el-icon-success" />正常
            </el-button>
            <el-button v-else class="danger" type="text" :disabled="!$permission('base_engine_testConnect')" @click="connectEngine(row)">
              <i class="el-icon-warning" />异常
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="分类" field="classify" width="120">
          <template #default="{ row }">
            <span v-if="row.classify && row.echoMap && row.echoMap.classify">
              {{ row.echoMap.classify.name }}
            </span>
            <span v-else>{{ row.classify }}</span>
          </template>
        </vxe-column>
        <vxe-column title="维护人" field="engineMaintainers" width="150" show-overflow="tooltip">
          <template #default="{ row }">
            <span v-if="row.engineMaintainers.length > 1">
              <el-tooltip :content="row.engineMaintainers.map(r=>r.echoMap.userId.name).join('、')" placement="top">
                <div>
                  {{ row.engineMaintainers.map(r=>r.echoMap.userId.name).join('、') }}
                </div>
              </el-tooltip>
            </span>
            <span v-else-if="row.engineMaintainers.length">
              <div>
                {{ row.engineMaintainers.map(r=>r.echoMap.userId.name).join('、') }}
              </div>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="访问URL" field="engineUrl" width="180" />
        <vxe-column title="创建人" field="createdBy" width="150">
          <template #default="{ row }">
            <span v-if="row.createdBy && row.echoMap && row.echoMap.createdBy">
              <vone-user-avatar
                :avatar-path="row.echoMap.createdBy.avatarPath"
                :name="row.echoMap.createdBy.name"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="巡检策略" field="patrolStrategy" width="120">
          <template #default="{ row }">
            {{ row.patrolStrategy }} 分钟
          </template>
        </vxe-column>
        <vxe-column field="updateTime" show-overflow="tooltip" title="更新时间" width="180" />
        <vxe-column field="description" title="描述" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('base_engine_edit')" icon="iconfont el-icon-application-edit" @click="editClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="权限分配" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('base_engineOrg_auth')" icon="iconfont el-icon-application-user-permission" @click="authDivision(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('base_engine_delete')" icon="iconfont el-icon-application-delete" @click="deleteRow(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getEngineList" />
    <!-- 引擎基本信息抽屉 -->
    <engineInfoDrawer v-if="engineDrawerParam.visible" v-bind="engineDrawerParam" v-model="engineDrawerParam.visible" :table-list="tableList" />

    <el-dialog title="选择巡检" v-model="dialogVisible" width="30%" :before-close="handleClose" :close-on-click-modal="false">
      <el-select v-model="value" placeholder="请选择" filterable>
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="seachSure">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 权限分配抽屉 -->
    <division v-if="divisionDrawerParam.visible" v-bind="divisionDrawerParam" v-model="divisionDrawerParam.visible" />
  </page-wrapper>
</template>

<script>
const sourceList = [
  {
    code: 'JENKINS_MASTER', icon: 'el-icon-JenkinsMaster', color: '#5777E5'
  },
  {
    code: 'FTP', icon: 'el-icon-FTP', color: '#2D85EB'
  },
  {
    code: 'SALT_STACK', icon: 'el-icon-SaltStack', color: '#4396B9'
  },
  {
    code: 'SCM_AGENT', icon: 'el-icon-ScmAgent', color: '#BD7FFA'
  },
  {
    code: 'GIT_LAB', icon: 'el-icon-GitLab', color: '#D94D36'
  },
  {
    code: 'DB_EXECUTOR', icon: 'el-icon-DBExecutor', color: '#EB8C6A'
  },
  {
    code: 'SONAR_QUBE', icon: 'el-icon-sonarqube', color: '#6CBCF0'
  },
  {
    code: 'SCRIPT_LIBRARY', icon: 'el-icon-ScriptLibrary', color: '#4CB569'
  },
  {
    code: 'SVN', icon: 'el-icon-ScriptLibrary', color: '#819FD6'
  },
  {
    code: 'JFROG', icon: 'el-icon-Jfrog', color: '#41BF47'
  },
  {
    code: 'HARBOR', icon: 'el-icon-Harbor', color: '#61BA33'
  },
  {
    code: 'GIT_EE', icon: 'el-icon-gitee', color: 'red'
  }

]
import {
  apiBaseEngineDelById,
  apiBaseEngineList,
  apiBaseEngineTestConnect,
  updatePatrolStrategy
} from '@/api/vone/base/engine'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import engineInfoDrawer from './function/engineInfo-dawer.vue'
import division from './function/division.vue'
import storage from 'store'

import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    engineInfoDrawer,
    division
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        },
        {
          key: 'state',
          name: '状态',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择状态',
          optionList: [
            {
              code: true,
              name: '正常',
              id: '1'
            },
            {
              code: false,
              name: '异常',
              id: '2'
            }
          ]
        },
        {
          key: 'classify',
          name: '引擎分类',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择分类'
        },
        {
          key: 'instance',
          name: '引擎类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择类型'
        }
      ],
      formData: {
        name: '',
        classify: '',
        instance: '',
        state: null
      },
      engineTypeDatas: [],
      sourceList,
      options: [
        { label: '默认(5分钟)', value: 5 },
        { label: '10分钟', value: 10 },
        { label: '15分钟', value: 15 },
        { label: '30分钟', value: 30 },
        { label: '60分钟', value: 60 },
        { label: '6小时', value: 360 }
      ],
      value: 5,
      name: '',
      dialogVisible: false,
      userMap: {},
      tableData: {},
      selecteTableData: [],
      tableLoading: false,
      engineDrawerParam: { visible: false },
      divisionDrawerParam: { visible: false },
      connect: false,
      actions: [
        {
          name: '批量删除',
          fn: () => {
            if (!this.getVxeTableSelectData('engineTable').length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.allDel()
          },
          // icon: 'iconfont el-icon-application-delete',
          disabled: !this.$permission('base_engine_delete')
        },
        {
          name: '设置巡检策略',
          icon: 'iconfont el-icon-application-setting',
          fn: () => {
            if (!this.getVxeTableSelectData('engineTable').length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.resDivisionAll()
          },
          disabled: !this.$permission('base_engineOrg_patrol_strate')

        }

      ],
      tableList: [],
      changeForm: {}
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.loadEngineType()
    this.onTypeChange()
  },
  created() {

  },
  methods: {
    /**
     * 获取引擎分类下拉框数据
     */
    async loadEngineType() {
      const res = await apiBaseDictNoPage(
        {
          type: 'ENGINE_CLASSIFY'
        }
      )
      if (!res.isSuccess) {
        // return this.$message.error(res.msg)
      }
      this.engineTypeDatas = res.data
      this.setData(this.defaultFileds, 'classify', res.data)
    },

    async onTypeChange(val) {
      // const ID = this.engineTypeDatas?.find(r => r.code == val)?.id
      const res = await apiBaseDictNoPage({
        // parentId: ID
        type: 'ENGINE_INSTANCE'
      })
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.setData(this.defaultFileds, 'instance', res.data)
    },
    authDivision(row) {
      this.divisionDrawerParam = { visible: true, id: row.id }
    },
    async getEngineList() {
      const search = this.$refs.searchForm

      try {
        this.tableLoading = true
        const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
        const sortObj = this.$refs.searchForm?.sortObj
        const params = {
          ...pageObj,
          ...sortObj,
          extra: {
            tableId: !search.isBtnSure ? search.viewId : null, // 比填,区分是查询还是切换筛选器
            ...this.extraData
          },
          model: { ...this.formData }
        }

        const res = await apiBaseEngineList(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        const sourceMap = this.sourceList.reduce((r, v) => (r[v.code] = v) && r, {})
        res.data.records.forEach(element => {
          element.icon = element.instance && element.instance.code && sourceMap[element.instance.code] ? sourceMap[element.instance.code].icon : 'el-icon-picture-outline-round'
          element.color = element.instance && element.instance.code && sourceMap[element.instance.code] ? sourceMap[element.instance.code].color : '#409EFF'
        })

        this.tableData = res.data
        this.tableList = res.data.records
      } catch (e) {
        this.tableLoading = false
      }
    },
    showInfo(row) {
      this.engineDrawerParam = { visible: true, id: row.id }
    },

    // 新增引擎
    clickAddEngine() {
      this.$router.push({
        name: 'base_engine_add'
      })
    },
    // 编辑引擎
    editClickRow(row) {
      const userInfo = storage.get('user')
      const user = row.engineMaintainers && row.engineMaintainers.length ? row.engineMaintainers.map(r => r.userId) : []

      if (user.includes(userInfo.id) || row.createdBy == userInfo.id) {
        this.$router.push({
          name: 'base_engine_edit',
          params: {
            id: row.id
          }
        })
      } else {
        this.$message.warning('当前登录账号不是创建人或维护人,不支持编辑')
      }
    },
    async deleteRow(item) {
      try {
        await this.$confirm(`确定删除【${item.name}】引擎吗?`, '删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
      } catch (e) {
        return
      }

      const res = await apiBaseEngineDelById([item.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getEngineList()
      // this.refreshTable(true)
    },
    // 批量删除
    async allDel() {
      const dateList = this.getVxeTableSelectData('engineTable')
      // const NAME = dateList.map((r) => r.name).join(',')
      const params = dateList.map((r) => r.id)
      await this.$confirm(`确定删除 ${dateList.length} 个引擎吗?`, '批量删除', {
        type: 'warning',
        customClass: 'delConfirm'
      })

      const res = await apiBaseEngineDelById(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getEngineList()
    },
    // 检测引擎连通性
    async connectEngine(row) {
      // this.connect = true
      this.$set(row, 'loading', true)
      const { data, isSuccess, msg } = await apiBaseEngineTestConnect({
        id: row.id,
        name: row.name
      })
      this.$set(row, 'loading', false)
      // this.connect = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      if (data) {
        this.$message.success('引擎连接成功')
      } else {
        this.$message.error('引擎连接失败')
      }

      this.getEngineList()
    },
    // 设置巡检策略
    resDivisionAll() {
      this.dialogVisible = true
      // TO DO
      // 缺少检测是否有操作人权限的接口
    },
    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    },
    async seachSure() {
      // var arr = []
      const arr = this.getVxeTableSelectData('engineTable').map(r => r.id)
      // this.selecteTableData.forEach((item) => {
      //   arr.push(item.id)
      // })
      const params = {
        engineIds: arr,
        patrolStrategy: this.value
      }
      const res = await updatePatrolStrategy(params)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      if (res.data) {
        this.$message.success('设置巡检策略成功')
        this.dialogVisible = false
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.danger {
  color: red !important;
}
.safe {
  color: #67c23a !important;
}

</style>

