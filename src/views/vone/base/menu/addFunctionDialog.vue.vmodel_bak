<template>
  <vone-drawer title="新增接口权限" size="sm" v-model:visible="visible" :before-close="onClose" class="drawer" :wrapper-closable="false">
    <el-form ref="nodeDetail" v-loading="formLoading" :model="nodeDetail" :rules="formRule"  label-position="top">
      <el-form-item label="父级菜单">
        <el-input v-model="parentMenu.name" disabled />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="nodeDetail.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="nodeDetail.code" placeholder="请输入编码" :disabled="id ? true : false" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="nodeDetail.description" type="textarea" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <div slot="footer">

      <el-button type="primary" :loading="saveLoading" @click="onSubmit">保存</el-button>
      <el-button @click="onClose">取消</el-button>
    </div>

  </vone-drawer>
</template>

<script>
import {
  apiBaseMenuAddFunction,
  apiBaseMenuFunctionGetById,
  apiBaseMenuEditFunction
} from '@/api/vone/base/meun'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    id: {
      type: String,
      default: undefined
    },
    parentMenu: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      nodeDetail: {
        menuId: this.parentMenu.id ? this.parentMenu.id : this.$route.params.id
      },
      formLoading: false,
      formRule: {
        name: [{ required: true, message: '请输入名称' }, {
          max: 20, message: '输入内容不能超过20个字符'
        }],
        path: [{ required: true, message: '请输入url' }],
        code: [{ required: true, message: '请输入编码' }],
        description: [{ required: false, message: '请输入描述' }, {
          max: 255, message: '输入内容不能超过255个字符'
        }]
      },
      saveLoading: false
    }
  },

  mounted() {
    if (this.id) {
      this.getDetailsById()
    }
    if (this.parentMenu) {
      this.$set(this.nodeDetail, 'name', this.parentMenu.name)
    }
  },
  methods: {
    onClose() {
      // this.$refs.nodeDetail.resetFields()
      this.$emit('update:visible', false)
    },
    // 查详情
    async getDetailsById() {
      this.formLoading = true
      const res = await apiBaseMenuFunctionGetById(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.nodeDetail = res.data
    },
    async onSubmit() {
      await this.$refs.nodeDetail.validate()
      if (this.id) {
        this.editApp()
        return
      }

      this.saveLoading = true
      apiBaseMenuAddFunction(this.nodeDetail).then(res => {
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('保存成功')
        this.onClose()
        this.$emit('success')
      }).catch(() => {
        this.saveLoading = false
      })
    },
    async editApp() {
      try {
        this.saveLoading = true
        const res = await apiBaseMenuEditFunction(
          {
            ...this.nodeDetail
          }
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('修改成功')
        this.onClose()
        this.$emit('success')
      } catch (e) {
        this.saveLoading = false
      }
    }

  }

}
</script>

<style lang="scss" scoped>
.footer {
  position: absolute;
  width: 100%;
  right: 0;
  bottom: 0;
  padding: 16px 20px;
  background-color: var(--main-bg-color, #fff);
  border-top: 1px solid var(--disabled-bg-color, #ebeef5);
  text-align: right;
}
.drawer {
  :deep(.el-drawer__body) {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 74px;
  }
}
</style>
