<template>
  <el-dialog :title="title" width="600" :visible.sync="visible" :before-close="onClose" :close-on-click-modal="false">
    <el-form ref="addForm" v-loading="loading" :model="addForm" :rules="rules" label-position="left">
      <el-row type="flex" justify="space-between" :gutter="12">
        <el-col :span="12">
          <el-form-item label="头像" prop="avatarPath" class="avatarPath centerLabel">
            <el-popover ref="popoverRef" placement="bottom-start" trigger="hover">
              <el-row class="popover">
                <template>
                  <el-col v-for="item in avatarList" :key="item.name" :span="4">
                    <div class="userhead item">
                      <a @click="changeIcon(item)">
                        <vone-user-avatar
                          :avatar-path="item.name"
                          :avatar-type="true"
                          :show-name="false"
                          height="45px"
                          width="45px"
                        />
                      </a>
                    </div>
                  </el-col>
                </template>
              </el-row>
              <span slot="reference">
                <vone-user-avatar
                  :avatar-path="addForm.avatarPath"
                  :avatar-type="true"
                  :show-name="false"
                  height="45px"
                  width="45px"
                />
              </span>
            </el-popover>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state" class="centerLabel" style="height: 45px">
            <el-switch
              v-model="addForm.state"
              class="openSwitch"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter="12">
        <el-col :span="id ? 12 : 24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入名称" :disabled="id && addForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号" prop="account">
            <el-input v-model="addForm.account" placeholder="请输入账号" :disabled="id && addForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
        </el-col>
        <el-col v-if="!id" :span="12">
          <el-form-item
            label="登录密码"
            prop="password"
            :rules="[
              {required: true,message: '请输入登录密码',trigger: 'change'},
              {
                pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
                message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符',
                trigger: 'change'
              }
            ]"
          >
            <el-input v-model="addForm.password" type="password" show-password placeholder="请输入登录密码" :disabled="id && addForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="集团工号" prop="idCard">
            <el-input v-model.trim="addForm.idCard" placeholder="请输入集团工号" :disabled="id && addForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="addForm.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属机构" prop="orgId">
            <vone-tree-select v-model="addForm.orgId" search-nested :tree-data="orgData" placeholder="请选择机构" :disabled="id && addForm.type == 'LOCAL_TYPE'" @select="orgChange" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="addForm.type" filterable>
              <el-option v-for="item in userType" :key="item.id" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属角色" prop="userRoles">
            <el-select v-model="addForm.userRoles" filterable multiple collapse-tags>
              <el-option v-for="mode in modes" :key="mode.id" :label="mode.name" :value="mode.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="addForm.mobile" placeholder="请输入手机号" :disabled="id && addForm.type == 'LOCAL_TYPE'" />
          </el-form-item>
        </el-col>
        <el-col :span="12" class="reportWork">
          <el-form-item label="报工" prop="reportWork">
            <el-radio-group v-model="addForm.reportWork">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input v-model.trim="addForm.description" type="textarea" placeholder="请输入描述" />
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button v-if="!id" @click="saveUser(1)">保存并添加下一个</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveUser(0)">确定</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import { orgList } from '@/api/vone/base/org'
import { addUser, getUserDetail } from '@/api/vone/base/user'
import { batchQuery } from '@/api/vone/base/role'
// import { getUserGroupQuery } from '@/api/vone/base/user-group'
import { gainTreeList } from '@/utils'
import { avatarList } from '@/assets/avatar/avatar'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { cloneDeep } from 'lodash'
// // 集团工号码校验
// const IDCardValidate = (rule, value, callback) => {
//   const reg = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/
//   if (!reg.test(value)) {
//     return callback(new Error('请输入正确的集团工号'))
//   } else {
//     callback()
//   }
// }
export default {
  props: {
    id: {
      type: String,
      default: null
    },
    orgId: {
      type: String,
      default: null
    },
    title: {
      type: String,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      avatarList,
      loading: false,
      saveLoading: false,
      save: '保存并添加下一个',
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_.\\u4e00-\\u9fa5]{1,50}$',
            message: '请输入不超过50位由字母、数字或者下划线或.组成的名称',
            trigger: 'change'
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          }
        ],
        userRoles: [
          {
            required: true,
            message: '请选择角色',
            trigger: 'change',
            type: 'array'
          }
        ],
        account: [
          {
            required: true,
            message: '请输入长度不超过50个字符的登录账号',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_@.]{1,50}$',
            message: '请输入不超过50位由字母、数字或者下划线或.或@组成的登录账号',
            trigger: 'change'
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的登录账号',
            trigger: 'change'
          }
        ],
        idCard: [
          {
            required: false,
            message: '请输入集团工号',
            trigger: 'blur'
          }
          // {
          //   max: 18,
          //   message: '请输入长度不超过18位的集团工号',
          //   trigger: 'change'
          // }
        ],
        email: [
          {
            required: true,
            message: '请输入邮箱地址',
            trigger: 'blur'
          },
          {
            pattern: '^\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}$',
            message: '请输入正确的邮箱地址',
            trigger: 'change'
          },
          {
            max: 200,
            message: '请输入长度不超过200个字符的邮箱地址',
            trigger: 'change'
          }
        ],
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          },
          {
            pattern: '^0?(13|14|15|16|17|18|19)[0-9]{9}$',
            message: '请输入正确的手机号',
            trigger: 'blur'
          }
        ],
        orgId: [
          {
            required: true,
            message: '请选择所属机构',
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }
        ],
        description: [
          {
            required: false,
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change'
          }
        ]
      },
      addForm: {
        reportWork: false,
        account: '',
        name: '',
        orgId: null,
        idCard: '',
        userRoles: [],
        password: '',
        email: '',
        mobile: '',
        state: true,
        description: '',
        avatar: '',
        type: '',
        avatarPath: 'avatar16',
        avatarType: true
      },
      // users: [],
      modes: [],
      orgData: [],
      userType: []
    }
  },
  mounted() {
    this.getOrgList() // 机构
    this.getUserType()
    if (this.id) {
      this.getUserDetail()
      this.getRole(this.orgId) // 角色
    }
  },
  methods: {
    getUserType() {
      apiBaseDictNoPage({ 'type': 'USER_TYPE' }).then(res => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.userType = res.data
        // if (this.id) {
        //   this.userType = res.data
        // } else {
        //   const userTypeData = res.data?.filter(item => item.code != 'LOCAL_TYPE') || []
        //   this.userType = userTypeData
        //   this.$set(this.addForm, 'type', userTypeData?.[0]?.code || '')
        // }
      })
    },
    changeIcon(item) {
      this.$set(this.addForm, 'avatarPath', item.name)
      this.$refs.popoverRef.doClose()
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.addForm.resetFields()
    },
    orgChange(val) {
      this.$set(this.addForm, 'userRoles', [])
      // this.$refs.addForm.clearValidate(['userRoles'])

      if (val?.id) {
        this.getRole(val.id)
      }
    },
    // 批量角色
    getRole(orgId) {
      batchQuery({ readonly: false, orgId: orgId }).then(res => {
        this.modes = res.data.filter(r => r.state)
      })
    },
    getUserDetail() {
      this.loading = true
      getUserDetail(this.id).then(res => {
        if (res.isSuccess) {
          this.$set(res.data, 'reportWork', res.data.reportWork || false)
          this.addForm = res.data
          var arr = []
          if (res.data.userRoles) {
            res.data.userRoles.forEach(item => {
              item?.roleId && arr.push(item.roleId)
            })
          }

          this.$set(this.addForm, 'userRoles', arr)
          this.loading = false
        }
      })
    },
    // 查询所有机构
    getOrgList() {
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgData = orgTree
      })
    },
    back() {
      this.$emit('back')
    },
    async saveUser(type) {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }
      try {
        const formData = cloneDeep(this.addForm)
        const arr = []
        formData.userRoles.forEach(item => {
          arr.push({ roleId: item })
        })
        formData.userRoles = arr

        this.saveLoading = true
        const res = await addUser(formData)

        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success(this.id ? '修改成功' : '保存成功')
        this.$emit('success')
        if (type == 0) {
          this.onClose()
        } else {
          this.$refs.addForm.resetFields()
        }
      } catch (e) {
        this.saveLoading = false
        return
      }
    }
  }
}
</script>

<style  lang="scss" scoped >
:deep(.vone-el-drawer__layout) {
  padding: 20px;
  overflow-y: auto !important;
  height: calc(100vh - 128px);
}

.popover {
  width: 400px;
  text-align: center;
  .item {
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: var(--main-bg-color,#fff);
    &:hover {
      background-color: var(--hover-bg-color,#f5f6fa);
    }
  }
}
.avatarPath{
   ::v-deep .el-form-item__content{
     width: 35%;
   }
}
:deep(.vue-treeselect--open-below:not(.vue-treeselect--append-to-body) .vue-treeselect__menu-container) {
  top: 74px
}
.reportWork{
  :deep(.el-form-item__label) {
      width: 100%;
    }
}
</style>

