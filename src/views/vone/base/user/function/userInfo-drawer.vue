<template>
  <vone-drawer v-model="visible" size="lg" :before-close="onClose">
    <div slot="title" class="drawer-title">
      <div class="drawer-title-text">{{ `用户【${basicData.name}】详情` }}
        <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />

        <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
      </div>
    </div>
    <div v-loading="pageLoading" class="pageBox">
      <div class="title">
        <strong>
          基本信息
        </strong>
      </div>
      <div class="formBox">

        <vone-desc>
          <vone-desc-item label="名称">
            <vone-user-avatar :avatar-path="basicData.avatarPath" :name="basicData.name" />
          </vone-desc-item>
          <vone-desc-item label="账号">
            {{ basicData.account }}
          </vone-desc-item>
          <vone-desc-item label="集团工号">
            {{ basicData.idCard }}
          </vone-desc-item>
          <vone-desc-item label="邮箱">
            {{ basicData.email }}
          </vone-desc-item>
          <vone-desc-item label="所属机构">
            <span v-if="basicData.orgId && basicData.echoMap && basicData.echoMap.orgId">
              {{ basicData.echoMap.orgId.name }}
            </span>
            <span v-else>
              {{ basicData.orgId }}
            </span>
          </vone-desc-item>
          <vone-desc-item label="手机号">
            {{ basicData.mobile }}
          </vone-desc-item>
          <vone-desc-item label="状态">
            {{ basicData.state ? "启用" :"禁用" }}
          </vone-desc-item>
          <vone-desc-item label="是否报工">
            <span>
              {{ basicData.reportWork ? '是' :'否' }}
            </span>
          </vone-desc-item>
          <vone-desc-item label="描述">
            {{ basicData.description }}
          </vone-desc-item>

        </vone-desc>

      </div>
      <div class="title">
        <strong>
          关联信息
        </strong>
      </div>

      <div class="formBox">

        <el-tabs v-model="activeName" type="card">

          <el-tab-pane label="角色" name="owner">
            <el-table :data="ownerData" class="vone-table">
              <template>
                <el-table-column prop="roleId" label="角色名称">
                  <template slot-scope="scope">
                    <span v-if="scope.row.roleId && scope.row.echoMap && scope.row.echoMap.roleId">
                      {{ scope.row.echoMap.roleId.name }}

                    </span>

                  </template>

                </el-table-column>
                <el-table-column prop="description" label="角色描述">
                  <template slot-scope="scope">
                    <span v-if="scope.row.roleId && scope.row.echoMap && scope.row.echoMap.roleId">
                      {{ scope.row.echoMap.roleId.description }}
                    </span>

                  </template>
                </el-table-column>

              </template>
            </el-table>
          </el-tab-pane>

        </el-tabs>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>
<script>

import {
  getUserDetail
} from '@/api/vone/base/user'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    tableList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentIndex: undefined, // 当前数据的索引
      pageLoading: false,
      ownerData: [],
      basicData: {},
      activeName: 'owner'

    }
  },
  watch: {
    visible(v) {
      if (!v) {
        this.activeName = 'owner'
        return
      }
    }
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex(item => item.id === this.id)
    this.activeName = 'owner'
    this.getBasicInfo()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },

    // 基本信息
    async getBasicInfo(val) {
      try {
        this.pageLoading = true
        const { data, isSuccess, msg } = await getUserDetail(val || this.id)
        this.pageLoading = false
        if (!isSuccess) {
          return this.$message.error(msg)
        }
        this.basicData = data
        this.ownerData = data.userRoles ? data.userRoles : []
      } catch (e) {
        this.pageLoading = false
      }
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    }

  }
}
</script>

<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .formBox {
    padding: 10px;
  }
  :deep(.el-form .el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-form-item__content) {
    display: flex;
  }
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>

