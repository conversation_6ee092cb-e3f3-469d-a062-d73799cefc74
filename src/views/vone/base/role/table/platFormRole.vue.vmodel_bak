<template>
  <div>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="baseRoleTable"
        class="vone-vxe-table"
        height="auto"
        border
        resizable
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="名称" field="name" min-width="150">
          <template #default="{ row }">
            <a @click="showInfo(row)">
              {{ row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="角色类型" field="type" width="100">
          <template #default="{ row }">
            <el-tag v-if="!row.orgId" style="color:var(--Blue-10); background:var(--Blue--50); border-color:var(--Blue--50)" effect="dark">公有</el-tag>
            <el-tag v-else style="color:var(--Orange-10); background:var(--Orange--50); border-color:var(--Orange--50)" type="warning" effect="dark">私有</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="state" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.state"
              class="openSwitch"
              :disabled="row.readonly || !$permission('base_role_edit')"
              active-text="启用"
              inactive-text="禁用"
              @change="editStatus(row)"
            />
          </template>
        </vxe-column>
        <vxe-column field="updateTime" show-overflow="tooltip" title="更新时间" width="180" />
        <vxe-column title="更新人" field="updatedBy" width="150">
          <template #default="{ row }">
            <span v-if="row.updatedBy && row.echoMap && row.echoMap.updatedBy">
              <vone-user-avatar
                :avatar-path="row.echoMap.updatedBy.avatarPath"
                :avatar-type="row.echoMap.updatedBy.avatarType"
                :name="row.echoMap.updatedBy.name"
              />
            </span>
            <span v-else> -- </span>
          </template>
        </vxe-column>
        <vxe-column title="所属机构" field="org" width="150">
          <template #default="{ row }">
            <span v-if="row.echoMap &&row.echoMap.orgId && row.echoMap.orgId.name">{{ row.echoMap.orgId.name }}</span>
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('base_role_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('base_role_delete')" icon="iconfont el-icon-application-delete icon_click" @click="delRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="iconfont el-icon-application-member" :disabled="row.readonly || !$permission('base_role_user')" :command="() =>handleUser(row)">
                    <span>分配人员</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-setting" :disabled="!$permission('base_role_auth')" :command="() =>handleSetting(row)">
                    <span>配置权限</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getRoleList" />
    <!-- 新增编辑弹窗 -->
    <add-or-edit
      v-if="dialogVisible"
      ref="editDialog"
      :now-row="nowRow"
      v-model:dialog-visible="dialogVisible"
      :action-type="actionType"
      @success="getRoleList"
    />

    <!-- 分配人员弹窗 -->
    <userRole
      v-if="userVisible"
      v-model:dialog-visible="userVisible"
      :role="nowRow"
      @success="getRoleList"
    />

    <!-- 设置人员菜单权限 -->
    <menuSetting
      v-if="menuVisible"
      ref="menuSetting"
      v-model:menu-visible="menuVisible"
      :role="nowRow"
    />

    <!-- 角色详情 -->
    <roleInfoDrawer
      v-if="roleDrawerParam.visible"
      v-bind="roleDrawerParam"
      v-model:visible="roleDrawerParam.visible"
      :table-list="tableList"
    />
  </div>
</template>
<script>

import {
  searchRoleDetail,
  delRole,
  apiBaseRoleList,
  editRole
} from '@/api/vone/base/role'
import addOrEdit from '../addOrEdit.vue'
import userRole from '../userRole.vue'
import menuSetting from '../menuDrawer.vue'
import roleInfoDrawer from '../function/role-info-drawer.vue'

export default {
  components: {
    addOrEdit,
    userRole,
    menuSetting,
    roleInfoDrawer
  },
  props: {
    formData: {
      type: Object,
      default: null
    },
    extraData: {
      type: Object,
      default: null
    },
    tableHeight: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableData: {},
      dialogVisible: false, // 新增和编辑弹窗
      userVisible: false, // 分配人员弹窗
      menuVisible: false, // 人员菜单权限
      actionType: 'add',
      tableLoading: false,
      nowRow: {},
      roleDrawerParam: { visible: false },
      tableList: []

    }
  },
  mounted() {
  },
  methods: {
    // 详情
    showInfo(row) {
      this.roleDrawerParam = { visible: true, id: row.id }
    },
    editStatus(row) {
      editRole(row).then(res => {
        if (res.isSuccess) {
          this.$message.success('状态修改成功')
        }
      })
    },
    async getRoleList(ref) {
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      let params = {}
      // const search = this.$refs[ref]
      params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await apiBaseRoleList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableList = res.data.records
    },
    // 修改角色
    editRow(row) {
      // 查询当前行角色的信息
      searchRoleDetail(row.id).then((res) => {
        if (res.isSuccess) {
          this.nowRow = res.data
          this.dialogVisible = true
          this.actionType = 'edit'
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 删除角色
    delRow(row) {
      this.$confirm(`是否删除角色【${row.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      }).then(() => {
        delRole(Array(row.id)).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除角色成功')
            this.getRoleList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('baseRoleTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        this.tableLoading = true
        const selectId = selectData.map(r => r.id)
        const res = await delRole(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getRoleList()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 分配人员
    handleUser(row) {
      this.nowRow = row
      this.userVisible = true
    },
    handleSetting(row) {
      this.nowRow = row
      this.menuVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.switchStyle  :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}

.testTabs {
  width: 100%;
  justify-content: center;
  align-items: center;
  display: flex;
  :deep() {

    .el-radio-button {
      width: 94%;
      display: flex;
      border: 4px solid var(--tab-bg-color);
      background-color: var(--tab-bg-color);
    }
		.el-radio-button__orig-radio:checked + .el-radio-button__inner{
			color: #4287ff;
      background-color:#fff;
		}
    .el-radio-button__inner  {
			color: var(--tab-font-color);
      border: none;
			border-radius: 2px;
			background-color: var(--tab-bg-color);
    }
  }
}
</style>
