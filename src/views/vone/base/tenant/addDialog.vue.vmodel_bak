<template>
  <el-dialog :title="title" width="40%" v-model:visible="visible" :before-close="onClose" :close-on-click-modal="false" destroy-on-close <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->>
    <el-form v-model="tenantFrom" v-loading="pageLoading">
      <el-form-item label="企业编码" prop="code">
        <el-input v-model="tenantFrom.code" placeholder="请输入企业编码" />
      </el-form-item>
      <el-form-item label="企业名称" prop="name">
        <el-input v-model="tenantFrom.name" placeholder="请输入企业名称" />
      </el-form-item>
      <el-form-item label="责任人" prop="user">
        <el-input v-model="tenantFrom.user" placeholder="请输入责任人" />
      </el-form-item>
      <el-form-item label="有效期" prop="time">
        <el-date-picker v-model="tenantFrom.time" type="datetime" placeholder="选择有效期" style="width:100%" />
      </el-form-item>
      <el-form-item label="企业简介" prop="info">
        <el-input v-model="tenantFrom.info" placeholder="请输入企业简介" />
      </el-form-item>
    </el-form>

    <div class="footer">
      <el-button @click="onClose">取消</el-button>&nbsp;
      <el-button type="primary" :loading="saveLoading" @click="onClose">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { apiBaseTenantInfo, apiBaseTenantSave } from '@/api/vone/base/tenant'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      tenantFrom: {},
      saveLoading: false,
      pageLoading: false
    }
  },
  mounted() {
    if (this.id) {
      this.getTenantInfo()
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    async getTenantInfo() {
      this.pageLoading = true
      const res = await apiBaseTenantInfo([
        this.id
      ])
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
    },

    async saveTenant() {
      this.saveLoading = true
      const res = await apiBaseTenantSave(
        this.tenantFrom
      )
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.onClose()
      this.$emit('success')
    }
  }

}
</script>

<style lang="scss" scoped>
.footer {
  text-align: right;
}
</style>

