<template>
  <div class="sectionPageContent">
    <div class="leftSection" :style="{'width': openFlag ? '400px' : '240px' }">
      <div class="header">
        <span>团队树级</span>
        <div class="search">
          <el-popover v-model="visible" placement="bottom-start" width="300" popper-class="table-search-form org-search-form">
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">筛选团队</span>
              </div>
              <div class="search-form">
                <el-form ref="searchForm" inline label-position="top">
                  <el-form-item label="团队名称" prop="name">
                    <el-input v-model.trim="searchForm.name" style="width: 100%" placeholder="输入机构名称" />
                  </el-form-item>
                  <el-form-item label="负责人" prop="leaderBy">
                    <vone-remote-user v-model="searchForm.leaderBy" style="width: 100%" placeholder="选择负责人" />
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer org-footer">
                <el-button plain @click="reset">重置</el-button>
                <el-button type="primary" @click="searchTree">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="筛选" placement="top">
                <i :class="['iconfont','el-icon-application-filter',visible ? 'active' : '']" style="margin-left: 12px;cursor: pointer;" />
              </el-tooltip>
            </span>
          </el-popover>
          <!-- <el-divider style="margin:-3px 8px 0px 8px; " direction="vertical" /> -->
          <el-tooltip class="item" effect="dark" content="新增团队" placement="top">
            <i style="margin-left: 8px;cursor: pointer;" class="iconfont el-icon-tips-plus" @click="add" />
          </el-tooltip>
          <el-divider style="margin: 0 10px 0 6px" direction="vertical" />
          <el-tooltip v-if="!openFlag" class="item" effect="dark" content="展开" placement="top">
            <i class="iconfont el-icon-direction-menu-unfold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
          <el-tooltip v-else class="item" effect="dark" content="收起" placement="top">
            <i class="iconfont el-icon-direction-menu-fold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
        </div>
      </div>
      <div class="treeContent">
        <vone-empty v-if="data.length == 0" />
        <el-tree
          v-else
          ref="teamTree"
          v-loading="treeLoading"
          class="tree custom-tree-icon"
          :data="data"
          node-key="id"
          default-expand-all
          check-strictly
          :expand-on-click-node="false"
          :current-node-key="currentNode"
          @node-click="changeUser"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <el-tooltip v-showTooltips="{'width': openFlag ? '400' : '240'}" :content="data.label" placement="top" class="node-label">
              <div>
                <svg-icon v-if="data.type == 'OrgTeam'" icon-class="setting-team" />
                <svg-icon v-else-if="data.type == 'ProjectTeam'" icon-class="setting-project" />
                <svg-icon v-else icon-class="setting-product" />
                <span class="label" :data-node="node.level">{{ data.label }}</span>
              </div>
            </el-tooltip>
            <span v-if="data.echoMap && data.echoMap.leaderBy" class="manager">
              <el-tag effect="dark">{{ data.echoMap && data.echoMap.leaderBy && data.echoMap.leaderBy.name }}</el-tag>
            </span>
            <span v-if="data.type == 'OrgTeam'" class="operation-tree-icon">
              <el-button type="text" size="mini" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('base_org_add')" @click.stop="() => add(node, data)" />
              <el-button type="text" size="mini" icon="iconfont el-icon-application-edit" :disabled="!$permission('base_org_edit')" @click.stop="() => edit(node, data)" />
              <el-button type="text" size="mini" icon="iconfont el-icon-application-delete" :disabled="!$permission('base_org_delete')" @click.stop="() => remove(node, data)" />
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="rightSection">
      <div class="rightTopSection">
        <div class="title">
          <svg-icon v-if="clickNode && teamType =='ProductTeam'" icon-class="setting-product" />
          <svg-icon v-else-if="clickNode && teamType == 'ProjectTeam'" icon-class="setting-project" />
          <svg-icon v-else icon-class="setting-team" />
          {{ clickNode && clickNode.name || '暂无团队' }}</div>
        <div class="detail">
          <span v-if="clickNode && teamType == 'OrgTeam'">隶属机构<p>{{ clickNode.echoMap &&clickNode.echoMap.orgId &&clickNode.echoMap.orgId.name || '-' }}</p></span>
          <span v-else-if="clickNode && teamType == 'ProjectTeam'">所属项目<p>{{ clickNode.echoMap &&clickNode.echoMap.projectInfos &&clickNode.echoMap.projectInfos[0].name }}</p></span>
          <span v-else>所属产品<p>-</p></span>
          <span>上级团队<p>{{ clickNode && clickNode.echoMap &&clickNode.echoMap.parentId &&clickNode.echoMap.parentId.name || '-' }}</p></span>
          <span>类型<p>{{ clickNode && clickNode.echoMap &&clickNode.echoMap.type &&clickNode.echoMap.type.name || '-' }}</p></span>
          <span>负责人<p>
            <vone-user-avatar
              v-if="clickNode && clickNode.echoMap && clickNode.echoMap.leaderBy"
              :avatar-path="clickNode.echoMap.leaderBy.avatarPath"
              :name="clickNode.echoMap.leaderBy.name"
              :show-name="true"
              height="20px"
              width="20px"
            /></p>
          </span>
        </div>
      </div>
      <div class="rightBottomSection">
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic
              ref="searchForm"
              table-search-key="teamUserTable"
              v-model:model="formData"
              v-model:default-fileds="defaultFileds"
              show-basic
              v-model:extra="extraData"
              :show-column="false"
              :table-ref="$refs['teamUserTable']"
              @getTableData="getTableData"
            />
          </template>
          <template v-if="clickNode && teamType == 'OrgTeam'" slot="actions">
            <el-button icon="iconfont el-icon-tips-plus-circle" type="primary" @click="addUser">添加成员</el-button>
            <el-dropdown trigger="click" @command="(e) => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template slot="fliter">
            <vone-search-filter
              v-model:extra="extraData"
              v-model:model="formData"
              v-model:default-fileds="defaultFileds"
              @getTableData="getTableData"
            />
          </template>
        </vone-search-wrapper>
        <div :style="{height: tableHeight}">
          <vxe-table
            ref="teamUserTable"
            class="vone-vxe-table"
            height="auto"
            border
            resizable
            show-overflow="tooltip"
            :empty-render="{name: 'empty'}"
            :loading="pageLoading"
            :data="tableData.records"
            :column-config="{
              minWidth:'120px'
            }"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-column type="checkbox" width="36" fixed="left" />
            <vxe-column title="账号" field="account" width="150" fixed="left" />
            <vxe-column title="名称" field="name" width="150">
              <template #default="{ row }">
                <vone-user-avatar :avatar-path="row.avatarPath" :avatar-type="row.avatarType" :name="row.name" />
              </template>
            </vxe-column>
            <vxe-column title="邮箱" field="email" width="150" />
            <vxe-column title="角色" field="role" width="150">
              <template #default="{ row }">
                {{ row.allRole }}
              </template>
            </vxe-column>
            <vxe-column title="用户类型" field="type" width="120">
              <template #default="{ row }">
                {{ userTypeName(row) }}
              </template>
            </vxe-column>
            <vxe-column field="updateTime" show-overflow="tooltip" title="更新时间" width="180" />
            <vxe-column field="mobile" title="手机号" />
            <vxe-column v-if="teamType =='OrgTeam'" title="操作" fixed="right" align="left" width="50">
              <template #default="{ row }">
                <template>
                  <el-tooltip class="item" content="删除" placement="top">
                    <el-button type="text" icon="iconfont el-icon-application-delete" @click="deleteUser(row)" />
                  </el-tooltip>
                </template>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
      </div>
    </div>
    <addEdit v-if="dialogVisible" ref="editDialog" v-model:dialog-visible="dialogVisible" :edit-title="editTitle" :click-node="editNode" @success="getTeamList" />
    <userDialog v-model:visible="userVisible" :click-node="clickNode" @success="getTableData" />
  </div>
</template>
<script>
import { teamList, delTeam, getUserListById, delUserListById, queryDetail, getProjectUserList } from '@/api/vone/base/team'
import addEdit from './add-edit-dialog.vue'
import userDialog from './user-dialog.vue'
import { gainTreeList } from '@/utils'
export default {
  components: {
    addEdit,
    userDialog
  },
  data() {
    return {
      extraData: {}, defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        }
      ],
      treeLoading: false,
      searchText: '',
      data: [],
      pageLoading: false,
      tableData: {},
      formData: {
        orgId: '',
        name: '',
        account: ''
      },
      dialogVisible: false,
      editTitle: '',
      userVisible: false,
      isShowButton: true,
      visible: false,
      searchForm: {},
      clickNode: {},
      selecteTableData: [],
      currentNode: '',
      editNode: {},
      openFlag: false,
      actions: [
        {
          name: '批量删除',
          fn: this.deleteUser
        }
      ]
    }
  },
  computed: {
    teamType() {
      return this.clickNode?.type
    },
    userTypeName() {
      return function(row) {
        return row.echoMap?.type?.name ? row.echoMap?.type?.name : row.echoMap?.userType?.name || ''
      }
    },
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height} - 94px)`
    }
  },
  mounted() {
    this.getTeamList()
  },
  methods: {
    getTeamList() {
      this.treeLoading = true
      teamList(this.searchForm).then((res) => {
        this.treeLoading = false
        const orgTree = gainTreeList(res.data)
        this.data = orgTree
        this.clickNode = this.data[0]
        this.currentNode = this.data[0]?.id
        this.getTableData()
      })
    },
    reset() {
      this.searchForm = { name: '', leadingBy: '' }
      this.$nextTick(() => {
        this.getTeamList()
        this.visible = false
      })
    },
    searchTree() {
      this.visible = false
      this.getTeamList()
    },
    add(node, data) {
      this.dialogVisible = true
      this.editTitle = '新增团队'
      this.editNode = {}
      this.editNode = data || {}
    },
    edit(node, data) {
      this.dialogVisible = true
      this.editNode = {}
      this.editNode = JSON.parse(JSON.stringify(data))
      this.editTitle = '编辑团队'
    },
    remove(node, data) {
      if (data.children) {
        return this.$message.warning('当前团队下有子级团队,不允许直接删除，请先删除子团队')
      }
      this.$confirm(`是否删除团队【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      }).then(() => {
        delTeam([data.id]).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTeamList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }).catch(() => { })
    },
    changeUser(node) {
      this.clickNode = node
      this.getTableData()
    },
    async getTableData() {
      if (!this.clickNode?.id) {
        return
      }
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      let res = null
      if (this.clickNode?.type == 'ProjectTeam') {
        await queryDetail(this.clickNode?.id).then(res => {
          if (res.isSuccess) {
            this.clickNode = res.data
          } else {
            this.$message.warning(res.msg)
          }
        })
        params.model['teamId'] = this.clickNode?.id
        params.model['projectId'] = this.clickNode.echoMap?.projectInfos?.[0]?.id
        res = await getProjectUserList(params)
        res.data.records.forEach(element => {
          element.allRole = element.echoMap?.projectRoles ? element.echoMap?.projectRoles.map(r => r.name).join('、') : ''
        })
      } else {
        res = await getUserListById(this.clickNode.id, params)
        res.data.records.forEach(element => {
          element.allRole = element.userRoles && element.userRoles.length ? element.userRoles.map(r => r.echoMap.roleId.name).join('、') : ''
        })
      }
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.pageLoading = false
      this.tableData = res.data
    },
    // 添加成员
    addUser() {
      this.userVisible = true
    },
    // 表格勾选
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs['teamUserTable'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs['teamUserTable'].getCheckboxRecords()
    },
    removeAll() {

    },
    // 删除成员
    deleteUser(row) {
      const Ids = []
      if (row && row.id) {
        Ids.push(row.id)
      } else {
        this.selecteTableData.map(item => { Ids.push(item.id) })
      }
      if (Ids.length == 0) {
        return this.$message.warning('请选择成员')
      }
      this.$confirm('是否删除成员?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      }).then(() => {
        delUserListById(this.clickNode.id, Ids).then((res) => {
          if (res.isSuccess) {
            this.selecteTableData = []
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }).catch(() => { })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.sectionPageContent {
  display: flex;
  justify-content: center;
}
.org-search-form .search-main{
  .search-header {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--font-main-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
  .search-form {
    padding: 16px;
    .el-form-item {
      width: 100%;
    }
  }
  .org-footer {
    text-align: right;
    padding: 12px 20px;
    border-top: 1px solid var(--disabled-bg-color);
  }
}
.leftSection {
	width: 240px;
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		span {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
		.search {
      display: flex;
      align-items: center;
      :deep(.el-divider--vertical) {
        margin: 0 10px 0 10px;
      }
			.iconfont {
				cursor: pointer;
				color: var(--font-second-color);
			}
			.iconfont:hover {
				color: var(--main-theme-color);
			}
			.iconfont.active {
				color: var(--main-theme-color);
			}
		}
	}
	.treeContent {
		margin-top:8px;
		height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px);
		overflow-y: overlay;
		.custom-tree-node {
			flex: 1;
			width: calc(100% - 90px);
      height: 100%;
      display: flex;
      align-items: center;
		}
		.el-tree-node__content {
      height: 36px;
      color: var(--font-main-color);
			display: inline-block;
			.node-label {
        display: inline-block;
        width: calc(100% - 90px);
				overflow: hidden;
				white-space: nowrap;
				text-overflow:ellipsis;
				.svg-icon {
					width: 16px;
					height: 16px;
          vertical-align: -0.2em;
				}
      }
			.manager {
        position: absolute;
        right: 16px;
      }
      .operation-tree-icon {
        .el-button {
          padding: 0px;
          height: unset;
          line-height: unset;
          min-width: unset;
          font-size: 16px;
          color: var(--font-second-color);
        }

        .el-button.is-disabled {
          background-color: unset;
          border: unset;
        }
        .el-button:hover {
          color: var(--main-theme-color);
        }
        opacity: 0;
				position: absolute;
        right: 16px;
      }

      &:hover {
				.manager {
					display: none
				}
        .operation-tree-icon {
          opacity: 1;
					background: var(--hover-bg-color);
        }
      }
			.svg-icon {
				margin-right: 4px;
			}
		}
  }
}
.rightSection {
  background: none;
  padding: 0;
  box-shadow: none
}
.rightTopSection {
	border-radius: 4px;
	padding: 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	height: 84px;
	color: var(--font-main-color);
	.title {
		font-weight: 500;
		line-height: 22px;
		display: flex;
		.svg-icon {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}
	}
	.detail {
		margin-top: 8px;
		line-height: 22px;
		span {
			color: var(--font-second-color);
			margin-right: 56px;
			display:inline-flex
		}
		p {
			display: inline-block;
			margin: 0px;
			margin-left:12px;
			color: var(--font-main-color);
		}
    :deep(.avatar span) {
      margin-left: 4px;
    }
	}
}
.rightBottomSection {
  position: relative;
	border-radius: 4px;
	padding: 16px 16px 10px 16px;
	box-shadow: var(--main-bg-shadow);
	background: var(--main-bg-color);
	margin-top: 10px;
	height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 94px);
}
// 44px-分页 48px-筛选 36px-表格头
.vone-vxe-table {
  :deep(.vxe-table--body-wrapper) {
    overflow-y: auto;
  }
}
.tableHeight {
 height: calc(100vh - #{$main-margin} - #{$main-margin} - #{$main-padding} - #{$main-padding} - #{$nav-top-height} - 40px - 40px  - 114px);
}
:deep(.el-tree-node__expand-icon) {
  color: #777F8E;
}
</style>
