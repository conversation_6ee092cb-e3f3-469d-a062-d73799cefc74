<template>
  <!-- 详情 -->
  <div class="drawerBox">
    <el-dialog top="5vh" v-model="visible" width="1000px" :before-close="onClose" :close-on-click-modal="false">
      <div slot="title">
        <div>
          {{ title }}【
          <span class="drawerCode">
            {{ fixedForm.code }}
          </span>
          】
          <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />
          <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
        </div>
      </div>

      <div>
        <el-form ref="fixedForm" :model="fixedForm" :disabled="infoDisabled" label-position="top">
          <div class="basicHeader">
            <el-skeleton :loading="drawerLoading" style="width: 100%" animated>
              <template slot="template">
                <el-skeleton-item variant="p" style="width: 30%; margin-bottom: 14px" />
                <div style="display: flex; align-items: center; justify-content: space-between">
                  <div v-for="index in 5" :key="index" style="flex: 1; display: flex; align-items: center">
                    <el-skeleton-item variant="image" style="width: 30px; height: 30px" />
                    <div style="width: 190px; margin-left: 10px">
                      <el-skeleton-item variant="p" style="width: 50%; margin-bottom: 6px" />
                      <el-skeleton-item variant="p" style="width: 70%" />
                    </div>
                  </div>
                </div>
              </template>
            </el-skeleton>

            <div v-if="!drawerLoading">
              <el-row v-for="item in fixedProperty.filter(r => r.key == 'name')" :key="item.id" class="name-row">
                <el-col :span="24">
                  <!-- 输入框 -->
                  <el-input v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                </el-col>
              </el-row>

              <div class="basica-form">
                <div v-for="item in fixedProperty.filter(r => r.key != 'name')" :key="item.id" class="fixedItem">
                  <el-form-item :label="item.key != 'name' ? item.name : null" :prop="item.key">
                    <!-- 图标 -->

                    <span v-if="item.key == 'typeCode'">
                      <span v-if="fixedForm.echoMap && fixedForm.echoMap[item.key]">
                        <i
                          :style="{ color: fixedForm.echoMap[item.key].color }"
                          :class="['iconfont', `${fixedForm.echoMap[item.key].icon}`]"
                        />
                      </span>
                      <span v-else>
                        <svg class="icon" aria-hidden="true" style="font-size: 24px">
                          <use :xlink:href="`#el-icon-icon-yixiang`" />
                        </svg>
                      </span>
                    </span>

                    <span v-else-if="item.key == 'handleBy'">
                      <span v-if="fixedForm.echoMap && fixedForm.echoMap[item.key]">
                        <vone-user-avatar
                          :avatar-path="fixedForm.echoMap[item.key].avatarPath"
                          :avatar-type="true"
                          :show-name="false"
                          height="24px"
                          width="24px"
                        />
                      </span>
                      <span v-else>
                        <i class="iconfont el-icon-icon-light-avatar" style="font-size: 24px" />
                      </span>
                    </span>
                    <span v-else>
                      <svg class="icon" aria-hidden="true" style="font-size: 24px">
                        <use :xlink:href="`#${iconMap[item.key]}`" />
                      </svg>
                    </span>
                    <!-- 标签 -->
                    <span v-if="item.type == 'SELECT' && item.key == 'tagId'" style="padding-left: 12px">
                      <el-tooltip
                        v-if="fixedForm[item.key].length > 1"
                        :content="fixedForm[item.key].join(',')"
                        placement="top"
                      >
                        <span v-for="(i, index) in fixedForm[item.key].slice(0, 1)" :key="index">
                          <span>
                            <el-tag type="success" class="ml-10">{{ i }}</el-tag>
                          </span>

                          &nbsp; &nbsp;
                          <el-tag v-if="fixedForm[item.key].length > 1" type="success">{{
                            ` + ${fixedForm[item.key].length - 1}`
                          }}</el-tag>
                        </span>
                      </el-tooltip>

                      <span v-else>
                        <span v-for="(j, index) in fixedForm[item.key]" :key="index">
                          <span>
                            <el-tag type="success" class="ml-10">{{ j }}</el-tag>
                          </span>
                        </span>
                      </span>
                    </span>
                    <!-- 时间 -->
                    <span v-else-if="item.type == 'DATE'" style="padding-left: 12px" class="dataPicker">
                      {{ fixedForm[item.key] && dayjs( fixedForm[item.key]).format('YYYY-MM-DD HH:mm') }}
                    </span>
                    <!-- 状态 -->
                    <span
                      v-else-if="item.key == 'stateCode'"
                      style="
                        max-width: 190px;
                        padding-left: 12px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{
                        fixedForm[item.key] && fixedForm.echoMap[item.key] ? fixedForm.echoMap[item.key].name : '待处理'
                      }}
                    </span>
                    <span v-else style="padding-left: 12px">
                      <span v-if="fixedForm.echoMap[item.key]">
                        {{ fixedForm.echoMap[item.key].name }}
                      </span>
                    </span>
                  </el-form-item>
                </div>

                <!-- </el-col> -->
              </div>
            </div>
          </div>
        </el-form>
        <el-row>
          <el-col :span="16" class="centerBox">
            <el-tabs v-model="tabActive" class="vone-tab-line" @tab-click="handleClick($event, leftTabs)">
              <el-tab-pane label="基本信息" class="contentBox" name="basic">
                <div class="pContent">
                  <el-skeleton v-if="infoLoading" style="width: 100%" :loading="infoLoading" animated :count="8">
                    <template slot="template">
                      <div style="padding: 14px">
                        <el-row type="flex" style="margin-top: 6px">
                          <el-skeleton-item variant="p" style="width: 50%; margin-right: 16px" />
                          <el-skeleton-item variant="p" style="width: 50%" />
                        </el-row>
                      </div>
                    </template>
                  </el-skeleton>
                  <div v-else class="centerBasic">
                    <el-form ref="otherForm" :model="otherForm" label-position="top" :disabled="infoDisabled">
                      <el-row class="upDetail">
                        <el-col v-for="item in basicProperty" :key="item.id" :span="24">
                          <el-form-item :label="item.name" :prop="item.key">
                            <!-- 文本编辑器 -->
                            <span v-if="item.type == 'EDITOR'">
                              <vone-editor
                                v-if="otherForm[item.key]"
                                ref="editor"
                                v-model="otherForm[item.key]"
                                :preview="infoDisabled"
                              />

                              <span v-else>--</span>
                            </span>

                            <!-- 文件 -->
                            <span v-else-if="item.type == 'FILE'">
                              <vone-upload
                                ref="uploadFile"
                                :biz-type="fileMap[typeCode]"
                                :files-data="otherForm.files"
                              />
                            </span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>

                    <el-divider />
                    <h4>基本属性</h4>
                    <vone-desc :column="2">
                      <template v-for="item in customList" :key="item.id">
                        <vone-desc-item :label="`${item.name} :`">
                          <template>
                            <span v-if="customForm[item.key] && customForm.echoMap[item.key] && item.isBuilt">
                              <vone-user-avatar
                                v-if="item.type == 'USER'"
                                :avatar-path="customForm.echoMap[item.key].avatarPath"
                                :avatar-type="customForm.echoMap[item.key].avatarType"
                                :name="customForm.echoMap[item.key].name"
                                height="20px"
                                width="20px"
                              />

                              <span v-else-if="item.type == 'SELECT' && item.multiple">
                                {{ customForm.echoMap[item.key].map(r => r.name).join(',') }}
                              </span>
                              <span v-else>{{ customForm.echoMap[item.key].name }}</span>
                            </span>
                            <span v-else-if="customForm[item.key] && !item.isBuilt">
                              <span v-if="item.type == 'SELECT' && !item.multiple && item.options">
                                {{
                                  item.options.find(r => r.id == customForm[item.key])
                                    ? item.options.find(r => r.id == customForm[item.key]).name
                                    : customForm[item.key]
                                }}
                              </span>
                              <span v-else-if="item.type == 'SELECT' && item.multiple">
                                {{ customForm[item.key] }}
                              </span>
                              <!-- 人员 -->
                              <span
                                v-else-if="
                                  item.type == 'USER' ||
                                  (item.type == 'PROJECTUSER' && customForm.echoMap[item.key].length)
                                "
                              >
                                <vone-user-avatar
                                  v-for="user in customForm.echoMap[item.key]"
                                  :key="user.id"
                                  :avatar-path="user.avatarPath"
                                  :avatar-type="user.avatarType"
                                  :name="user.name"
                                  height="20px"
                                  width="20px"
                                />
                              </span>
                              <!-- 机构 -->
                              <span v-else-if="item.type == 'ORG'">
                                {{ customForm.echoMap[item.key].map(r => r.name).join(',') }}
                              </span>

                              <!-- 文件 -->
                              <vone-upload
                                v-else-if="item.type == 'FILE'"
                                ref="formUploadFile"
                                :files-data="customForm.echoMap[item.key]"
                                :biz-type="fileMap[typeCode]"
                                :class-name="'hidebtn'"
                              />

                              <!-- 关联 -->
                              <dataSelect
                                v-else-if="item.type == 'LINKED'"
                                text-info="info"
                                v-model:model="customForm[item.key]"
                                :config="item"
                              />

                              <!-- 引用 -->
                              <div v-else-if="item.type == 'QUOTE'">
                                <vone-user-avatar
                                  v-if="item.quoteType === 'user' && userMap[customForm[item.key]]"
                                  :avatar-path="userMap[customForm[item.key]].avatarPath"
                                  :avatar-type="true"
                                  height="22px"
                                  width="22px"
                                  :name="userMap[customForm[item.key]].name"
                                  :show-name="true"
                                />
                                <span v-else> {{ customForm[item.key] }} </span>
                              </div>
                              <!-- 超链接 -->
                              <el-tooltip
                                v-else-if="item.type == 'LINK'"
                                class="item"
                                effect="dark"
                                :content="customForm[item.key]"
                                placement="top-start"
                              >
                                <span class="file-name" @click="fileClick(customForm[item.key])">{{
                                  customForm[item.key]
                                }}</span>
                              </el-tooltip>
                              <span v-else>
                                {{ customForm[item.key] }}
                              </span>

                              <!-- {{ customForm.echoMap[item.key] }} -->
                            </span>

                            <span v-else>
                              {{ customForm[item.key] || '--' }}
                            </span>
                          </template>
                        </vone-desc-item>
                      </template>
                    </vone-desc>

                    <!-- <el-form ref="customForm" :model="customForm" label-position="top" :disabled="infoDisabled" class="custom" label-width="110px">
                      <el-row :gutter="24">
                        <el-col v-for="item in customList" :key="item.id" :span="12">
                          <el-form-item :label="`${item.name} :`" :prop="item.key" />

                        </el-col>
                      </el-row>
                    </el-form> -->
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane
                v-for="(tab, index) in leftTabs"
                :key="index"
                class="contentBox"
                :label="tab.label"
                :name="tab.name"
              >
                <component
                  :ref="tab.name"
                  :is="tab.name"
                  v-if="tab.active"
                  :type-code="typeCode"
                  :issue-id="fixedForm.id"
                  :issue-info="fixedForm"
                  :product-id="fixedForm.productId"
                  :tab-name="tab.label"
                  :showPopupForSplit="showPopupForSplit"
                  :isShowCreateBtn="isShowCreateBtn"
                  @initList="initList"
                  @add-child="$emit('add-child', $event)"
                />
              </el-tab-pane>
            </el-tabs>
          </el-col>

          <el-col v-loading="infoLoading" :span="8" class="rightBox">
            <el-tabs
              v-model="rightTabActive"
              class="vone-tab-line mintabline"
              @tab-click="handleClick($event, rightTabs)"
            >
              <el-tab-pane v-for="tab in rightTabs" :key="tab.name" :label="tab.label" :name="tab.name">
                <vone-comment
                  v-if="rightTabActive == 'comment' && tab.active"
                  height="calc(60vh - 20px)"
                  :source-id="commentId"
                  :source-type="typeCode"
                />
                <activeTab
                  v-if="rightTabActive == 'active' && tab.active"
                  :form-id="id"
                  :type="typeCode"
                  height="calc(60vh - 20px)"
                  :source-type="typeCode"
                  :source-id="commentId"
                  :view-type="'detail'"
                />
                <activityRecord
                  v-if="rightTabActive == 'activityRecord' && tab.active"
                  :form-id="id"
                  :type="typeCode"
                  :source-type="typeCode"
                  height="calc(60vh - 40px)"
                  :source-id="commentId"
                />
                <workTime
                  v-if="rightTabActive == 'workTime' && tab.active"
                  height="calc(60vh - 40px)"
                  :source-id="commentId"
                  :source-type="typeCode"
                  :project-id="fixedForm.projectId"
                  :rowTypeCode="rowTypeCode"
                />
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>

      <div slot="footer">
        <el-button @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// index.js 的相对路径
import index from './index.js' // 名字可以任取
export default index
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
