<template>
  <el-dialog :title="title" v-model:visible="dialogVisible" width="456px" :before-close="onClose" :close-on-click-modal="false">
    <el-form ref="viewForm" :model="viewForm" label-position="top" :rules="rules">
      <el-form-item label="视图名称" prop="name">
        <el-input v-model="viewForm.name" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">

      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveView">确定</el-button>

    </span>
  </el-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import { operationFilter } from '@/api/common'
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    viewData: {
      type: Object,
      default: null
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableFilterData: {
      type: Array,
      default: () => []
    },
    formJson: {
      type: Object,
      default: null
    },
    tableSearchKey: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isChangeDefault: false,
      viewForm: {
        isDefault: false
      },
      saveLoading: false,
      rules: { name: [{ required: true, message: '请输入视图名称', trigger: 'blur' }] }
    }
  },
  mounted() {
    if (this.viewData) {
      this.viewForm = cloneDeep(this.viewData)
    }
  },
  methods: {
    saveView() {
      if (!this.viewData) {
        const hasFilter = this.tableFilterData.filter(item => item.name == this.viewForm.name)
        if (hasFilter.length) {
          this.$message.warning('已存在相同的视图，名称为' + hasFilter[0].name)
          return
        }

        console.log(this.formJson, 'this.formJson')
        const params = {
          name: this.viewForm.name,
          search: JSON.stringify(this.formJson),
          tableCode: this.tableSearchKey,
          isDefault: true,
          isProtect: false,
          isPublic: false
        }
        this.saveLoading = true
        operationFilter(params, 'post').then(res => {
          this.saveLoading = false
          if (res.isSuccess) {
            this.$message.success(res.msg)
            this.$emit('success')
            this.dropdownName = res.data.name
            this.onClose()
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        this.editView()
      }
    },
    changeDefault() {
      this.isChangeDefault = true
    },
    async editView() {
      console.log(this.viewForm, 'viewFormviewForm')
      try {
        await this.$refs.viewForm.validate()
      } catch (e) {
        return
      }
      try {
        const hasFilter = this.tableFilterData.filter(item => item.name == this.viewForm.name)
        if (hasFilter.length && !this.isChangeDefault) {
          this.$message.warning('已存在相同的视图，名称为' + hasFilter[0].name)
          return
        }
        this.saveLoading = true
        // if (!this.viewForm?.isDefault) {
        //   this.viewForm.isDefault = false
        // }
        const { isSuccess, msg } = await operationFilter(
          this.viewForm, 'put'
        )
        this.saveLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('编辑成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    onClose() {
      this.$refs.viewForm.resetFields()
      this.$emit('update:viewData', null)
      this.isChangeDefault = false
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>
