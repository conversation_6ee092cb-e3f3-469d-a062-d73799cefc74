<template>
  <div>
    <el-dialog
      :append-to-body="true"
      width="35%"
      :model-value="visible"
      @update:model-value="$emit('update:visible', $event)"
      :before-close="onClose"
      :close-on-click-modal="false"
      <!-- v-on="$listeners" - Vue3: 事件监听器现在包含在 $attrs 中 -->
    >
      <span slot="title">
        <strong> 登记工时 </strong>
        <span :style="{ color: getColor(allFillTime) }"> ( {{ allFillTime }} / {{ standardTime }} ) </span>
      </span>
      <el-form ref="worktimeForm" :model="worktimeForm" label-position="top" :rules="worktimeFormRules">
        <!-- <div class="progress">
          <div class="progress-text">
            <span>已记录<span class="text">23</span>小时</span>
            <span class="operate">剩余<span class="text">23</span>小时</span>
          </div>

          <el-progress :percentage="60" :show-text="false" />
        </div> -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工时" required prop="duration">
              <el-input v-model="worktimeForm.duration" controls-position="right" :step="0.5" placeholder="请输入" />
              <!-- <el-input v-model="worktimeForm.duration" type="number" placeholder="请输入记录工时">
                <div slot="suffix">小时</div>
              </el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作项状态" required prop="stateCode">
              <el-select
                v-model="worktimeForm.stateCode"
                filterable
                placeholder="请选择工作项状态"
                clearable
                collapse-tags
              >
                <el-option v-for="item in stateCodeList" :key="item.id" :label="item.name" :value="item.stateCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="剩余工时" prop="projectId">
              <el-input-number v-model="worktimeForm.input" disabled placeholder="请输入剩余工时" controls-position="right" :step="0.5" :min="1" :max="maxtime" />
            </el-form-item> -->
            <el-form-item label="填报日期" prop="fillingTime">
              <el-date-picker
                v-model="worktimeForm.fillingTime"
                type="date"
                placeholder="请选择填报日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                :editable="false"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model.trim="worktimeForm.description" type="textarea" :rows="2" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <div slot="footer" style="text-align: right">
        <el-button @click="onClose">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="sureAdd">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addWorkingHoursInfo, getWorkingHoursConfig, getWorkingHoursDayAll } from '@/api/vone/manhour/index'
import { apiAlmFindNextNode } from '@/api/vone/project/issue'
import { getFlow } from '@/api/vone/base/work-flow'
import storage from 'store'
import dayjs from 'dayjs'
import _ from 'lodash'

export default {
  name: 'WorkDialog',
  emits: ['update:visible', 'success'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bizId: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    rowTypeCode: {
      // 需求中心/项目里,从列表数据取到的类型
      type: String,
      default: undefined
    },
    projectId: {
      type: String,
      default: undefined
    }
  },
  data() {
    var checkTime = (rule, value, callback) => {
      if (value === null || value === '' || value === undefined) {
        return callback(new Error('请输入工时'))
      }
      if (parseFloat(value).toString() == 'NaN') {
        callback(new Error('工时为数字'))
      }
      if (parseFloat(value) > 0 && parseFloat(value) <= this.maxtime) {
        callback()
      } else {
        callback(new Error(`工时不能为0且小于等于${this.maxtime}`))
      }
    }
    return {
      stateCodeList: [],
      fillCycle: 'week',
      pickerOptions: {
        disabledDate: this.disabledDate
      },
      worktimeForm: {
        filledBy: this.$store.state.user.user.id,
        bizId: this.bizId,
        type: this.type,
        stateCode: ''
      },
      saveLoading: false,
      worktimeFormRules: {
        duration: [{ validator: checkTime, trigger: ['blur', 'change'] }],
        stateCode: [{ required: true, message: '请选择工作项状态', trigger: ['blur', 'change'] }],
        fillingTime: [{ required: true, message: '请选择日期', trigger: ['blur', 'change'] }]
      },
      maxtime: 0,
      allFillTime: 0,
      standardTime: 0
    }
  },
  computed: {
    getColor() {
      return function (count) {
        if (count < this.standardTime) {
          return 'red' // √
        } else if (count == this.standardTime) {
          return 'green' // 当数量等于8时显示蓝色
        } else {
          return '#F27900' // 当数量大于8时显示绿色
        }
      }
    }
  },
  mounted() {
    this.getAllStatus()
    this.getWorkTimeInfo()
    this.getWorkingHoursConfFn()
  },
  methods: {
    // 查询全部工作流状态
    async getAllStatus() {
      let url = ''
      url = `/api/alm/alm/projectWorkflow/findProjectByProjectIdAndTypeClassifyAndTypeCode/${this.projectId}/${this.type}/${this.rowTypeCode}`
      const r = await apiAlmFindNextNode(url)
      const flowId = r.data.workflowId
      const res = await getFlow(flowId)
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data.workflowNodes
    },
    async getWorkTimeInfo() {
      const userInfo = storage.get('user')
      const dayDate = dayjs().format('YYYY-MM-DD')
      const res = await getWorkingHoursDayAll(dayDate, dayDate, [userInfo.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      // const aa = res.data.userHistory.map(r => r.duration)
      const allTime = _.sum(res.data?.userHistory?.map(r => r.duration))
      this.allFillTime = allTime
    },
    async sureAdd() {
      this.saveLoading = true
      try {
        await this.$refs.worktimeForm.validate()
        this.worktimeForm.stateCode = this.worktimeForm.stateCode
        const { isSuccess, msg } = await addWorkingHoursInfo({
          ...this.worktimeForm
        })
        if (!isSuccess) {
          this.$message.warning(msg)
          this.saveLoading = false
          return
        }
        this.saveLoading = false
        this.$message.success('提交成功')
        this.onClose()
        this.$emit('success')
      } catch (error) {
        this.saveLoading = false
        return
      }
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.worktimeForm.resetFields()
    },
    async getWorkingHoursConfFn() {
      const res = await getWorkingHoursConfig()
      res.data.map(e => {
        if (e.key == 'MAX_HOURS_OF_EVERY_DAY') {
          this.maxtime = e.value
        }
        if (e.key == 'NORM_HOURS_OF_EVERY_DAY') {
          this.standardTime = e.value
        }
        if (e.key == 'FILLABLE_PERIOD') {
          this.fillCycle = e.value
        }
      })
    },
    disabledDate(time) {
      const today = new Date()
      const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      const oneQuarterAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)

      if (this.fillCycle == 'week') {
        return time.getTime() < oneWeekAgo.getTime() || time.getTime() > today.getTime()
      } else if (this.fillCycle == 'month') {
        return time.getTime() < oneMonthAgo.getTime() || time.getTime() > today.getTime()
      } else if (this.fillCycle == 'quarter') {
        return time.getTime() < oneQuarterAgo.getTime() || time.getTime() > today.getTime()
      } else {
        return time.getTime() > today.getTime()
      }

      // 禁止选择今天之后的日期和一周以前的日期
    }
  }
}
</script>
<style lang="scss" scoped>
.progress {
  padding: 10px;
  border: 1px solid #e5e5e5;
  .progress-text {
    padding: 10px 0;
    .text {
      font-weight: 600;
    }
  }
  .operate {
    float: right;
  }
}
:deep(.el-input-number) {
  width: 100%;
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
  .el-input {
    &::after {
      content: '小时';
      position: absolute;
      right: 16px;
      line-height: 32px;
    }
  }
  .el-input__inner {
    text-align: left;
  }
}
:deep(.el-button + .el-button) {
  margin-left: 12px;
}
</style>
