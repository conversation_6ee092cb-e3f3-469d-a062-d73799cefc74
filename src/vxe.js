import XEUtils from 'xe-utils'
import VXETablePluginElement from 'vxe-table-plugin-element'
import 'vxe-table-plugin-element/dist/style.css'
// import FilterSelect from '@/components/CustomVxeFilter'
import {
  // 全局对象
  // VXETable,
  // 功能模块
  // Filter,
  // Edit,
  // Validator,
  // Column,
  // Colgroup,
  // Input,
  // Tooltip,
  // Select,
  // Option,
  // Textarea,
  // Pulldown,
  // 表格
  // Table
  // Menu,
  // Export,
  // Keyboard,
  // 可选组件
  // Icon,
  // Grid,
  // Toolbar,
  // Pager,
  // Checkbox,
  // CheckboxGroup,
  // Radio,
  // RadioGroup,
  // RadioButton,
  // Button,
  // Modal,
  // Form,
  // FormItem,
  // FormGather,
  // Optgroup,
  // Switch,
  // List,
} from 'vxe-table'
import zhCN from 'vxe-table/lib/locale/lang/zh-CN'
import { h } from 'vue'

VXETable.use(VXETablePluginElement)
// 按需加载的方式默认是不带国际化的，自定义国际化需要自行解析占位符 '{0}'，例如：
VXETable.config({
  i18n: (key, args) => XEUtils.toFormatString(XEUtils.get(zhCN, key), args),
  icon: {
    TABLE_FILTER_NONE: 'iconfont el-icon-application-filter-s',
    TABLE_FILTER_MATCH: 'iconfont el-icon-application-filter-s'
  },
  zIndex: 9999, // 想多高就设置多高
  table: {
    minHeight: 10,
    resizable: true,
    loadingConfig: {
      icon: 'iconfont el-icon-vone_loading',
      text: '正在加载...'
    },
    sortConfig: {
      remote: true,
      trigger: 'cell',
      orders: ['asc', 'desc', null]
    },
    rowConfig: {
      isHover: true
    },
    scrollX: {
      enabled: true // 是否默认开启横向虚拟滚动
    },
    scrollY: {
      enabled: true // 是否默认开启纵向虚拟滚动
    }
  }
})
// 表格功能
// app.component(FilterSelect.name, FilterSelect)

export default function setupVXETable(app) {
  app.use(Edit)
    .use(Filter)
    .use(Validator)
    .use(Column)
    .use(Colgroup)
    .use(Input)
    .use(Tooltip)
    .use(Select)
    .use(Option)
    .use(Textarea)
    .use(Pulldown)
    .use(Table)
}
// .use(Menu)
// .use(Export)
// .use(Keyboard)
// 可选组件
// .use(Icon)
// .use(Grid)
// .use(Toolbar)
// .use(Pager)
// .use(Checkbox)
// .use(CheckboxGroup)
// .use(Radio)
// .use(RadioGroup)
// .use(RadioButton)
// .use(Button)
// .use(Modal)
// .use(Form)
// .use(FormItem)
// .use(FormGather)
// .use(Optgroup)
// .use(Switch)
// .use(List)

// VXETable.renderer.add('FilterSelect', {
//   isFooter: false,
//   // 筛选模板
//   renderFilter(h, renderOpts, params) {
//     return <filter-select params={ params } searchForm={renderOpts.props.searchForm}></filter-select>
//   },
//   // 重置数据方法
//   filterResetMethod({ options }) {
//     options.forEach(option => {
//       option.data.value = []
//     })
//   },
//   // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
//   filterRecoverMethod({ option }) {
//     option.data.value = []
//   },
//   // 筛选方法
//   filterMethod({ option, row, column }) {
//     return false
//   }
// })
VXETable.renderer.add('empty', {
  // 空内容模板
  renderEmpty() {
    return h('div', { class: 'empty_wrap' }, [
      h('svg-icon', { class: 'empty_pic', 'icon-class': 'empty-file' }),
      h('span', { class: 'empty_desc' }, '暂无内容')
    ])
  }
})
