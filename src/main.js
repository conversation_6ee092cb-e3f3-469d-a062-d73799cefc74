import { createApp } from 'vue'
import setupElementPlus from './element' // element配置文件
import setupVXETable from './vxe' // vxe-table配置文件
import setupComponents from '@/components' // 内部封装的组件
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import 'element-plus/dist/index.css' // Element Plus样式
// import variables from '@/styles/variables.scss'
import * as variables from '@/styles/variables.scss'
import '@/styles/index.scss' // global css
import store from './store'
import router from './router'
import App from './App.vue'
import checkOpermission from '@/utils/operation-permission' // 权限
import bus from '@/utils/bus'
import setupIcons from '@/icons'
import '@/assets/iconfont/iconfont.css' // 阿里图标库
import '@/assets/iconfont/iconfont.js'
import mixins from './mixin/vxe-mixin'
import '@/assets/fabfont/iconfont.css'
import * as directives from '@/directives'

const app = createApp(App)

// 注册全局混入
app.mixin(mixins)

// 注册全局指令
Object.keys(directives).forEach(key => {
  app.directive(key, directives[key])
})

import uploader from 'vue-simple-uploader' // 上传组件
app.use(uploader)

import VueClipboard from 'vue-clipboard2' // 复制组件
VueClipboard.config.autoSetContainer = true
app.use(VueClipboard)

import Swiper from 'swiper/bundle' // 轮播图组件
import 'swiper/css/bundle'
app.config.globalProperties.Swiper = Swiper

import * as dayjs from 'dayjs' // 日期插件
import 'dayjs/locale/zh-cn'
import * as relativeTime from 'dayjs/plugin/relativeTime'
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
app.config.globalProperties.dayjs = dayjs
app.config.globalProperties.$filters = {
  fromNow(val) {
    if (!val) return ''
    return dayjs(val).fromNow()
  }
}
// 仅限dev环境使用
// if (process.env.NODE_ENV !== 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }
// 注释掉mavon-editor，使用新的编辑器
// import mavonEditor from 'mavon-editor' // 编辑器
// import 'mavon-editor/dist/css/index.css'
// app.use(mavonEditor)
import JsonViewer from 'vue-json-viewer'
app.use(JsonViewer)

// 全屏插件
import fullscreen from 'vue-fullscreen'
app.use(fullscreen)
import '@/utils/draggable'

import VueFab from 'vue-float-action-button'
app.use(VueFab)

// 全局属性配置
app.config.globalProperties.$axiosList = [] // 拦截请求
app.config.globalProperties.$permission = checkOpermission
app.config.globalProperties.$tableHeight = variables.tableHeight
app.config.globalProperties.$reduceTableHeight = `100vh - 180px`
app.config.globalProperties.$bus = bus

// 配置Element Plus
setupElementPlus(app)

// 配置VXE Table
setupVXETable(app)

// 注册全局组件
setupComponents(app)

// 配置图标
setupIcons(app)

// 使用路由和状态管理
app.use(store)
app.use(router)

// 挂载应用
app.mount('#app')
