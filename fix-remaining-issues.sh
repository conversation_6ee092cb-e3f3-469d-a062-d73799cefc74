#!/bin/bash

# Vue3 剩余问题修复脚本
# 用于修复Vue2到Vue3迁移过程中的剩余问题

echo "🚀 开始修复Vue3剩余问题..."

# 1. 修复所有 ~@ 语法为 @
echo "📝 修复SCSS导入语法..."
find src -name "*.vue" -exec sed -i '' 's/~@/@/g' {} \;

# 2. 修复v-model语法
echo "📝 修复v-model语法..."
find src -name "*.vue" -exec sed -i '' 's/v-model:model=/:model=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:extra=/:extra=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:default-fileds=/:default-fileds=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:add-name=/:add-name=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:is-edit=/:is-edit=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:edit-id=/:edit-id=/g' {} \;
find src -name "*.vue" -exec sed -i '' 's/v-model:row-data=/:row-data=/g' {} \;

# 3. 修复HTML注释语法错误
echo "📝 修复HTML注释语法..."
find src -name "*.vue" -exec sed -i '' 's/ <!-- v-on[^>]*-->//g' {} \;

# 4. 修复扩展运算符语法错误
echo "📝 修复扩展运算符语法..."
find src -name "*.vue" -exec sed -i '' 's/{ \.\.\. /{ .../g' {} \;

# 5. 删除备份文件
echo "🗑️  删除备份文件..."
find . -name "*.bak" -not -path "./node_modules/*" -delete

# 6. 重命名有空格的文件名
echo "📝 修复文件名..."
find src -name "* *.vue" -exec bash -c 'mv "$1" "${1// /-}"' _ {} \;

# 7. 修复常见的template语法问题
echo "📝 修复template语法问题..."
# 修复多余的template标签
find src -name "*.vue" -exec sed -i '' '/<template>$/,/<\/template>$/{
  /<template>$/!{
    /<\/template>$/!{
      s/<template>//g
      s/<\/template>//g
    }
  }
}' {} \;

echo "✅ Vue3剩余问题修复完成！"
echo ""
echo "📊 修复总结："
echo "✅ SCSS导入语法: ~@ → @"
echo "✅ v-model语法: v-model:prop → :prop"
echo "✅ HTML注释语法错误"
echo "✅ 扩展运算符语法"
echo "✅ 备份文件清理"
echo "✅ 文件名规范化"
echo "✅ Template语法问题"
echo ""
echo "🔄 请重启开发服务器以查看效果"
